"""
Database models and utilities for the enhanced proctoring system
"""
import json
import uuid
from datetime import datetime, timezone, timedelta
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, ForeignKey, Float
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from db_manager import engine, SessionLocal
Base = declarative_base()

def get_db_session():
    """Get database session"""
    return SessionLocal()

# User model (copied to avoid circular import)
class User(Base):
    __tablename__ = 'users'

    id = Column(Integer, primary_key=True, autoincrement=True)
    email = Column(String(255), unique=True, nullable=False, index=True)
    password_hash = Column(String(255), nullable=False)
    name = Column(String(255), nullable=False)
    role = Column(String(50), nullable=False, default='student')
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    last_login = Column(DateTime, nullable=True)
    is_active = Column(Boolean, default=True)

    def __repr__(self):
        return f"<User(email='{self.email}', name='{self.name}', role='{self.role}')>"

class Test(Base):
    __tablename__ = 'tests'

    id = Column(Integer, primary_key=True, autoincrement=True)
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    duration_minutes = Column(Integer, nullable=False, default=60)
    questions_data = Column(Text, nullable=False)  # JSON string of questions
    settings = Column(Text, nullable=True)  # JSON string of test settings
    created_by = Column(Integer, ForeignKey('users.id'), nullable=False)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    is_active = Column(Boolean, default=True)
    
    def get_questions(self):
        """Parse questions JSON"""
        try:
            return json.loads(self.questions_data) if self.questions_data else []
        except json.JSONDecodeError:
            return []

    def set_questions(self, questions_list):
        """Set questions as JSON"""
        self.questions_data = json.dumps(questions_list)
    
    def get_settings(self):
        """Parse settings JSON"""
        try:
            return json.loads(self.settings) if self.settings else {}
        except json.JSONDecodeError:
            return {}
    
    def set_settings(self, settings_dict):
        """Set settings as JSON"""
        self.settings = json.dumps(settings_dict)
    
    def __repr__(self):
        return f"<Test(title='{self.title}', duration={self.duration_minutes}min)>"


class ProctorSession(Base):
    __tablename__ = 'proctor_sessions'

    id = Column(Integer, primary_key=True, autoincrement=True)
    test_id = Column(Integer, ForeignKey('tests.id'), nullable=False)
    student_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    submission_id = Column(Integer, nullable=True)  # Legacy linkage to StudentSubmission
    session_token = Column(String(255), unique=True, nullable=False, index=True)
    status = Column(String(50), nullable=False, default='created')  # created, active, completed, terminated
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    total_duration = Column(Integer, nullable=True)  # in seconds
    recording_path = Column(String(500), nullable=True)

    # Summary statistics
    total_violations = Column(Integer, default=0)
    face_detection_score = Column(Float, default=0.0)
    attention_score = Column(Float, default=0.0)
    
    @staticmethod
    def generate_token():
        """Generate unique session token"""
        return str(uuid.uuid4())
    
    def start_session(self):
        """Mark session as started"""
        self.status = 'active'
        self.started_at = datetime.now(timezone.utc)
    
    def complete_session(self):
        """Mark session as completed"""
        self.status = 'completed'
        self.completed_at = datetime.now(timezone.utc)
        if self.started_at:
            # Ensure both datetimes are timezone-aware
            started_at = self.started_at
            if started_at.tzinfo is None:
                started_at = started_at.replace(tzinfo=timezone.utc)
            self.total_duration = int((self.completed_at - started_at).total_seconds())
    
    def __repr__(self):
        return f"<ProctorSession(test_id={self.test_id}, student_id={self.student_id}, status='{self.status}')>"


class MonitoringEvent(Base):
    __tablename__ = 'monitoring_events'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(Integer, ForeignKey('proctor_sessions.id'), nullable=False)
    event_type = Column(String(100), nullable=False)  # face_lost, multiple_faces, tab_switch, fullscreen_exit, etc.
    event_data = Column(Text, nullable=True)  # JSON string with event details
    timestamp = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    severity = Column(String(20), default='medium')  # low, medium, high, critical
    
    def get_event_data(self):
        """Parse event data JSON"""
        try:
            return json.loads(self.event_data) if self.event_data else {}
        except json.JSONDecodeError:
            return {}
    
    def set_event_data(self, data_dict):
        """Set event data as JSON"""
        self.event_data = json.dumps(data_dict)
    
    def __repr__(self):
        return f"<MonitoringEvent(session_id={self.session_id}, type='{self.event_type}', severity='{self.severity}')>"


class SessionMetrics(Base):
    __tablename__ = 'session_metrics'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(Integer, ForeignKey('proctor_sessions.id'), nullable=False)
    timestamp = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    
    # Face detection metrics
    face_detected = Column(Boolean, default=False)
    face_count = Column(Integer, default=0)
    face_confidence = Column(Float, default=0.0)
    
    # Attention metrics
    looking_away = Column(Boolean, default=False)
    attention_score = Column(Float, default=0.0)
    
    # Browser metrics
    tab_switches = Column(Integer, default=0)
    fullscreen_exits = Column(Integer, default=0)
    
    def __repr__(self):
        return f"<SessionMetrics(session_id={self.session_id}, timestamp={self.timestamp})>"


class TestAssignment(Base):
    __tablename__ = 'test_assignments'

    id = Column(Integer, primary_key=True)
    test_id = Column(Integer, ForeignKey('tests.id'), nullable=False)
    student_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    assigned_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))
    deadline = Column(DateTime(timezone=True), nullable=False)
    status = Column(String(50), default='assigned')  # assigned, started, completed, expired, not_attempted
    access_token = Column(String(255), unique=True, nullable=False)
    attempts_allowed = Column(Integer, default=1)
    attempts_used = Column(Integer, default=0)

    def __repr__(self):
        return f"<TestAssignment(test_id={self.test_id}, student_id={self.student_id}, status={self.status})>"


class TestResult(Base):
    __tablename__ = 'test_results'

    id = Column(Integer, primary_key=True)
    assignment_id = Column(Integer, ForeignKey('test_assignments.id'), nullable=False)
    student_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    test_id = Column(Integer, ForeignKey('tests.id'), nullable=False)
    started_at = Column(DateTime(timezone=True), nullable=False)
    completed_at = Column(DateTime(timezone=True))
    answers_data = Column(Text)  # JSON string of student answers
    score = Column(Float, default=0.0)
    max_score = Column(Float, default=0.0)
    percentage = Column(Float, default=0.0)
    status = Column(String(50), default='in_progress')  # in_progress, completed, auto_submitted
    time_taken = Column(Integer)  # in seconds
    admin_remarks = Column(Text)
    admin_score = Column(Float)  # Manual score given by admin
    graded_at = Column(DateTime(timezone=True))
    graded_by = Column(String(255))

    def __repr__(self):
        return f"<TestResult(student_id={self.student_id}, test_id={self.test_id}, score={self.score})>"

    def get_answers(self):
        """Parse answers JSON"""
        try:
            return json.loads(self.answers_data) if self.answers_data else {}
        except json.JSONDecodeError:
            return {}

    def set_answers(self, answers_dict):
        """Set answers as JSON"""
        self.answers_data = json.dumps(answers_dict)


# Database utility functions
def create_test(title, description, duration_minutes, questions, settings, created_by):
    """Create a new test"""
    db = get_db_session()
    try:
        test = Test(
            title=title,
            description=description,
            duration_minutes=duration_minutes,
            created_by=created_by
        )
        test.set_questions(questions)
        test.set_settings(settings)

        db.add(test)
        db.commit()

        # Refresh the test object to get the ID
        db.refresh(test)
        test_id = test.id

        # Return success with test ID
        return True, test_id
    except Exception as e:
        db.rollback()
        return False, str(e)
    finally:
        db.close()


def create_proctor_session(test_id, student_id, submission_id=None):
    """Create a new proctoring session"""
    db = get_db_session()
    try:
        session = ProctorSession(
            test_id=test_id,
            student_id=student_id,
            submission_id=submission_id,
            session_token=ProctorSession.generate_token()
        )
        db.add(session)
        db.commit()
        db.refresh(session)
        session_id = session.id
        session_token = session.session_token
        return_session = ProctorSession(
            id=session_id,
            test_id=session.test_id,
            student_id=session.student_id,
            submission_id=session.submission_id,
            session_token=session.session_token,
            status=session.status,
            created_at=session.created_at,
            started_at=session.started_at,
            completed_at=session.completed_at,
            total_duration=session.total_duration,
            recording_path=session.recording_path,
            total_violations=session.total_violations,
            face_detection_score=session.face_detection_score,
            attention_score=session.attention_score
        )
        return return_session
    finally:
        db.close()

# Migration logic for SQLite: ensure submission_id column exists
def migrate_proctor_sessions_table():
    from sqlalchemy import inspect
    db = get_db_session()
    inspector = inspect(db.bind)
    columns = [col['name'] for col in inspector.get_columns('proctor_sessions')]
    if 'submission_id' not in columns:
        from sqlalchemy import text
        db.execute(text('ALTER TABLE proctor_sessions ADD COLUMN submission_id INTEGER'))
        db.commit()
    db.close()


def log_monitoring_event(session_id, event_type, event_data=None, severity='medium'):
    """Log a monitoring event"""
    db = get_db_session()
    try:
        event = MonitoringEvent(
            session_id=session_id,
            event_type=event_type,
            severity=severity
        )
        if event_data:
            event.set_event_data(event_data)
        
        db.add(event)
        db.commit()
        return event
    except Exception as e:
        db.rollback()
        raise e
    finally:
        db.close()


def record_session_metrics(session_id, metrics_data):
    """Record session metrics"""
    db = get_db_session()
    try:
        metrics = SessionMetrics(
            session_id=session_id,
            **metrics_data
        )
        
        db.add(metrics)
        db.commit()
        return metrics
    except Exception as e:
        db.rollback()
        raise e
    finally:
        db.close()


def get_session_by_token(token):
    """Get session by token"""
    db = get_db_session()
    try:
        return db.query(ProctorSession).filter(ProctorSession.session_token == token).first()
    finally:
        db.close()


def get_active_sessions():
    """Get all active sessions"""
    db = get_db_session()
    try:
        return db.query(ProctorSession).filter(ProctorSession.status == 'active').all()
    finally:
        db.close()


def get_tests_by_admin(admin_email):
    """Get all tests created by an admin"""
    db = get_db_session()
    try:
        # First get the admin user ID from email
        admin_user = db.query(User).filter(User.email == admin_email).first()
        if not admin_user:
            return []

        return db.query(Test).filter(Test.created_by == admin_user.id, Test.is_active == True).all()
    finally:
        db.close()


def assign_test_to_student(test_id, student_id, deadline_hours=24, attempts_allowed=1):
    """Assign a test to a student with deadline"""
    import uuid

    db = get_db_session()
    try:
        # Check if assignment already exists
        existing = db.query(TestAssignment).filter(
            TestAssignment.test_id == test_id,
            TestAssignment.student_id == student_id,
            TestAssignment.status.in_(['assigned', 'started'])
        ).first()

        if existing:
            return False, "Test already assigned to this student"

        # Create new assignment
        deadline = datetime.now(timezone.utc) + timedelta(hours=deadline_hours)
        access_token = str(uuid.uuid4())

        assignment = TestAssignment(
            test_id=test_id,
            student_id=student_id,
            deadline=deadline,
            access_token=access_token,
            attempts_allowed=attempts_allowed
        )

        db.add(assignment)
        db.commit()
        db.refresh(assignment)

        return True, assignment.access_token

    except Exception as e:
        db.rollback()
        return False, str(e)
    finally:
        db.close()


def get_student_assignments(student_id):
    """Get all test assignments for a student"""
    db = get_db_session()
    try:
        assignments = db.query(TestAssignment).filter(
            TestAssignment.student_id == student_id
        ).order_by(TestAssignment.assigned_at.desc()).all()

        # Update expired assignments
        current_time = datetime.now(timezone.utc)
        for assignment in assignments:
            # Handle timezone-naive datetime comparison
            assignment_deadline = assignment.deadline
            if assignment_deadline.tzinfo is None:
                assignment_deadline = assignment_deadline.replace(tzinfo=timezone.utc)

            if assignment_deadline < current_time and assignment.status == 'assigned':
                assignment.status = 'expired'

        db.commit()

        # Refresh all assignments to ensure they're fully loaded
        for assignment in assignments:
            db.refresh(assignment)

        # Create detached copies with all attributes loaded
        detached_assignments = []
        for assignment in assignments:
            # Access all attributes to load them before detaching
            _ = assignment.id
            _ = assignment.test_id
            _ = assignment.student_id
            _ = assignment.deadline
            _ = assignment.status
            _ = assignment.assigned_at
            _ = assignment.access_token
            _ = assignment.attempts_used
            _ = assignment.attempts_allowed
            detached_assignments.append(assignment)

        return detached_assignments
    finally:
        db.close()


def start_test_attempt(access_token, student_id):
    """Start a test attempt using access token"""
    db = get_db_session()
    try:
        assignment = db.query(TestAssignment).filter(
            TestAssignment.access_token == access_token,
            TestAssignment.student_id == student_id
        ).first()

        if not assignment:
            return False, "Invalid access token"

        current_time = datetime.now(timezone.utc)

        # Check if expired (handle timezone-naive datetime)
        assignment_deadline = assignment.deadline
        if assignment_deadline.tzinfo is None:
            assignment_deadline = assignment_deadline.replace(tzinfo=timezone.utc)

        if assignment_deadline < current_time:
            assignment.status = 'expired'
            db.commit()
            return False, "Test deadline has passed"

        # Check attempts
        if assignment.attempts_used >= assignment.attempts_allowed:
            return False, "Maximum attempts exceeded"

        # Check if already completed
        if assignment.status == 'completed':
            return False, "Test already completed"

        # Start the test
        assignment.status = 'started'
        assignment.attempts_used += 1

        # Create test result record
        test_result = TestResult(
            assignment_id=assignment.id,
            student_id=student_id,
            test_id=assignment.test_id,
            started_at=current_time
        )

        db.add(test_result)
        db.commit()
        db.refresh(test_result)

        return True, test_result.id

    except Exception as e:
        db.rollback()
        return False, str(e)
    finally:
        db.close()


def submit_test_result(result_id, answers_dict, auto_submit=False):
    """Submit test answers and calculate score"""
    db = get_db_session()
    try:
        result = db.query(TestResult).filter(TestResult.id == result_id).first()
        if not result:
            return False, "Test result not found"

        # Get test and questions
        test = db.query(Test).filter(Test.id == result.test_id).first()
        if not test:
            return False, "Test not found"

        questions = test.get_questions()
        if not questions:
            return False, "No questions found"

        # Calculate score
        total_score = 0
        max_score = 0
        correct_answers = 0

        for i, question in enumerate(questions):
            question_key = f"q_{i}"
            max_score += question.get('points', 1)

            if question_key in answers_dict:
                user_answer = answers_dict[question_key]['answer']
                correct_answer = question.get('correct_answer')

                if user_answer == correct_answer:
                    total_score += question.get('points', 1)
                    correct_answers += 1

        percentage = (total_score / max_score * 100) if max_score > 0 else 0

        # Update result
        result.set_answers(answers_dict)
        result.score = total_score
        result.max_score = max_score
        result.percentage = percentage
        result.completed_at = datetime.now(timezone.utc)
        result.status = 'auto_submitted' if auto_submit else 'completed'

        # Handle timezone-naive datetime comparison for time calculation
        started_at = result.started_at
        if started_at.tzinfo is None:
            started_at = started_at.replace(tzinfo=timezone.utc)

        result.time_taken = int((result.completed_at - started_at).total_seconds())

        # Update assignment status
        assignment = db.query(TestAssignment).filter(TestAssignment.id == result.assignment_id).first()
        if assignment:
            assignment.status = 'completed'

        db.commit()

        return True, {
            'score': total_score,
            'max_score': max_score,
            'percentage': percentage,
            'correct_answers': correct_answers,
            'total_questions': len(questions)
        }

    except Exception as e:
        db.rollback()
        return False, str(e)
    finally:
        db.close()


def get_sessions_by_admin(admin_id):
    """Get all sessions created by a specific admin"""
    db = get_db_session()
    try:
        # Get tests created by this admin
        admin_tests = db.query(Test).filter(Test.created_by == admin_id).all()
        test_ids = [test.id for test in admin_tests]

        if not test_ids:
            return []

        # Get sessions for these tests
        sessions = db.query(ProctorSession).filter(ProctorSession.test_id.in_(test_ids)).all()
        return sessions
    except Exception as e:
        print(f"Error getting sessions by admin: {e}")
        return []
    finally:
        db.close()


def get_completed_sessions_today(admin_id):
    """Get count of sessions completed today by admin"""
    from datetime import date
    db = get_db_session()
    try:
        admin_tests = db.query(Test).filter(Test.created_by == admin_id).all()
        test_ids = [test.id for test in admin_tests]

        if not test_ids:
            return 0

        today = date.today()
        completed_today = db.query(ProctorSession).filter(
            ProctorSession.test_id.in_(test_ids),
            ProctorSession.status == 'completed',
            db.func.date(ProctorSession.completed_at) == today
        ).count()

        return completed_today
    except Exception as e:
        print(f"Error getting completed sessions today: {e}")
        return 0
    finally:
        db.close()


def get_session_violations_summary(session_id):
    """Aggregate MonitoringEvent counts per event_type and severity for a session."""
    db = get_db_session()
    try:
        events = db.query(MonitoringEvent).filter(MonitoringEvent.session_id == session_id).all()
        summary = {}
        for event in events:
            etype = event.event_type
            sev = event.severity
            if etype not in summary:
                summary[etype] = {'count': 0, 'severity': {}}
            summary[etype]['count'] += 1
            if sev not in summary[etype]['severity']:
                summary[etype]['severity'][sev] = 0
            summary[etype]['severity'][sev] += 1
        return summary
    finally:
        db.close()
