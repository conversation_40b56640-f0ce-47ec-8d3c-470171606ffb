"""
PROCTORING SYSTEM TROUBLESHOOTING & IMPLEMENTATION GUIDE
========================================================

Comprehensive guide for diagnosing and fixing proctoring system issues
"""

import subprocess
import sys
import os
from pathlib import Path

class ProctoringTroubleshooter:
    """Comprehensive troubleshooting system for proctoring issues"""
    
    def __init__(self):
        self.issues_found = []
        self.fixes_applied = []
    
    def run_full_diagnosis(self):
        """Run complete system diagnosis"""
        print("🔍 PROCTORING SYSTEM DIAGNOSIS")
        print("=" * 50)
        
        # Check system requirements
        self._check_system_requirements()
        
        # Check file integrity
        self._check_file_integrity()
        
        # Check database status
        self._check_database_status()
        
        # Check browser compatibility
        self._check_browser_compatibility()
        
        # Generate fix recommendations
        self._generate_fix_recommendations()
        
        return self.issues_found, self.fixes_applied
    
    def _check_system_requirements(self):
        """Check if system meets requirements"""
        print("\n📋 SYSTEM REQUIREMENTS CHECK")
        print("-" * 30)
        
        requirements = {
            "python_version": "3.8+",
            "streamlit": "1.28+",
            "opencv": "4.5+",
            "sqlalchemy": "1.4+",
            "https_required": "For camera access"
        }
        
        # Check Python version
        python_version = f"{sys.version_info.major}.{sys.version_info.minor}"
        if float(python_version) >= 3.8:
            print(f"✅ Python {python_version} (Required: 3.8+)")
        else:
            print(f"❌ Python {python_version} (Required: 3.8+)")
            self.issues_found.append("Python version too old")
        
        # Check required packages
        required_packages = ['streamlit', 'opencv-python', 'sqlalchemy']
        for package in required_packages:
            try:
                __import__(package.replace('-', '_'))
                print(f"✅ {package} installed")
            except ImportError:
                print(f"❌ {package} missing")
                self.issues_found.append(f"Missing package: {package}")
        
        print("✅ HTTPS requirement noted for camera access")
    
    def _check_file_integrity(self):
        """Check if all required files exist and are properly configured"""
        print("\n📁 FILE INTEGRITY CHECK")
        print("-" * 30)
        
        required_files = {
            "student_test_interface.py": "Main student interface",
            "admin_dashboard.py": "Admin dashboard",
            "database_models.py": "Database models",
            "background_proctor.py": "Camera monitoring",
            "automated_flagging_system.py": "Violation detection",
            "static/proctoring.js": "Browser monitoring"
        }
        
        for file_path, description in required_files.items():
            if os.path.exists(file_path):
                print(f"✅ {file_path} - {description}")
            else:
                print(f"❌ {file_path} - {description} (MISSING)")
                self.issues_found.append(f"Missing file: {file_path}")
    
    def _check_database_status(self):
        """Check database connectivity and structure"""
        print("\n🗄️ DATABASE STATUS CHECK")
        print("-" * 30)
        
        try:
            from database_models import get_db_session, ProctorSession, MonitoringEvent
            
            db = get_db_session()
            try:
                # Test basic connectivity
                db.execute("SELECT 1")
                print("✅ Database connection successful")
                
                # Check if tables exist
                tables = ['users', 'tests', 'proctor_sessions', 'monitoring_events']
                for table in tables:
                    try:
                        db.execute(f"SELECT COUNT(*) FROM {table}")
                        print(f"✅ Table '{table}' exists")
                    except Exception as e:
                        print(f"❌ Table '{table}' missing or corrupted")
                        self.issues_found.append(f"Database table issue: {table}")
                
            finally:
                db.close()
                
        except Exception as e:
            print(f"❌ Database connection failed: {e}")
            self.issues_found.append("Database connectivity issue")
    
    def _check_browser_compatibility(self):
        """Check browser compatibility requirements"""
        print("\n🌐 BROWSER COMPATIBILITY CHECK")
        print("-" * 30)
        
        compatibility_info = {
            "getUserMedia API": "Required for camera access",
            "Fullscreen API": "Required for fullscreen enforcement",
            "JavaScript": "Required for monitoring",
            "WebRTC": "Required for real-time communication",
            "HTTPS": "Required for camera permissions"
        }
        
        print("📋 Browser Requirements:")
        for feature, requirement in compatibility_info.items():
            print(f"   • {feature}: {requirement}")
        
        print("\n✅ Supported Browsers:")
        print("   • Chrome 60+ (Recommended)")
        print("   • Firefox 55+")
        print("   • Safari 11+")
        print("   • Edge 79+")
        
        print("\n⚠️ Known Issues:")
        print("   • Safari: Limited fullscreen API support")
        print("   • Mobile browsers: Reduced functionality")
        print("   • Incognito mode: May block camera access")
    
    def _generate_fix_recommendations(self):
        """Generate specific fix recommendations based on found issues"""
        print("\n🛠️ FIX RECOMMENDATIONS")
        print("-" * 30)
        
        if not self.issues_found:
            print("✅ No issues found - system appears healthy!")
            return
        
        fix_map = {
            "Python version too old": "Upgrade Python to 3.8 or higher",
            "Missing package": "Run: pip install streamlit opencv-python sqlalchemy",
            "Missing file": "Restore missing files from backup or repository",
            "Database connectivity issue": "Check database file permissions and recreate if needed",
            "Database table issue": "Run database initialization script"
        }
        
        print("🔧 Recommended Fixes:")
        for issue in self.issues_found:
            for issue_type, fix in fix_map.items():
                if issue_type in issue:
                    print(f"   • {issue}: {fix}")
                    break
            else:
                print(f"   • {issue}: Manual investigation required")

def create_enhanced_error_handling():
    """Create enhanced error handling for the proctoring system"""
    print("\n" + "=" * 50)
    print("🛡️ ENHANCED ERROR HANDLING IMPLEMENTATION")
    print("=" * 50)
    
    error_handling_code = '''
// Enhanced JavaScript Error Handling for Proctoring
class EnhancedProctoringMonitor extends ProctoringMonitor {
    constructor() {
        super();
        this.errorCount = 0;
        this.maxErrors = 5;
        this.setupErrorHandling();
    }
    
    setupErrorHandling() {
        // Global error handler
        window.addEventListener('error', (event) => {
            this.handleError('JavaScript Error', event.error);
        });
        
        // Unhandled promise rejection handler
        window.addEventListener('unhandledrejection', (event) => {
            this.handleError('Promise Rejection', event.reason);
        });
    }
    
    async initializeCameraWithRetry(maxRetries = 3) {
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                await this.initializeCamera();
                return true;
            } catch (error) {
                console.warn(`Camera initialization attempt ${attempt} failed:`, error);
                
                if (attempt === maxRetries) {
                    this.handleCameraFailure(error);
                    return false;
                }
                
                // Wait before retry
                await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
            }
        }
    }
    
    handleCameraFailure(error) {
        const errorMessages = {
            'NotAllowedError': 'Camera access denied. Please allow camera permissions.',
            'NotFoundError': 'No camera found. Please connect a camera.',
            'NotReadableError': 'Camera is being used by another application.',
            'OverconstrainedError': 'Camera constraints not supported.',
            'SecurityError': 'Camera access blocked by security policy.'
        };
        
        const userMessage = errorMessages[error.name] || 'Camera initialization failed.';
        this.showUserFriendlyError(userMessage);
        this.logViolation('camera_failure', { error: error.message });
    }
    
    showUserFriendlyError(message) {
        // Create user-friendly error display
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #f8d7da;
            color: #721c24;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #f5c6cb;
            z-index: 10000;
            max-width: 400px;
            text-align: center;
        `;
        errorDiv.innerHTML = `
            <h3>⚠️ Proctoring Setup Issue</h3>
            <p>${message}</p>
            <button onclick="this.parentElement.remove()">Dismiss</button>
        `;
        document.body.appendChild(errorDiv);
    }
}
'''
    
    print("📝 Enhanced Error Handling Code Generated")
    print("💡 Key Features:")
    print("   • Automatic retry mechanisms")
    print("   • User-friendly error messages")
    print("   • Graceful degradation")
    print("   • Comprehensive error logging")
    
    return error_handling_code

def main():
    """Run complete troubleshooting and provide implementation guide"""
    troubleshooter = ProctoringTroubleshooter()
    issues, fixes = troubleshooter.run_full_diagnosis()
    
    # Create enhanced error handling
    error_handling = create_enhanced_error_handling()
    
    print("\n" + "=" * 50)
    print("📊 DIAGNOSIS SUMMARY")
    print("=" * 50)
    print(f"🔍 Issues Found: {len(issues)}")
    print(f"🛠️ Fixes Available: {len(fixes)}")
    
    if issues:
        print("\n⚠️ Issues Requiring Attention:")
        for i, issue in enumerate(issues, 1):
            print(f"   {i}. {issue}")
    else:
        print("\n✅ System appears to be functioning correctly!")
    
    return issues, fixes, error_handling

if __name__ == "__main__":
    main()
