import{s as l,r as d,L as p,ap as u,j as a,b9 as b,bc as f,aE as x,aF as k,aJ as L}from"./index.DKN5MVff.js";const C=l("div",{target:"esjt38z0"})({display:"flex",flexDirection:"column",width:"100%"}),v=l("a",{target:"esjt38z1"})(({disabled:n,isCurrentPage:e,theme:o})=>({textDecoration:"none",width:"100%",display:"flex",flexDirection:"row",alignItems:"center",justifyContent:"flex-start",gap:o.spacing.sm,borderRadius:o.radii.button,paddingLeft:o.spacing.sm,paddingRight:o.spacing.sm,marginTop:o.spacing.threeXS,marginBottom:o.spacing.threeXS,lineHeight:o.lineHeights.menuItem,backgroundColor:e?o.colors.darkenedBgMix15:"transparent","&:hover":{backgroundColor:e?o.colors.darkenedBgMix25:o.colors.darkenedBgMix15},"&:active,&:visited,&:hover":{textDecoration:"none"},"&:focus":{outline:"none"},"&:focus-visible":{backgroundColor:o.colors.darkenedBgMix15},"@media print":{paddingLeft:o.spacing.none},...n?{borderColor:o.colors.borderColor,backgroundColor:o.colors.transparent,color:o.colors.fadedText40,cursor:"not-allowed","&:hover":{color:o.colors.fadedText40,backgroundColor:o.colors.transparent}}:{}})),T=l("span",{target:"esjt38z2"})(({disabled:n,theme:e})=>({color:e.colors.bodyText,overflow:"hidden",whiteSpace:"nowrap",textOverflow:"ellipsis",display:"table-cell",...n?{borderColor:e.colors.borderColor,backgroundColor:e.colors.transparent,color:e.colors.fadedText40,cursor:"not-allowed"}:{}}));function w(n){const{onPageChange:e,currentPageScriptHash:o}=d.useContext(p),{colors:s}=u(),{disabled:t,element:r}=n,i=o===r.pageScriptHash,g=c=>{r.external?t&&c.preventDefault():(c.preventDefault(),t||e(r.pageScriptHash))};return a("div",{className:"stPageLink","data-testid":"stPageLink",children:a(b,{help:r.help,placement:f.TOP_RIGHT,containerWidth:!0,children:a(C,{children:x(v,{"data-testid":"stPageLink-NavLink",disabled:t,isCurrentPage:i,href:r.page,target:r.external?"_blank":"",rel:"noreferrer",onClick:g,children:[r.icon&&a(k,{size:"lg",color:t?s.fadedText40:s.bodyText,iconValue:r.icon}),a(T,{disabled:t,children:a(L,{source:r.label,allowHTML:!1,isLabel:!0,boldLabel:i,largerLabel:!0,disableLinks:!0})})]})})})})}const S=d.memo(w);export{S as default};
