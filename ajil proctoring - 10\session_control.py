"""
Session Control System
Allows admins to start/stop proctoring sessions and control user access
"""
import streamlit as st
import requests
import json
from datetime import datetime, timezone
from database_models import (
    Test, ProctorSession, create_proctor_session,
    get_session_by_token, get_active_sessions, get_db_session, User
)


def show_session_control():
    """Main session control interface"""
    st.header("🎮 Session Control Center")
    
    # Check if user is admin
    if st.session_state.get('user_role') != 'admin':
        st.error("Access denied. Admin privileges required.")
        return
    
    # Control tabs
    tab1, tab2, tab3 = st.tabs([
        "🚀 Start Sessions", 
        "🔍 Monitor Sessions", 
        "⚙️ Session Settings"
    ])
    
    with tab1:
        show_start_session_interface()
    
    with tab2:
        show_monitor_sessions_interface()
    
    with tab3:
        show_session_settings()


def show_start_session_interface():
    """Interface for starting new proctoring sessions"""
    st.subheader("🚀 Start New Proctoring Session")
    
    db = get_db_session()
    try:
        # Get available tests
        tests = db.query(Test).filter(Test.is_active == True).all()
        if not tests:
            st.error("No active tests available. Please create a test first.")
            return
        
        # Get available students
        students = db.query(User).filter(
            User.role == 'student', 
            User.is_active == True
        ).all()
        if not students:
            st.error("No active students found.")
            return
        
        # Session creation form
        with st.form("start_session_form"):
            col1, col2 = st.columns(2)
            
            with col1:
                # Test selection
                test_options = {f"{test.title} ({test.duration_minutes} min)": test.id for test in tests}
                selected_test = st.selectbox("Select Test", options=list(test_options.keys()))
                test_id = test_options[selected_test]
                
                # Student selection
                student_options = {f"{student.name} ({student.email})": student.id for student in students}
                selected_student = st.selectbox("Select Student", options=list(student_options.keys()))
                student_id = student_options[selected_student]
            
            with col2:
                # Session options
                auto_start_monitoring = st.checkbox("Auto-start background monitoring", value=True)
                send_notification = st.checkbox("Send notification to student", value=True)
                record_session = st.checkbox("Record session", value=True)
            
            # Submit button
            submitted = st.form_submit_button("🚀 Start Proctoring Session", type="primary")
            
            if submitted:
                try:
                    # Create the proctoring session
                    session = create_proctor_session(test_id, student_id)
                    
                    st.success(f"✅ Proctoring session created successfully!")
                    st.info(f"**Session Token:** `{session.session_token}`")
                    
                    # Display session details
                    col1, col2 = st.columns(2)
                    with col1:
                        st.write(f"**Session ID:** {session.id}")
                        st.write(f"**Test:** {selected_test}")
                        st.write(f"**Student:** {selected_student}")
                    
                    with col2:
                        st.write(f"**Status:** {session.status.title()}")
                        st.write(f"**Created:** {session.created_at.strftime('%Y-%m-%d %H:%M')}")
                    
                    # Auto-start monitoring if requested
                    if auto_start_monitoring:
                        if start_background_monitoring(session.session_token):
                            st.success("🎥 Background monitoring started automatically!")
                        else:
                            st.warning("⚠️ Failed to start background monitoring automatically.")
                    
                    # Generate test URL for student
                    test_url = generate_test_url(session.session_token)

                    st.markdown("---")
                    st.markdown("### 📝 Student Access Information")

                    col_a, col_b = st.columns(2)

                    with col_a:
                        st.markdown("**Option 1: Direct Link**")
                        st.code(test_url, language="text")
                        st.caption("Send this link directly to the student")

                    with col_b:
                        st.markdown("**Option 2: Manual Entry**")
                        st.markdown(f"**Session Token:** `{session.session_token}`")
                        st.markdown(f"**Student Portal:** http://localhost:8503")
                        st.caption("Student can enter the token manually at the portal")

                    st.info("""
                    **📋 Instructions for Student:**
                    1. Make sure the Student Test Portal is running (http://localhost:8503)
                    2. Either click the direct link OR go to the portal and enter the session token
                    3. Enter their registered email address
                    4. Click "Start Test" to begin
                    """)

                    # Copy buttons (placeholder - would need JavaScript in real implementation)
                    if st.button("📋 Copy Test Link", key=f"copy_link_{session.id}"):
                        st.success("✅ Test link copied to clipboard! (Feature would work with JavaScript)")

                    if st.button("📋 Copy Session Token", key=f"copy_token_{session.id}"):
                        st.success("✅ Session token copied to clipboard! (Feature would work with JavaScript)")
                    
                    # Copy to clipboard button
                    st.markdown(f"""
                    <button onclick="navigator.clipboard.writeText('{test_url}')">
                        📋 Copy Test URL
                    </button>
                    """, unsafe_allow_html=True)
                    
                    if send_notification:
                        st.info("📧 Notification feature will be implemented in future updates.")
                    
                except Exception as e:
                    st.error(f"❌ Error creating session: {str(e)}")
    
    finally:
        db.close()


def show_monitor_sessions_interface():
    """Enhanced interface for monitoring active sessions with detailed participant info"""
    st.subheader("🔍 Enhanced Session Monitor")

    # Get current admin info
    db = get_db_session()
    try:
        admin_user = db.query(User).filter(User.email == st.session_state.user_email).first()
        admin_sessions = get_sessions_by_admin(admin_user.id) if admin_user else []
        active_admin_sessions = [s for s in admin_sessions if s.status == 'active']
    finally:
        db.close()

    # Admin session overview
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("My Active Sessions", len(active_admin_sessions))
    with col2:
        total_participants = len(active_admin_sessions)
        st.metric("Total Participants", total_participants)
    with col3:
        completed_today = get_completed_sessions_today(admin_user.id if admin_user else 0)
        st.metric("Completed Today", completed_today)
    with col4:
        avg_violations = calculate_avg_violations(active_admin_sessions)
        st.metric("Avg Violations", f"{avg_violations:.1f}")

    # Refresh controls
    st.divider()
    col1, col2, col3, col4 = st.columns([1, 1, 1, 1])
    with col1:
        if st.button("🔄 Refresh"):
            st.rerun()

    with col2:
        auto_refresh = st.checkbox("Auto-refresh (10s)")

    with col3:
        if st.button("🛑 Stop All My Sessions", type="secondary"):
            stop_admin_sessions(admin_user.id if admin_user else 0)
            st.success("All your sessions stopped.")
            st.rerun()

    with col4:
        show_all_sessions = st.checkbox("Show All Sessions", help="Show sessions from all admins")

    # Auto-refresh logic
    if auto_refresh:
        import time
        time.sleep(10)
        st.rerun()

    # Get sessions to display
    if show_all_sessions:
        active_sessions = get_active_sessions()
        st.subheader("🌐 All Active Sessions")
    else:
        active_sessions = active_admin_sessions
        st.subheader("👤 My Active Sessions")

    if not active_sessions:
        st.info("No active proctoring sessions.")
        return

    st.write(f"**Displaying {len(active_sessions)} active session(s)**")

    # Enhanced session display with detailed participant info
    for session in active_sessions:
        with st.container():
            # Get session details
            db = get_db_session()
            try:
                test = db.query(Test).filter(Test.id == session.test_id).first()
                student = db.query(User).filter(User.id == session.student_id).first()

                # Get violation details
                from database_models import MonitoringEvent
                violations = db.query(MonitoringEvent).filter(
                    MonitoringEvent.session_id == session.id
                ).order_by(MonitoringEvent.timestamp.desc()).limit(5).all()

                # Session header
                col1, col2, col3 = st.columns([3, 1, 1])
                with col1:
                    st.markdown(f"### 📝 {test.title if test else 'Unknown Test'}")
                    st.write(f"**Student:** {student.name if student else 'Unknown'} ({student.email if student else 'N/A'})")

                with col2:
                    status_color = "🟢" if session.status == 'active' else "🔴"
                    st.write(f"**Status:** {status_color} {session.status.title()}")
                    if session.started_at:
                        duration = datetime.now(timezone.utc) - session.started_at
                        st.write(f"**Duration:** {str(duration).split('.')[0]}")

                with col3:
                    monitoring_status = check_monitoring_status(session.session_token)
                    monitor_color = "🟢" if monitoring_status else "🔴"
                    st.write(f"**Monitoring:** {monitor_color}")
                    st.write(f"**Session ID:** {session.id}")

                # Detailed metrics
                col1, col2, col3, col4, col5 = st.columns(5)

                with col1:
                    st.metric("Total Violations", session.total_violations,
                             delta=f"+{len([v for v in violations if v.severity in ['high', 'critical']])}" if violations else None)

                with col2:
                    st.metric("Face Detection", f"{session.face_detection_score:.1%}",
                             delta="Good" if session.face_detection_score > 0.8 else "Poor")

                with col3:
                    st.metric("Attention Score", f"{session.attention_score:.1%}",
                             delta="Good" if session.attention_score > 0.7 else "Poor")

                with col4:
                    tab_switches = count_violation_type(violations, 'tab_switch')
                    st.metric("Tab Switches", tab_switches,
                             delta="High" if tab_switches > 3 else None)

                with col5:
                    fullscreen_exits = count_violation_type(violations, 'fullscreen_exit')
                    st.metric("Fullscreen Exits", fullscreen_exits,
                             delta="High" if fullscreen_exits > 2 else None)

                # Recent violations
                if violations:
                    st.write("**Recent Violations:**")
                    violation_cols = st.columns(len(violations[:5]))
                    for i, violation in enumerate(violations[:5]):
                        with violation_cols[i]:
                            severity_emoji = {
                                'low': '🟢', 'medium': '🟡',
                                'high': '🔴', 'critical': '🚨'
                            }.get(violation.severity, '⚪')
                            st.write(f"{severity_emoji} {violation.event_type.replace('_', ' ').title()}")
                            st.caption(violation.timestamp.strftime('%H:%M:%S'))

                # Admin controls
                st.write("**Admin Controls:**")
                col1, col2, col3, col4, col5 = st.columns(5)

                with col1:
                    if st.button("🛑 Terminate Session", key=f"terminate_{session.id}", type="secondary"):
                        if st.session_state.get(f"confirm_terminate_{session.id}", False):
                            stop_session(session.session_token)
                            st.success("Session terminated.")
                            st.rerun()
                        else:
                            st.session_state[f"confirm_terminate_{session.id}"] = True
                            st.warning("Click again to confirm termination")

                with col2:
                    if st.button("⚠️ Send Warning", key=f"warn_{session.id}"):
                        send_warning_to_student(session.id, "Please focus on your test and avoid suspicious activities.")
                        st.success("Warning sent to student")

                with col3:
                    if st.button("🚫 Kick Participant", key=f"kick_{session.id}", type="secondary"):
                        if st.session_state.get(f"confirm_kick_{session.id}", False):
                            kick_participant(session.id, "Removed due to suspicious activity")
                            st.success("Participant kicked from test")
                            st.rerun()
                        else:
                            st.session_state[f"confirm_kick_{session.id}"] = True
                            st.error("Click again to confirm kick")

                with col4:
                    if st.button("📊 Detailed Report", key=f"report_{session.id}"):
                        show_detailed_session_report(session)

                with col5:
                    if st.button("📹 View Recording", key=f"recording_{session.id}"):
                        if session.recording_path:
                            st.info(f"Recording: {session.recording_path}")
                        else:
                            st.warning("No recording available")

                # Expandable detailed view
                with st.expander(f"🔍 Detailed Analysis - Session {session.id}", expanded=False):
                    show_detailed_session_analysis(session, violations)

            finally:
                db.close()

            st.divider()


def get_sessions_by_admin(admin_id):
    """Get all sessions created by a specific admin"""
    db = get_db_session()
    try:
        # Get tests created by this admin
        admin_tests = db.query(Test).filter(Test.created_by == admin_id).all()
        test_ids = [test.id for test in admin_tests]

        # Get sessions for these tests
        sessions = db.query(ProctorSession).filter(ProctorSession.test_id.in_(test_ids)).all()
        return sessions
    finally:
        db.close()


def get_completed_sessions_today(admin_id):
    """Get count of sessions completed today by admin"""
    from datetime import date
    db = get_db_session()
    try:
        admin_tests = db.query(Test).filter(Test.created_by == admin_id).all()
        test_ids = [test.id for test in admin_tests]

        today = date.today()
        completed_today = db.query(ProctorSession).filter(
            ProctorSession.test_id.in_(test_ids),
            ProctorSession.status == 'completed',
            db.func.date(ProctorSession.completed_at) == today
        ).count()

        return completed_today
    finally:
        db.close()


def calculate_avg_violations(sessions):
    """Calculate average violations across sessions"""
    if not sessions:
        return 0

    total_violations = sum(session.total_violations for session in sessions)
    return total_violations / len(sessions)


def stop_admin_sessions(admin_id):
    """Stop all active sessions for a specific admin"""
    admin_sessions = get_sessions_by_admin(admin_id)
    active_sessions = [s for s in admin_sessions if s.status == 'active']

    for session in active_sessions:
        stop_session(session.session_token)


def count_violation_type(violations, violation_type):
    """Count specific type of violations"""
    return len([v for v in violations if v.event_type == violation_type])


def send_warning_to_student(session_id, message):
    """Send warning message to student (mock implementation)"""
    # In a real implementation, this would send a notification to the student's interface
    pass


def kick_participant(session_id, reason):
    """Kick participant from test session"""
    db = get_db_session()
    try:
        session = db.query(ProctorSession).filter(ProctorSession.id == session_id).first()
        if session:
            session.status = 'terminated'
            session.completed_at = datetime.now(timezone.utc)
            if session.started_at:
                session.total_duration = int((session.completed_at - session.started_at).total_seconds())

            # Log the kick event
            from database_models import log_monitoring_event
            log_monitoring_event(
                session_id=session_id,
                event_type='participant_kicked',
                event_data={'reason': reason, 'kicked_by': 'admin'},
                severity='critical'
            )

            db.commit()
    finally:
        db.close()


def show_detailed_session_report(session):
    """Show detailed session report in modal"""
    st.subheader(f"📊 Detailed Report - Session {session.id}")

    db = get_db_session()
    try:
        test = db.query(Test).filter(Test.id == session.test_id).first()
        student = db.query(User).filter(User.id == session.student_id).first()

        # Session overview
        col1, col2 = st.columns(2)

        with col1:
            st.write("**Session Information:**")
            st.write(f"- Test: {test.title if test else 'Unknown'}")
            st.write(f"- Student: {student.name if student else 'Unknown'}")
            st.write(f"- Email: {student.email if student else 'Unknown'}")
            st.write(f"- Status: {session.status.title()}")
            st.write(f"- Started: {session.started_at.strftime('%Y-%m-%d %H:%M:%S') if session.started_at else 'Not started'}")
            st.write(f"- Duration: {session.total_duration // 60 if session.total_duration else 0} minutes")

        with col2:
            st.write("**Performance Metrics:**")
            st.write(f"- Total Violations: {session.total_violations}")
            st.write(f"- Face Detection Score: {session.face_detection_score:.1%}")
            st.write(f"- Attention Score: {session.attention_score:.1%}")

            # Calculate additional metrics
            from database_models import MonitoringEvent
            violations = db.query(MonitoringEvent).filter(MonitoringEvent.session_id == session.id).all()

            critical_violations = len([v for v in violations if v.severity == 'critical'])
            st.write(f"- Critical Violations: {critical_violations}")

            tab_switches = len([v for v in violations if v.event_type == 'tab_switch'])
            st.write(f"- Tab Switches: {tab_switches}")

            fullscreen_exits = len([v for v in violations if v.event_type == 'fullscreen_exit'])
            st.write(f"- Fullscreen Exits: {fullscreen_exits}")

        # Violation timeline
        if violations:
            st.subheader("🕒 Violation Timeline")
            violation_data = []
            for violation in violations:
                violation_data.append({
                    'Time': violation.timestamp.strftime('%H:%M:%S'),
                    'Type': violation.event_type.replace('_', ' ').title(),
                    'Severity': violation.severity.title(),
                    'Details': str(violation.get_event_data()) if violation.event_data else 'N/A'
                })

            import pandas as pd
            df = pd.DataFrame(violation_data)
            st.dataframe(df, use_container_width=True, hide_index=True)

    finally:
        db.close()


def show_detailed_session_analysis(session, violations):
    """Show detailed session analysis"""
    col1, col2 = st.columns(2)

    with col1:
        st.write("**Behavioral Analysis:**")

        # Analyze violation patterns
        if violations:
            violation_types = {}
            for violation in violations:
                v_type = violation.event_type
                violation_types[v_type] = violation_types.get(v_type, 0) + 1

            st.write("Violation Breakdown:")
            for v_type, count in violation_types.items():
                st.write(f"- {v_type.replace('_', ' ').title()}: {count}")
        else:
            st.write("No violations detected - Good behavior!")

        # Risk assessment
        risk_level = calculate_risk_level(session, violations)
        risk_color = {"Low": "🟢", "Medium": "🟡", "High": "🔴", "Critical": "🚨"}.get(risk_level, "⚪")
        st.write(f"**Risk Level:** {risk_color} {risk_level}")

    with col2:
        st.write("**Recommendations:**")

        recommendations = generate_recommendations(session, violations)
        for rec in recommendations:
            st.write(f"- {rec}")


def calculate_risk_level(session, violations):
    """Calculate risk level based on session data"""
    score = 0

    # Factor in violations
    score += session.total_violations * 2

    # Factor in critical violations
    critical_violations = len([v for v in violations if v.severity == 'critical'])
    score += critical_violations * 5

    # Factor in attention score
    if session.attention_score < 0.5:
        score += 10
    elif session.attention_score < 0.7:
        score += 5

    # Factor in face detection
    if session.face_detection_score < 0.6:
        score += 8
    elif session.face_detection_score < 0.8:
        score += 4

    if score >= 20:
        return "Critical"
    elif score >= 15:
        return "High"
    elif score >= 8:
        return "Medium"
    else:
        return "Low"


def generate_recommendations(session, violations):
    """Generate recommendations based on session analysis"""
    recommendations = []

    if session.total_violations > 5:
        recommendations.append("High violation count - Consider reviewing test environment setup")

    if session.face_detection_score < 0.7:
        recommendations.append("Low face detection - Student may need better camera positioning")

    if session.attention_score < 0.6:
        recommendations.append("Low attention score - Consider additional monitoring or breaks")

    tab_switches = len([v for v in violations if v.event_type == 'tab_switch'])
    if tab_switches > 3:
        recommendations.append("Multiple tab switches detected - Investigate potential cheating")

    if not recommendations:
        recommendations.append("Session appears normal - No immediate concerns")

    return recommendations


def show_session_settings():
    """Session configuration settings"""
    st.subheader("⚙️ Session Settings")
    
    # Global settings
    st.write("**Global Proctoring Settings**")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("Monitoring Settings")
        
        # Violation thresholds
        max_face_lost_time = st.number_input(
            "Max time without face (seconds)", 
            min_value=1, 
            max_value=60, 
            value=5
        )
        
        max_violations = st.number_input(
            "Max violations before termination", 
            min_value=1, 
            max_value=20, 
            value=10
        )
        
        enable_audio_monitoring = st.checkbox("Enable audio monitoring", value=False)
        enable_screen_recording = st.checkbox("Enable screen recording", value=True)
    
    with col2:
        st.subheader("Alert Settings")
        
        real_time_alerts = st.checkbox("Real-time violation alerts", value=True)
        email_notifications = st.checkbox("Email notifications", value=False)
        
        alert_severity = st.selectbox(
            "Minimum alert severity",
            ["low", "medium", "high", "critical"],
            index=1
        )
        
        auto_terminate = st.checkbox("Auto-terminate on critical violations", value=False)
    
    # Save settings
    if st.button("💾 Save Settings"):
        # Save settings to database or config file
        settings = {
            'max_face_lost_time': max_face_lost_time,
            'max_violations': max_violations,
            'enable_audio_monitoring': enable_audio_monitoring,
            'enable_screen_recording': enable_screen_recording,
            'real_time_alerts': real_time_alerts,
            'email_notifications': email_notifications,
            'alert_severity': alert_severity,
            'auto_terminate': auto_terminate
        }
        
        # TODO: Implement settings persistence
        st.success("Settings saved successfully!")
    
    # API Server Status
    st.divider()
    st.subheader("🔧 System Status")
    
    col1, col2 = st.columns(2)
    
    with col1:
        # Check API server status
        api_status = check_api_server_status()
        if api_status:
            st.success("✅ API Server: Online")
        else:
            st.error("❌ API Server: Offline")
            if st.button("🚀 Start API Server"):
                start_api_server()
    
    with col2:
        # Database status
        try:
            db = get_db_session()
            db.close()
            st.success("✅ Database: Connected")
        except Exception as e:
            st.error(f"❌ Database: Error - {str(e)}")


def start_background_monitoring(session_token):
    """Start background monitoring for a session"""
    try:
        response = requests.post(
            'http://localhost:5000/api/session/start',
            json={'session_token': session_token},
            timeout=5
        )
        return response.status_code == 200
    except Exception as e:
        st.error(f"Error starting monitoring: {e}")
        return False


def stop_session(session_token):
    """Stop a proctoring session"""
    try:
        # Stop background monitoring
        requests.post(
            'http://localhost:5000/api/session/stop',
            json={'session_token': session_token},
            timeout=5
        )
        
        # Update session status in database
        db = get_db_session()
        try:
            session = get_session_by_token(session_token)
            if session:
                session.status = 'terminated'
                session.completed_at = datetime.now(timezone.utc)
                if session.started_at:
                    session.total_duration = int((session.completed_at - session.started_at).total_seconds())
                db.merge(session)
                db.commit()
                return True
        finally:
            db.close()
            
    except Exception as e:
        st.error(f"Error stopping session: {e}")
        return False


def stop_all_sessions():
    """Stop all active sessions"""
    active_sessions = get_active_sessions()
    for session in active_sessions:
        stop_session(session.session_token)


def check_monitoring_status(session_token):
    """Check if background monitoring is active for a session"""
    try:
        response = requests.get('http://localhost:5000/api/session/status', timeout=5)
        if response.status_code == 200:
            data = response.json()
            return session_token in data.get('active_sessions', [])
        return False
    except:
        return False


def check_api_server_status():
    """Check if API server is running"""
    try:
        response = requests.get('http://localhost:5000/api/session/status', timeout=2)
        return response.status_code == 200
    except:
        return False


def start_api_server():
    """Start the API server"""
    try:
        import subprocess
        import sys
        
        subprocess.Popen([
            sys.executable, "api_server.py"
        ], cwd=".")
        
        st.success("API server starting...")
        st.info("Please wait a few seconds and refresh to check status.")
        
    except Exception as e:
        st.error(f"Error starting API server: {e}")


def generate_test_url(session_token):
    """Generate test URL for student"""
    # Generate URL for the student test portal
    base_url = "http://localhost:8503"  # Student test portal port
    return f"{base_url}?token={session_token}"


def show_session_details_modal(session):
    """Show detailed session information in a modal"""
    st.subheader(f"Session Details - {session.id}")
    
    db = get_db_session()
    try:
        test = db.query(Test).filter(Test.id == session.test_id).first()
        student = db.query(User).filter(User.id == session.student_id).first()
        
        # Basic info
        col1, col2 = st.columns(2)
        
        with col1:
            st.write(f"**Session ID:** {session.id}")
            st.write(f"**Token:** {session.session_token}")
            st.write(f"**Test:** {test.title if test else 'Unknown'}")
            st.write(f"**Student:** {student.name if student else 'Unknown'}")
        
        with col2:
            st.write(f"**Status:** {session.status.title()}")
            st.write(f"**Created:** {session.created_at.strftime('%Y-%m-%d %H:%M')}")
            if session.started_at:
                st.write(f"**Started:** {session.started_at.strftime('%Y-%m-%d %H:%M')}")
            if session.completed_at:
                st.write(f"**Completed:** {session.completed_at.strftime('%Y-%m-%d %H:%M')}")
        
        # Metrics
        st.subheader("Performance Metrics")
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("Total Violations", session.total_violations)
        with col2:
            st.metric("Face Detection Score", f"{session.face_detection_score:.1%}")
        with col3:
            st.metric("Attention Score", f"{session.attention_score:.1%}")
        
        # Recent events
        from database_models import MonitoringEvent
        recent_events = db.query(MonitoringEvent).filter(
            MonitoringEvent.session_id == session.id
        ).order_by(MonitoringEvent.timestamp.desc()).limit(10).all()
        
        if recent_events:
            st.subheader("Recent Events")
            event_data = []
            for event in recent_events:
                event_data.append({
                    'Time': event.timestamp.strftime('%H:%M:%S'),
                    'Type': event.event_type.replace('_', ' ').title(),
                    'Severity': event.severity.title()
                })
            
            import pandas as pd
            df = pd.DataFrame(event_data)
            st.dataframe(df, use_container_width=True, hide_index=True)
        else:
            st.info("No events recorded for this session.")
    
    finally:
        db.close()
