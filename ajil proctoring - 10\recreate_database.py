"""
Recreate Database Script
Backs up existing data and recreates database with proper schema
"""
import sqlite3
import os
import json
from datetime import datetime

def backup_and_recreate_database():
    """Backup existing data and recreate database"""
    db_path = "users.db"
    backup_path = f"users_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
    
    print("🔄 Starting database recreation...")
    
    # Backup existing database if it exists
    if os.path.exists(db_path):
        print(f"📦 Backing up existing database to {backup_path}")
        import shutil
        shutil.copy2(db_path, backup_path)
        
        # Extract existing data
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Backup users
        users_data = []
        try:
            cursor.execute("SELECT * FROM users")
            users_data = cursor.fetchall()
            print(f"📊 Backed up {len(users_data)} users")
        except sqlite3.OperationalError:
            print("⚠️ Users table not found")
        
        # Backup tests
        tests_data = []
        try:
            cursor.execute("SELECT * FROM tests")
            tests_data = cursor.fetchall()
            print(f"📊 Backed up {len(tests_data)} tests")
        except sqlite3.OperationalError:
            print("⚠️ Tests table not found")
        
        conn.close()
        
        # Remove old database
        os.remove(db_path)
        print("🗑️ Removed old database")
    else:
        users_data = []
        tests_data = []
        print("📝 No existing database found, creating new one")
    
    # Create new database with proper schema
    print("🏗️ Creating new database with proper schema...")
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Create users table
    cursor.execute("""
        CREATE TABLE users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            email VARCHAR(255) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            name VARCHAR(255) NOT NULL,
            role VARCHAR(50) NOT NULL DEFAULT 'student',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            last_login DATETIME,
            is_active BOOLEAN DEFAULT 1
        )
    """)
    print("✅ Created users table")
    
    # Create tests table
    cursor.execute("""
        CREATE TABLE tests (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title VARCHAR(255) NOT NULL,
            description TEXT,
            duration_minutes INTEGER NOT NULL,
            created_by VARCHAR(255) NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            questions_data TEXT,
            settings TEXT,
            is_active BOOLEAN DEFAULT 1
        )
    """)
    print("✅ Created tests table")
    
    # Create proctor_sessions table
    cursor.execute("""
        CREATE TABLE proctor_sessions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            test_id INTEGER NOT NULL,
            student_id INTEGER NOT NULL,
            session_token VARCHAR(255) UNIQUE NOT NULL,
            status VARCHAR(50) NOT NULL DEFAULT 'created',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            started_at DATETIME,
            completed_at DATETIME,
            total_duration INTEGER,
            recording_path VARCHAR(500),
            total_violations INTEGER DEFAULT 0,
            face_detection_score REAL DEFAULT 0.0,
            attention_score REAL DEFAULT 0.0,
            FOREIGN KEY (test_id) REFERENCES tests (id),
            FOREIGN KEY (student_id) REFERENCES users (id)
        )
    """)
    print("✅ Created proctor_sessions table")
    
    # Create monitoring_events table
    cursor.execute("""
        CREATE TABLE monitoring_events (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            session_id INTEGER NOT NULL,
            event_type VARCHAR(100) NOT NULL,
            severity VARCHAR(20) DEFAULT 'medium',
            description TEXT,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            metadata TEXT,
            FOREIGN KEY (session_id) REFERENCES proctor_sessions (id)
        )
    """)
    print("✅ Created monitoring_events table")
    
    # Create session_metrics table
    cursor.execute("""
        CREATE TABLE session_metrics (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            session_id INTEGER NOT NULL,
            metric_name VARCHAR(100) NOT NULL,
            metric_value REAL NOT NULL,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (session_id) REFERENCES proctor_sessions (id)
        )
    """)
    print("✅ Created session_metrics table")
    
    # Create indexes
    indexes = [
        "CREATE INDEX idx_users_email ON users(email)",
        "CREATE INDEX idx_users_role ON users(role)",
        "CREATE INDEX idx_tests_created_by ON tests(created_by)",
        "CREATE INDEX idx_tests_is_active ON tests(is_active)",
        "CREATE INDEX idx_proctor_sessions_test_id ON proctor_sessions(test_id)",
        "CREATE INDEX idx_proctor_sessions_student_id ON proctor_sessions(student_id)",
        "CREATE INDEX idx_proctor_sessions_status ON proctor_sessions(status)",
        "CREATE INDEX idx_proctor_sessions_created_at ON proctor_sessions(created_at)",
        "CREATE INDEX idx_monitoring_events_session_id ON monitoring_events(session_id)",
        "CREATE INDEX idx_monitoring_events_timestamp ON monitoring_events(timestamp)",
        "CREATE INDEX idx_session_metrics_session_id ON session_metrics(session_id)"
    ]
    
    for index_sql in indexes:
        cursor.execute(index_sql)
    print("✅ Created database indexes")
    
    # Restore users data
    if users_data:
        print(f"📥 Restoring {len(users_data)} users...")
        for user in users_data:
            try:
                cursor.execute("""
                    INSERT INTO users (id, email, password_hash, name, role, created_at, last_login, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, user)
            except Exception as e:
                print(f"⚠️ Error restoring user {user[1]}: {e}")
    
    # Restore tests data
    if tests_data:
        print(f"📥 Restoring {len(tests_data)} tests...")
        for test in tests_data:
            try:
                # Handle different column counts
                if len(test) >= 8:
                    cursor.execute("""
                        INSERT INTO tests (id, title, description, duration_minutes, created_by, created_at, questions_data, settings, is_active)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, test[:9])
                else:
                    # Old format, add default values
                    cursor.execute("""
                        INSERT INTO tests (id, title, description, duration_minutes, created_by, created_at, questions_data, settings, is_active)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1)
                    """, test + (None, None))
            except Exception as e:
                print(f"⚠️ Error restoring test {test[1] if len(test) > 1 else 'unknown'}: {e}")
    
    # Create default admin user if no users exist
    cursor.execute("SELECT COUNT(*) FROM users")
    user_count = cursor.fetchone()[0]
    
    if user_count == 0:
        print("👤 Creating default admin user...")
        import hashlib
        admin_password = hashlib.sha256("admin123".encode()).hexdigest()
        cursor.execute("""
            INSERT INTO users (email, password_hash, name, role)
            VALUES (?, ?, ?, ?)
        """, ("<EMAIL>", admin_password, "Admin User", "admin"))
        
        student_password = hashlib.sha256("student123".encode()).hexdigest()
        cursor.execute("""
            INSERT INTO users (email, password_hash, name, role)
            VALUES (?, ?, ?, ?)
        """, ("<EMAIL>", student_password, "Test Student", "student"))
        
        print("✅ Created default users")
    
    conn.commit()
    conn.close()
    
    print("🎉 Database recreation completed successfully!")
    print(f"📦 Backup saved as: {backup_path}")
    print("\n🔑 Default login credentials:")
    print("Admin: <EMAIL> / admin123")
    print("Student: <EMAIL> / student123")

if __name__ == "__main__":
    backup_and_recreate_database()
