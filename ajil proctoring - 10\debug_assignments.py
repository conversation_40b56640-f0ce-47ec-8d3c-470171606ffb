"""
Debug script to check test assignments in the database
"""
from database_models import get_db_session, User, Test, TestAssignment
from datetime import datetime, timezone

def debug_assignments():
    """Check what's in the database"""
    db = get_db_session()
    try:
        print("=== DATABASE DEBUG ===")
        
        # Check users
        print("\n1. USERS:")
        users = db.query(User).all()
        for user in users:
            print(f"   ID: {user.id}, Email: {user.email}, Role: {user.role}, Active: {user.is_active}")
        
        # Check tests
        print("\n2. TESTS:")
        tests = db.query(Test).all()
        for test in tests:
            print(f"   ID: {test.id}, Title: {test.title}, Created by: {test.created_by}, Active: {test.is_active}")
        
        # Check test assignments
        print("\n3. TEST ASSIGNMENTS:")
        assignments = db.query(TestAssignment).all()
        if assignments:
            for assignment in assignments:
                print(f"   ID: {assignment.id}")
                print(f"   Test ID: {assignment.test_id}")
                print(f"   Student ID: {assignment.student_id}")
                print(f"   Status: {assignment.status}")
                print(f"   Deadline: {assignment.deadline}")
                print(f"   Assigned at: {assignment.assigned_at}")
                print(f"   Access token: {assignment.access_token}")
                print("   ---")
        else:
            print("   No test assignments found!")
        
        # Check specific student
        print("\n4. STUDENT TEST ASSIGNMENTS:")
        student = db.query(User).filter(User.email == '<EMAIL>').first()
        if student:
            print(f"   Student found: ID {student.id}, Name: {student.name}")
            student_assignments = db.query(TestAssignment).filter(
                TestAssignment.student_id == student.id
            ).all()
            print(f"   Assignments for this student: {len(student_assignments)}")
            for assignment in student_assignments:
                test = db.query(Test).filter(Test.id == assignment.test_id).first()
                print(f"     - Test: {test.title if test else 'Unknown'}")
                print(f"       Status: {assignment.status}")
                print(f"       Deadline: {assignment.deadline}")
        else:
            print("   Student not found!")
            
    finally:
        db.close()

if __name__ == "__main__":
    debug_assignments()
