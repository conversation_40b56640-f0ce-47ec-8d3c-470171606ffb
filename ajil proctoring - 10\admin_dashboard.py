"""
Admin Dashboard and Reporting System
"""
import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import time
from datetime import datetime, timezone, timedelta
from sqlalchemy import func
from database_models import (
    Test, ProctorSession, MonitoringEvent, SessionMetrics, get_db_session, User
)
import pandas as pd
from automated_flagging_system import start_flagging_system, stop_flagging_system, get_flagged_sessions


def show_admin_dashboard():
    """Main admin dashboard interface"""
    st.header("🔧 Admin Dashboard")
    
    # Check if user is admin
    if st.session_state.get('user_role') != 'admin':
        st.error("Access denied. Admin privileges required.")
        return
    
    # Dashboard tabs
    tab1, tab2, tab3, tab4, tab5, tab6 = st.tabs([
        "📊 Overview",
        "🔍 Live Monitoring",
        "📈 Reports",
        "📋 Session Management",
        "📊 Report Generator",
        "🚨 Flagging System"
    ])
    
    with tab1:
        show_overview_dashboard()

    with tab2:
        show_live_monitoring()

    with tab3:
        show_reports_dashboard()

    with tab4:
        show_session_management()

    with tab5:
        # Import and show report generator
        try:
            from report_generator import show_report_generator
            show_report_generator()
        except ImportError as e:
            st.error(f"Error loading report generator: {e}")

    with tab6:
        show_flagging_system_dashboard()


def show_overview_dashboard():
    """Overview dashboard with key metrics"""
    st.subheader("📊 System Overview")
    
    # Get database session
    db = get_db_session()
    try:
        # Key metrics
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            total_tests = db.query(Test).filter(Test.is_active == True).count()
            st.metric("Active Tests", total_tests)
        
        with col2:
            active_sessions = db.query(ProctorSession).filter(ProctorSession.status == 'active').count()
            st.metric("Active Sessions", active_sessions)
        
        with col3:
            total_students = db.query(User).filter(User.role == 'student', User.is_active == True).count()
            st.metric("Total Students", total_students)
        
        with col4:
            total_violations = db.query(MonitoringEvent).count()
            st.metric("Total Violations", total_violations)
        
        st.divider()
        
        # Recent activity
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("📝 Recent Tests")
            recent_tests = db.query(Test).order_by(Test.created_at.desc()).limit(5).all()
            
            if recent_tests:
                test_data = []
                for test in recent_tests:
                    test_data.append({
                        'Title': test.title,
                        'Duration': f"{test.duration_minutes} min",
                        'Created': test.created_at.strftime('%Y-%m-%d %H:%M'),
                        'Status': 'Active' if test.is_active else 'Inactive'
                    })

                df = pd.DataFrame(test_data)
                st.dataframe(df, use_container_width=True, hide_index=True)
            else:
                st.info("📝 **No tests found**")
                st.markdown("""
                **Get started:**
                1. Go to **Test Management** tab
                2. Click **Create Test**
                3. Upload documents or create questions manually
                """)

                # Show sample data format
                sample_data = pd.DataFrame({
                    'Title': ['Sample Math Test', 'Science Quiz'],
                    'Duration': ['45 min', '30 min'],
                    'Created': ['2024-01-15 10:30', '2024-01-15 14:20'],
                    'Status': ['Active', 'Active']
                })
                st.markdown("**Data will appear in this format:**")
                st.dataframe(sample_data, use_container_width=True, hide_index=True)
        
        with col2:
            st.subheader("🚨 Recent Violations")
            recent_violations = db.query(MonitoringEvent).order_by(
                MonitoringEvent.timestamp.desc()
            ).limit(5).all()
            
            if recent_violations:
                violation_data = []
                for violation in recent_violations:
                    violation_data.append({
                        'Type': violation.event_type.replace('_', ' ').title(),
                        'Severity': violation.severity.title(),
                        'Time': violation.timestamp.strftime('%Y-%m-%d %H:%M'),
                        'Session': violation.session_id
                    })

                df = pd.DataFrame(violation_data)
                st.dataframe(df, use_container_width=True, hide_index=True)
            else:
                st.info("🚨 **No violations recorded**")
                st.markdown("""
                **Violations will appear when:**
                1. Students take tests with proctoring enabled
                2. Suspicious behavior is detected
                3. Camera or screen monitoring flags issues
                """)

                # Show sample data format
                sample_data = pd.DataFrame({
                    'Type': ['Face Not Detected', 'Tab Switch', 'Multiple Faces'],
                    'Severity': ['High', 'Medium', 'High'],
                    'Time': ['2024-01-15 10:35', '2024-01-15 10:42', '2024-01-15 11:15'],
                    'Session': [101, 102, 103]
                })
                st.markdown("**Data will appear in this format:**")
                st.dataframe(sample_data, use_container_width=True, hide_index=True)
        
        # Charts
        st.divider()
        st.subheader("📈 Analytics")
        
        col1, col2 = st.columns(2)
        
        with col1:
            # Violations by type chart
            violation_counts = db.query(
                MonitoringEvent.event_type,
                func.count(MonitoringEvent.id).label('count')
            ).group_by(MonitoringEvent.event_type).all()
            
            if violation_counts:
                violation_df = pd.DataFrame(violation_counts, columns=['Event Type', 'Count'])
                violation_df['Event Type'] = violation_df['Event Type'].str.replace('_', ' ').str.title()
                
                fig = px.pie(
                    violation_df, 
                    values='Count', 
                    names='Event Type',
                    title="Violations by Type"
                )
                st.plotly_chart(fig, use_container_width=True)
            else:
                st.info("No violation data available for chart.")
        
        with col2:
            # Sessions over time
            session_counts = db.query(
                func.date(ProctorSession.created_at).label('date'),
                func.count(ProctorSession.id).label('count')
            ).group_by(func.date(ProctorSession.created_at)).order_by('date').all()
            
            if session_counts:
                session_df = pd.DataFrame(session_counts, columns=['Date', 'Sessions'])
                
                fig = px.line(
                    session_df, 
                    x='Date', 
                    y='Sessions',
                    title="Sessions Over Time"
                )
                st.plotly_chart(fig, use_container_width=True)
            else:
                st.info("No session data available for chart.")
    
    finally:
        db.close()


def show_live_monitoring():
    """Live monitoring of active sessions with violation dashboard"""
    st.subheader("🔍 Live Session Monitoring")

    # Auto-refresh toggle
    auto_refresh = st.checkbox("Auto-refresh (30 seconds)", value=True)

    # Add violation monitoring section
    show_violation_dashboard()
    
    if auto_refresh:
        # Auto-refresh every 30 seconds
        time.sleep(30)
        st.rerun()
    
    # Manual refresh button
    if st.button("🔄 Refresh Now"):
        st.rerun()
    
    db = get_db_session()
    try:
        # Get active sessions
        active_sessions = db.query(ProctorSession).filter(
            ProctorSession.status == 'active'
        ).all()
        
        if not active_sessions:
            st.info("No active proctoring sessions.")
            return
        
        st.write(f"**Active Sessions: {len(active_sessions)}**")
        
        # Display each active session
        for session in active_sessions:
            with st.expander(f"Session {session.id} - Token: {session.session_token[:8]}...", expanded=True):
                col1, col2, col3 = st.columns([2, 1, 1])
                
                with col1:
                    # Session info
                    test = db.query(Test).filter(Test.id == session.test_id).first()
                    student = db.query(User).filter(User.id == session.student_id).first()
                    
                    st.write(f"**Test:** {test.title if test else 'Unknown'}")
                    st.write(f"**Student:** {student.name if student else 'Unknown'}")
                    
                    if session.started_at:
                        duration = datetime.now(timezone.utc) - session.started_at
                        st.write(f"**Duration:** {str(duration).split('.')[0]}")
                
                with col2:
                    # Current metrics
                    st.metric("Violations", session.total_violations)
                    st.metric("Face Score", f"{session.face_detection_score:.1%}")
                    st.metric("Attention", f"{session.attention_score:.1%}")
                
                with col3:
                    # Actions
                    if st.button(f"Terminate Session", key=f"terminate_{session.id}"):
                        terminate_session(session.id)
                        st.rerun()
                    
                    if st.button(f"View Details", key=f"details_{session.id}"):
                        show_session_details(session.id)
                
                # Recent events for this session
                recent_events = db.query(MonitoringEvent).filter(
                    MonitoringEvent.session_id == session.id
                ).order_by(MonitoringEvent.timestamp.desc()).limit(3).all()
                
                if recent_events:
                    st.write("**Recent Events:**")
                    for event in recent_events:
                        severity_color = {
                            'low': '🟢',
                            'medium': '🟡', 
                            'high': '🔴',
                            'critical': '🚨'
                        }.get(event.severity, '⚪')
                        
                        st.write(f"{severity_color} {event.event_type.replace('_', ' ').title()} - {event.timestamp.strftime('%H:%M:%S')}")
    
    finally:
        db.close()


def show_reports_dashboard():
    """Comprehensive reporting dashboard"""
    st.subheader("📈 Reports & Analytics")
    
    # Report type selection
    report_type = st.selectbox(
        "Select Report Type",
        ["Session Summary", "Violation Analysis", "Student Performance", "Test Analytics"]
    )
    
    # Date range filter
    col1, col2 = st.columns(2)
    with col1:
        start_date = st.date_input("Start Date", value=datetime.now().date() - timedelta(days=30))
    with col2:
        end_date = st.date_input("End Date", value=datetime.now().date())
    
    if report_type == "Session Summary":
        show_session_summary_report(start_date, end_date)
    elif report_type == "Violation Analysis":
        show_violation_analysis_report(start_date, end_date)
    elif report_type == "Student Performance":
        show_student_performance_report(start_date, end_date)
    elif report_type == "Test Analytics":
        show_test_analytics_report(start_date, end_date)


def show_session_summary_report(start_date, end_date):
    """Session summary report with detailed table"""
    st.subheader("📋 Session Summary Report")
    
    db = get_db_session()
    try:
        # Query sessions in date range
        sessions = db.query(ProctorSession).filter(
            ProctorSession.created_at >= start_date,
            ProctorSession.created_at <= end_date + timedelta(days=1)
        ).all()
        
        if not sessions:
            st.info("No sessions found in the selected date range.")
            return
        
        # Prepare data for table
        session_data = []
        for session in sessions:
            test = db.query(Test).filter(Test.id == session.test_id).first()
            student = db.query(User).filter(User.id == session.student_id).first()
            
            # Get violation count
            violation_count = db.query(MonitoringEvent).filter(
                MonitoringEvent.session_id == session.id
            ).count()
            
            session_data.append({
                'Session ID': session.id,
                'Test': test.title if test else 'Unknown',
                'Student': student.name if student else 'Unknown',
                'Status': session.status.title(),
                'Started': session.started_at.strftime('%Y-%m-%d %H:%M') if session.started_at else 'Not Started',
                'Duration (min)': round(session.total_duration / 60) if session.total_duration else 0,
                'Violations': violation_count,
                'Face Score': f"{session.face_detection_score:.1%}",
                'Attention Score': f"{session.attention_score:.1%}"
            })
        
        # Display table
        df = pd.DataFrame(session_data)
        st.dataframe(df, use_container_width=True, hide_index=True)
        
        # Download button
        csv = df.to_csv(index=False)
        st.download_button(
            label="📥 Download CSV",
            data=csv,
            file_name=f"session_summary_{start_date}_to_{end_date}.csv",
            mime="text/csv"
        )
        
        # Summary statistics
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Total Sessions", len(sessions))
        with col2:
            avg_duration = df['Duration (min)'].mean() if not df.empty else 0
            st.metric("Avg Duration", f"{avg_duration:.1f} min")
        with col3:
            total_violations = df['Violations'].sum() if not df.empty else 0
            st.metric("Total Violations", total_violations)
        with col4:
            completed_sessions = len([s for s in sessions if s.status == 'completed'])
            st.metric("Completed", completed_sessions)
    
    finally:
        db.close()


def show_violation_analysis_report(start_date, end_date):
    """Detailed violation analysis report"""
    st.subheader("🚨 Violation Analysis Report")
    
    db = get_db_session()
    try:
        # Query violations in date range
        violations = db.query(MonitoringEvent).filter(
            MonitoringEvent.timestamp >= start_date,
            MonitoringEvent.timestamp <= end_date + timedelta(days=1)
        ).all()
        
        if not violations:
            st.info("No violations found in the selected date range.")
            return
        
        # Prepare violation data
        violation_data = []
        for violation in violations:
            session = db.query(ProctorSession).filter(ProctorSession.id == violation.session_id).first()
            student = db.query(User).filter(User.id == session.student_id).first() if session else None
            
            violation_data.append({
                'Timestamp': violation.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                'Session ID': violation.session_id,
                'Student': student.name if student else 'Unknown',
                'Violation Type': violation.event_type.replace('_', ' ').title(),
                'Severity': violation.severity.title(),
                'Details': str(violation.get_event_data()) if violation.event_data else 'N/A'
            })
        
        # Display table
        df = pd.DataFrame(violation_data)
        st.dataframe(df, use_container_width=True, hide_index=True)
        
        # Download button
        csv = df.to_csv(index=False)
        st.download_button(
            label="📥 Download Violations CSV",
            data=csv,
            file_name=f"violations_{start_date}_to_{end_date}.csv",
            mime="text/csv"
        )
        
        # Violation statistics
        col1, col2 = st.columns(2)
        
        with col1:
            # Violations by type
            violation_counts = df['Violation Type'].value_counts()
            fig = px.bar(
                x=violation_counts.index,
                y=violation_counts.values,
                title="Violations by Type"
            )
            st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            # Violations by severity
            severity_counts = df['Severity'].value_counts()
            fig = px.pie(
                values=severity_counts.values,
                names=severity_counts.index,
                title="Violations by Severity"
            )
            st.plotly_chart(fig, use_container_width=True)
    
    finally:
        db.close()


def show_student_performance_report(start_date, end_date):
    """Student performance analysis"""
    st.subheader("👥 Student Performance Report")
    
    db = get_db_session()
    try:
        # Get student performance data
        students = db.query(User).filter(User.role == 'student', User.is_active == True).all()
        
        student_data = []
        for student in students:
            # Get sessions for this student
            sessions = db.query(ProctorSession).filter(
                ProctorSession.student_id == student.id,
                ProctorSession.created_at >= start_date,
                ProctorSession.created_at <= end_date + timedelta(days=1)
            ).all()
            
            if sessions:
                total_violations = sum([
                    db.query(MonitoringEvent).filter(MonitoringEvent.session_id == s.id).count()
                    for s in sessions
                ])
                
                avg_attention = sum([s.attention_score for s in sessions if s.attention_score]) / len(sessions)
                avg_face_score = sum([s.face_detection_score for s in sessions if s.face_detection_score]) / len(sessions)
                
                student_data.append({
                    'Student': student.name,
                    'Email': student.email,
                    'Total Sessions': len(sessions),
                    'Completed Sessions': len([s for s in sessions if s.status == 'completed']),
                    'Total Violations': total_violations,
                    'Avg Violations/Session': round(total_violations / len(sessions), 1),
                    'Avg Attention Score': f"{avg_attention:.1%}",
                    'Avg Face Detection': f"{avg_face_score:.1%}"
                })
        
        if student_data:
            df = pd.DataFrame(student_data)
            st.dataframe(df, use_container_width=True, hide_index=True)
            
            # Download button
            csv = df.to_csv(index=False)
            st.download_button(
                label="📥 Download Student Performance CSV",
                data=csv,
                file_name=f"student_performance_{start_date}_to_{end_date}.csv",
                mime="text/csv"
            )
        else:
            st.info("No student performance data found in the selected date range.")
    
    finally:
        db.close()


def show_test_analytics_report(start_date, end_date):
    """Test analytics and performance"""
    st.subheader("📝 Test Analytics Report")
    
    db = get_db_session()
    try:
        # Get test analytics
        tests = db.query(Test).filter(Test.is_active == True).all()
        
        test_data = []
        for test in tests:
            sessions = db.query(ProctorSession).filter(
                ProctorSession.test_id == test.id,
                ProctorSession.created_at >= start_date,
                ProctorSession.created_at <= end_date + timedelta(days=1)
            ).all()
            
            if sessions:
                total_violations = sum([
                    db.query(MonitoringEvent).filter(MonitoringEvent.session_id == s.id).count()
                    for s in sessions
                ])
                
                completion_rate = len([s for s in sessions if s.status == 'completed']) / len(sessions) * 100
                
                test_data.append({
                    'Test Title': test.title,
                    'Duration (min)': test.duration_minutes,
                    'Total Sessions': len(sessions),
                    'Completion Rate': f"{completion_rate:.1f}%",
                    'Total Violations': total_violations,
                    'Avg Violations/Session': round(total_violations / len(sessions), 1),
                    'Created': test.created_at.strftime('%Y-%m-%d')
                })
        
        if test_data:
            df = pd.DataFrame(test_data)
            st.dataframe(df, use_container_width=True, hide_index=True)
            
            # Download button
            csv = df.to_csv(index=False)
            st.download_button(
                label="📥 Download Test Analytics CSV",
                data=csv,
                file_name=f"test_analytics_{start_date}_to_{end_date}.csv",
                mime="text/csv"
            )
        else:
            st.info("No test analytics data found in the selected date range.")
    
    finally:
        db.close()


def show_session_management():
    """Session management interface"""
    st.subheader("📋 Session Management")
    
    # Session actions
    col1, col2 = st.columns(2)
    
    with col1:
        st.write("**Quick Actions**")
        if st.button("🔄 Refresh All Sessions"):
            st.rerun()
        
        if st.button("🛑 Stop All Active Sessions"):
            stop_all_active_sessions()
            st.success("All active sessions stopped.")
            st.rerun()
    
    with col2:
        st.write("**Session Statistics**")
        db = get_db_session()
        try:
            total_sessions = db.query(ProctorSession).count()
            active_sessions = db.query(ProctorSession).filter(ProctorSession.status == 'active').count()
            completed_sessions = db.query(ProctorSession).filter(ProctorSession.status == 'completed').count()
            
            st.write(f"Total Sessions: {total_sessions}")
            st.write(f"Active Sessions: {active_sessions}")
            st.write(f"Completed Sessions: {completed_sessions}")
        finally:
            db.close()
    
    # Display all sessions
    st.divider()
    show_all_sessions_table()


def show_all_sessions_table():
    """Display all sessions in a table"""
    db = get_db_session()
    try:
        sessions = db.query(ProctorSession).order_by(ProctorSession.created_at.desc()).all()
        
        if not sessions:
            st.info("No sessions found.")
            return
        
        session_data = []
        for session in sessions:
            test = db.query(Test).filter(Test.id == session.test_id).first()
            student = db.query(User).filter(User.id == session.student_id).first()
            
            session_data.append({
                'ID': session.id,
                'Test': test.title if test else 'Unknown',
                'Student': student.name if student else 'Unknown',
                'Status': session.status.title(),
                'Created': session.created_at.strftime('%Y-%m-%d %H:%M'),
                'Duration': f"{session.total_duration // 60} min" if session.total_duration else 'N/A',
                'Violations': session.total_violations
            })
        
        df = pd.DataFrame(session_data)
        st.dataframe(df, use_container_width=True, hide_index=True)
    
    finally:
        db.close()


def show_session_details(session_id):
    """Show detailed session information"""
    st.subheader(f"Session Details - ID: {session_id}")
    
    db = get_db_session()
    try:
        session = db.query(ProctorSession).filter(ProctorSession.id == session_id).first()
        if not session:
            st.error("Session not found.")
            return
        
        # Session info
        test = db.query(Test).filter(Test.id == session.test_id).first()
        student = db.query(User).filter(User.id == session.student_id).first()
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.write(f"**Test:** {test.title if test else 'Unknown'}")
            st.write(f"**Student:** {student.name if student else 'Unknown'}")
            st.write(f"**Status:** {session.status.title()}")
            st.write(f"**Token:** {session.session_token}")
        
        with col2:
            st.write(f"**Created:** {session.created_at.strftime('%Y-%m-%d %H:%M')}")
            if session.started_at:
                st.write(f"**Started:** {session.started_at.strftime('%Y-%m-%d %H:%M')}")
            if session.completed_at:
                st.write(f"**Completed:** {session.completed_at.strftime('%Y-%m-%d %H:%M')}")
            st.write(f"**Duration:** {session.total_duration // 60 if session.total_duration else 0} minutes")
        
        # Events for this session
        st.subheader("Events")
        events = db.query(MonitoringEvent).filter(
            MonitoringEvent.session_id == session_id
        ).order_by(MonitoringEvent.timestamp.desc()).all()
        
        if events:
            event_data = []
            for event in events:
                event_data.append({
                    'Timestamp': event.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                    'Type': event.event_type.replace('_', ' ').title(),
                    'Severity': event.severity.title(),
                    'Data': str(event.get_event_data()) if event.event_data else 'N/A'
                })
            
            df = pd.DataFrame(event_data)
            st.dataframe(df, use_container_width=True, hide_index=True)
        else:
            st.info("No events recorded for this session.")
    
    finally:
        db.close()


def terminate_session(session_id):
    """Terminate a session"""
    db = get_db_session()
    try:
        session = db.query(ProctorSession).filter(ProctorSession.id == session_id).first()
        if session:
            session.status = 'terminated'
            session.completed_at = datetime.now(timezone.utc)
            if session.started_at:
                session.total_duration = int((session.completed_at - session.started_at).total_seconds())
            db.commit()
    finally:
        db.close()


def stop_all_active_sessions():
    """Stop all active sessions"""
    db = get_db_session()
    try:
        active_sessions = db.query(ProctorSession).filter(ProctorSession.status == 'active').all()
        for session in active_sessions:
            session.status = 'terminated'
            session.completed_at = datetime.now(timezone.utc)
            if session.started_at:
                session.total_duration = int((session.completed_at - session.started_at).total_seconds())
        db.commit()
    finally:
        db.close()

def show_violation_dashboard():
    """Show real-time violation dashboard"""

    st.markdown("### 🚨 Live Violation Monitoring")

    # Get recent violations (last 24 hours)
    db = get_db_session()
    try:
        from datetime import datetime, timedelta
        cutoff_time = datetime.now() - timedelta(hours=24)

        violations = db.query(MonitoringEvent).filter(
            MonitoringEvent.timestamp >= cutoff_time
        ).order_by(MonitoringEvent.timestamp.desc()).limit(50).all()

        if violations:
            # Create violation summary
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                total_violations = len(violations)
                st.metric("Total Violations (24h)", total_violations)

            with col2:
                high_severity = len([v for v in violations if v.severity == 'high'])
                st.metric("High Severity", high_severity)

            with col3:
                critical_violations = len([v for v in violations if v.severity == 'critical'])
                st.metric("Critical", critical_violations, delta=critical_violations)

            with col4:
                active_sessions = db.query(ProctorSession).filter(
                    ProctorSession.status == 'active'
                ).count()
                st.metric("Active Sessions", active_sessions)

            # Violation details table
            st.markdown("#### Recent Violations")

            violation_data = []
            for violation in violations:
                # Get session info
                session = db.query(ProctorSession).filter(
                    ProctorSession.id == violation.session_id
                ).first()

                if session:
                    # Get student info
                    student = db.query(User).filter(
                        User.id == session.student_id
                    ).first()

                    # Get test info
                    test = db.query(Test).filter(
                        Test.id == session.test_id
                    ).first()

                    violation_data.append({
                        'Time': violation.timestamp.strftime('%H:%M:%S'),
                        'Student': student.name if student else 'Unknown',
                        'Test': test.title if test else 'Unknown',
                        'Violation': violation.event_type.replace('_', ' ').title(),
                        'Severity': violation.severity.upper(),
                        'Session ID': session.id
                    })

            if violation_data:
                df = pd.DataFrame(violation_data)
                st.dataframe(df, use_container_width=True, hide_index=True)

                # Flagged sessions alert
                session_violations = {}
                for violation in violations:
                    session_id = violation.session_id
                    if session_id not in session_violations:
                        session_violations[session_id] = []
                    session_violations[session_id].append(violation)

                flagged_count = 0
                for session_id, session_viols in session_violations.items():
                    high_count = len([v for v in session_viols if v.severity == 'high'])
                    critical_count = len([v for v in session_viols if v.severity == 'critical'])

                    if critical_count >= 1 or high_count >= 3:
                        flagged_count += 1

                if flagged_count > 0:
                    st.error(f"⚠️ {flagged_count} sessions require immediate review!")
                else:
                    st.success("✅ No sessions currently flagged")

        else:
            st.info("No violations recorded in the last 24 hours")

    finally:
        db.close()

def show_flagging_system_dashboard():
    """Show automated flagging system dashboard"""
    st.subheader("🚨 Automated Flagging System")

    # System controls
    col1, col2, col3 = st.columns(3)

    with col1:
        if st.button("🟢 Start Flagging System", type="primary"):
            try:
                start_flagging_system()
                st.success("Flagging system started!")
            except Exception as e:
                st.error(f"Error starting system: {e}")

    with col2:
        if st.button("🔴 Stop Flagging System"):
            try:
                stop_flagging_system()
                st.success("Flagging system stopped!")
            except Exception as e:
                st.error(f"Error stopping system: {e}")

    with col3:
        if st.button("🔄 Refresh Data"):
            st.rerun()

    st.divider()

    # Flagged sessions
    st.markdown("### 🚩 Recently Flagged Sessions")

    try:
        flagged_sessions = get_flagged_sessions(hours=24)

        if flagged_sessions:
            # Summary metrics
            col1, col2, col3 = st.columns(3)

            with col1:
                st.metric("Flagged Sessions (24h)", len(flagged_sessions))

            with col2:
                critical_count = len([s for s in flagged_sessions if 'critical' in s.get('reason', '').lower()])
                st.metric("Critical Flags", critical_count)

            with col3:
                high_count = len([s for s in flagged_sessions if 'high' in s.get('reason', '').lower()])
                st.metric("High Priority", high_count)

            # Flagged sessions table
            st.markdown("#### Flagged Session Details")

            session_data = []
            for session in flagged_sessions:
                session_data.append({
                    'Session ID': session['session_id'],
                    'Student': session['student_name'],
                    'Test': session['test_title'],
                    'Flagged At': session['flagged_at'].strftime('%Y-%m-%d %H:%M:%S'),
                    'Reason': session['reason'],
                    'Violations': str(session.get('violation_counts', {}))
                })

            if session_data:
                df = pd.DataFrame(session_data)
                st.dataframe(df, use_container_width=True, hide_index=True)

                # Action buttons for flagged sessions
                st.markdown("#### Actions")

                selected_session = st.selectbox(
                    "Select session for action:",
                    options=[s['session_id'] for s in flagged_sessions],
                    format_func=lambda x: f"Session {x} - {next(s['student_name'] for s in flagged_sessions if s['session_id'] == x)}"
                )

                col1, col2, col3 = st.columns(3)

                with col1:
                    if st.button("📋 View Details"):
                        show_session_details(selected_session)

                with col2:
                    if st.button("✅ Mark Reviewed"):
                        st.success(f"Session {selected_session} marked as reviewed")

                with col3:
                    if st.button("🚫 Terminate Session"):
                        # Terminate the session
                        db = get_db_session()
                        try:
                            session = db.query(ProctorSession).filter(
                                ProctorSession.id == selected_session
                            ).first()
                            if session:
                                session.status = 'terminated'
                                session.completed_at = datetime.now()
                                db.commit()
                                st.success(f"Session {selected_session} terminated")
                        finally:
                            db.close()

        else:
            st.info("No sessions have been flagged in the last 24 hours")

    except Exception as e:
        st.error(f"Error loading flagged sessions: {e}")

    # Flagging thresholds configuration
    st.divider()
    st.markdown("### ⚙️ Flagging Thresholds")

    with st.expander("Configure Flagging Thresholds"):
        col1, col2 = st.columns(2)

        with col1:
            critical_threshold = st.number_input("Critical Violations Threshold", min_value=1, value=1)
            high_threshold = st.number_input("High Violations Threshold", min_value=1, value=3)

        with col2:
            medium_threshold = st.number_input("Medium Violations Threshold", min_value=1, value=5)
            total_threshold = st.number_input("Total Violations Threshold", min_value=1, value=8)

        if st.button("Update Thresholds"):
            # TODO: Update thresholds in the flagging system
            st.success("Thresholds updated successfully!")

    # System status
    st.divider()
    st.markdown("### 📊 System Status")

    # Show system information
    col1, col2 = st.columns(2)

    with col1:
        st.info("**Monitoring Interval:** 30 seconds")
        st.info("**Auto-flagging:** Enabled")

    with col2:
        st.info("**Alert Methods:** Dashboard, Logs")
        st.info("**Data Retention:** 30 days")
