"""
Test Script for Enhanced Proctoring System
Tests all components of the new admin-controlled proctoring system
"""
import sys
import os
import time
import json
import requests
from datetime import datetime, timezone

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main_app import init_database, get_db_session, User, authenticate_user
from database_models import (
    Test, ProctorSession, MonitoringEvent, SessionMetrics,
    create_test, create_proctor_session, log_monitoring_event, record_session_metrics
)


class EnhancedSystemTester:
    """Test suite for the enhanced proctoring system"""
    
    def __init__(self):
        self.test_results = []
        self.admin_user_id = None
        self.student_user_id = None
        self.test_id = None
        self.session_id = None
        self.session_token = None
    
    def run_all_tests(self):
        """Run all test cases"""
        print("🧪 Testing Enhanced Proctoring System")
        print("=" * 50)
        
        # Database tests
        self.test_database_initialization()
        self.test_user_authentication()
        
        # Test management tests
        self.test_test_creation()
        self.test_session_creation()
        
        # Monitoring tests
        self.test_monitoring_events()
        self.test_session_metrics()
        
        # API tests
        self.test_api_endpoints()
        
        # Report generation tests
        self.test_report_generation()
        
        # Print results
        self.print_test_results()
    
    def test_database_initialization(self):
        """Test database initialization and table creation"""
        print("\n1. Testing Database Initialization...")
        
        try:
            # Initialize database
            result = init_database()
            
            if result:
                self.log_test("Database Initialization", True, "Database initialized successfully")
                
                # Test database connection
                db = get_db_session()
                try:
                    # Check if tables exist by querying them
                    user_count = db.query(User).count()
                    self.log_test("Database Connection", True, f"Connected successfully, {user_count} users found")
                except Exception as e:
                    self.log_test("Database Connection", False, f"Connection failed: {e}")
                finally:
                    db.close()
            else:
                self.log_test("Database Initialization", False, "Failed to initialize database")
                
        except Exception as e:
            self.log_test("Database Initialization", False, f"Exception: {e}")
    
    def test_user_authentication(self):
        """Test user authentication system"""
        print("\n2. Testing User Authentication...")
        
        try:
            # Test admin authentication
            admin_user = authenticate_user("<EMAIL>", "admin123")
            if admin_user and admin_user['role'] == 'admin':
                self.admin_user_id = admin_user['id']
                self.log_test("Admin Authentication", True, f"Admin user authenticated: {admin_user['name']}")
            else:
                self.log_test("Admin Authentication", False, "Failed to authenticate admin user")
            
            # Test student authentication
            student_user = authenticate_user("<EMAIL>", "student123")
            if student_user and student_user['role'] == 'student':
                self.student_user_id = student_user['id']
                self.log_test("Student Authentication", True, f"Student user authenticated: {student_user['name']}")
            else:
                self.log_test("Student Authentication", False, "Failed to authenticate student user")
            
            # Test invalid authentication
            invalid_user = authenticate_user("<EMAIL>", "wrongpassword")
            if invalid_user is None:
                self.log_test("Invalid Authentication", True, "Invalid credentials correctly rejected")
            else:
                self.log_test("Invalid Authentication", False, "Invalid credentials incorrectly accepted")
                
        except Exception as e:
            self.log_test("User Authentication", False, f"Exception: {e}")
    
    def test_test_creation(self):
        """Test test creation functionality"""
        print("\n3. Testing Test Creation...")
        
        try:
            if not self.admin_user_id:
                self.log_test("Test Creation", False, "No admin user available for testing")
                return
            
            # Create a test
            test_questions = [
                {
                    "question": "What is 2 + 2?",
                    "type": "multiple_choice",
                    "options": ["3", "4", "5", "6"],
                    "correct_answer": 1,
                    "points": 1
                },
                {
                    "question": "Python is a programming language.",
                    "type": "true_false",
                    "correct_answer": True,
                    "points": 1
                }
            ]
            
            test_settings = {
                "allow_tab_switch": False,
                "allow_fullscreen_exit": False,
                "require_webcam": True,
                "max_violations": 5,
                "auto_submit": True,
                "record_session": True
            }
            
            test = create_test(
                title="Test System Validation",
                description="Test created for system validation",
                duration_minutes=30,
                questions=test_questions,
                settings=test_settings,
                created_by=self.admin_user_id
            )
            
            if test and test.id:
                self.test_id = test.id
                self.log_test("Test Creation", True, f"Test created with ID: {test.id}")
                
                # Verify test data
                retrieved_questions = test.get_questions()
                retrieved_settings = test.get_settings()
                
                if len(retrieved_questions) == 2 and retrieved_settings.get('max_violations') == 5:
                    self.log_test("Test Data Integrity", True, "Test questions and settings stored correctly")
                else:
                    self.log_test("Test Data Integrity", False, "Test data not stored correctly")
            else:
                self.log_test("Test Creation", False, "Failed to create test")
                
        except Exception as e:
            self.log_test("Test Creation", False, f"Exception: {e}")
    
    def test_session_creation(self):
        """Test proctoring session creation"""
        print("\n4. Testing Session Creation...")
        
        try:
            if not self.test_id or not self.student_user_id:
                self.log_test("Session Creation", False, "Missing test or student for session creation")
                return
            
            # Create a proctoring session
            session = create_proctor_session(self.test_id, self.student_user_id)
            
            if session and session.id:
                self.session_id = session.id
                self.session_token = session.session_token
                self.log_test("Session Creation", True, f"Session created with ID: {session.id}")
                
                # Verify session data
                if session.status == 'created' and session.session_token:
                    self.log_test("Session Data", True, f"Session data correct, token: {session.session_token[:8]}...")
                else:
                    self.log_test("Session Data", False, "Session data incorrect")
            else:
                self.log_test("Session Creation", False, "Failed to create session")
                
        except Exception as e:
            self.log_test("Session Creation", False, f"Exception: {e}")
    
    def test_monitoring_events(self):
        """Test monitoring event logging"""
        print("\n5. Testing Monitoring Events...")
        
        try:
            if not self.session_id:
                self.log_test("Monitoring Events", False, "No session available for testing")
                return
            
            # Log various types of monitoring events
            events_to_test = [
                ("face_lost", {"duration": 3.5}, "high"),
                ("multiple_faces", {"face_count": 2}, "critical"),
                ("tab_switch", {"url": "https://google.com"}, "high"),
                ("fullscreen_exit", {"timestamp": datetime.now().isoformat()}, "medium")
            ]
            
            logged_events = 0
            for event_type, event_data, severity in events_to_test:
                try:
                    event = log_monitoring_event(
                        session_id=self.session_id,
                        event_type=event_type,
                        event_data=event_data,
                        severity=severity
                    )
                    if event:
                        logged_events += 1
                except Exception as e:
                    print(f"   Failed to log {event_type}: {e}")
            
            if logged_events == len(events_to_test):
                self.log_test("Monitoring Events", True, f"All {logged_events} events logged successfully")
            else:
                self.log_test("Monitoring Events", False, f"Only {logged_events}/{len(events_to_test)} events logged")
                
        except Exception as e:
            self.log_test("Monitoring Events", False, f"Exception: {e}")
    
    def test_session_metrics(self):
        """Test session metrics recording"""
        print("\n6. Testing Session Metrics...")
        
        try:
            if not self.session_id:
                self.log_test("Session Metrics", False, "No session available for testing")
                return
            
            # Record sample metrics
            metrics_data = {
                "face_detected": True,
                "face_count": 1,
                "face_confidence": 0.95,
                "looking_away": False,
                "attention_score": 0.88,
                "tab_switches": 0,
                "fullscreen_exits": 0
            }
            
            metrics = record_session_metrics(self.session_id, metrics_data)
            
            if metrics:
                self.log_test("Session Metrics", True, "Metrics recorded successfully")
                
                # Record a few more metrics to simulate monitoring
                for i in range(3):
                    time.sleep(0.1)  # Small delay
                    metrics_data["attention_score"] = 0.85 + (i * 0.02)
                    record_session_metrics(self.session_id, metrics_data)
                
                self.log_test("Multiple Metrics", True, "Multiple metrics recorded successfully")
            else:
                self.log_test("Session Metrics", False, "Failed to record metrics")
                
        except Exception as e:
            self.log_test("Session Metrics", False, f"Exception: {e}")
    
    def test_api_endpoints(self):
        """Test API endpoints"""
        print("\n7. Testing API Endpoints...")
        
        try:
            # Test if API server is running
            try:
                response = requests.get("http://localhost:5000/api/session/status", timeout=2)
                if response.status_code == 200:
                    self.log_test("API Server", True, "API server is running")
                    
                    # Test session status endpoint
                    data = response.json()
                    if 'active_sessions' in data:
                        self.log_test("Session Status API", True, f"Status API working, {data['total_active']} active sessions")
                    else:
                        self.log_test("Session Status API", False, "Status API response format incorrect")
                else:
                    self.log_test("API Server", False, f"API server returned status {response.status_code}")
                    
            except requests.exceptions.RequestException:
                self.log_test("API Server", False, "API server not running or not accessible")
                
        except Exception as e:
            self.log_test("API Endpoints", False, f"Exception: {e}")
    
    def test_report_generation(self):
        """Test report generation functionality"""
        print("\n8. Testing Report Generation...")
        
        try:
            db = get_db_session()
            try:
                # Test data retrieval for reports
                sessions = db.query(ProctorSession).all()
                events = db.query(MonitoringEvent).all()
                metrics = db.query(SessionMetrics).all()
                
                if sessions:
                    self.log_test("Session Data Retrieval", True, f"Retrieved {len(sessions)} sessions")
                else:
                    self.log_test("Session Data Retrieval", False, "No sessions found")
                
                if events:
                    self.log_test("Event Data Retrieval", True, f"Retrieved {len(events)} events")
                else:
                    self.log_test("Event Data Retrieval", False, "No events found")
                
                if metrics:
                    self.log_test("Metrics Data Retrieval", True, f"Retrieved {len(metrics)} metrics")
                else:
                    self.log_test("Metrics Data Retrieval", False, "No metrics found")
                
                # Test data aggregation
                if sessions and events:
                    session_with_events = 0
                    for session in sessions:
                        session_events = [e for e in events if e.session_id == session.id]
                        if session_events:
                            session_with_events += 1
                    
                    self.log_test("Data Aggregation", True, f"{session_with_events} sessions have associated events")
                
            finally:
                db.close()
                
        except Exception as e:
            self.log_test("Report Generation", False, f"Exception: {e}")
    
    def log_test(self, test_name, passed, message):
        """Log test result"""
        status = "✅ PASS" if passed else "❌ FAIL"
        self.test_results.append({
            'name': test_name,
            'passed': passed,
            'message': message
        })
        print(f"   {status}: {test_name} - {message}")
    
    def print_test_results(self):
        """Print summary of test results"""
        print("\n" + "=" * 50)
        print("🧪 TEST RESULTS SUMMARY")
        print("=" * 50)
        
        passed_tests = sum(1 for result in self.test_results if result['passed'])
        total_tests = len(self.test_results)
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {total_tests - passed_tests}")
        print(f"Success Rate: {(passed_tests / total_tests * 100):.1f}%")
        
        # Show failed tests
        failed_tests = [result for result in self.test_results if not result['passed']]
        if failed_tests:
            print("\n❌ FAILED TESTS:")
            for test in failed_tests:
                print(f"   - {test['name']}: {test['message']}")
        
        print("\n" + "=" * 50)
        
        if passed_tests == total_tests:
            print("🎉 ALL TESTS PASSED! The enhanced proctoring system is working correctly.")
        else:
            print("⚠️  Some tests failed. Please review the issues above.")
        
        print("=" * 50)


def create_sample_data():
    """Create sample data for testing"""
    print("\n📊 Creating Sample Data for Testing...")
    
    try:
        db = get_db_session()
        try:
            # Check if we have the required users
            admin = db.query(User).filter(User.email == "<EMAIL>").first()
            student = db.query(User).filter(User.email == "<EMAIL>").first()
            
            if not admin or not student:
                print("   ⚠️  Default users not found. Please run the main application first to create default users.")
                return False
            
            print(f"   ✅ Found admin user: {admin.name}")
            print(f"   ✅ Found student user: {student.name}")
            
            return True
            
        finally:
            db.close()
            
    except Exception as e:
        print(f"   ❌ Error creating sample data: {e}")
        return False


def main():
    """Main test function"""
    print("🚀 Enhanced Proctoring System Test Suite")
    print("=" * 50)
    
    # Create sample data
    if not create_sample_data():
        print("❌ Failed to create sample data. Exiting.")
        return
    
    # Run tests
    tester = EnhancedSystemTester()
    tester.run_all_tests()
    
    # Additional system checks
    print("\n🔧 Additional System Checks:")
    
    # Check if required files exist
    required_files = [
        "main_app.py",
        "database_models.py",
        "test_management.py",
        "background_proctor.py",
        "session_control.py",
        "admin_dashboard.py",
        "report_generator.py",
        "api_server.py"
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"   ✅ {file} exists")
        else:
            print(f"   ❌ {file} missing")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n⚠️  Missing files: {', '.join(missing_files)}")
    else:
        print("\n✅ All required files are present")
    
    print("\n🎯 Test completed!")


if __name__ == "__main__":
    main()
