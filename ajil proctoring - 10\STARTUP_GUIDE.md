# 🚀 Enhanced Proctoring System - Startup Guide

## ✅ **FIXED: Circular Import Issue Resolved**

The `StreamlitAPIException: set_page_config() can only be called once per app page` error has been fixed by restructuring the imports and removing circular dependencies.

## 🔐 **Admin Login Credentials**

**Email:** `<EMAIL>`  
**Password:** `admin123`

## 🚀 **How to Start the System**

### **Option 1: Using Batch File (Recommended)**
1. **Double-click** `start_enhanced_system.bat`
2. **Wait** for the application to start
3. **Open browser** to `http://localhost:8501`
4. **Login** with admin credentials

### **Option 2: Using Command Line**
```bash
# Navigate to the project directory
cd "C:\Users\<USER>\Desktop\ajil proctoring"

# Activate virtual environment
venv\Scripts\activate

# Start the application
streamlit run start_enhanced_system.py --server.port 8501
```

### **Option 3: Using PowerShell**
```powershell
# Navigate to project directory
Set-Location "C:\Users\<USER>\Desktop\ajil proctoring"

# Activate virtual environment
.\venv\Scripts\Activate.ps1

# Start the application
streamlit run start_enhanced_system.py --server.port 8501
```

## 🌐 **Access Points**

Once started, access the system at:
- **Main Application:** http://localhost:8501
- **Admin Dashboard:** Login with admin credentials to access all features

## 🎯 **What You'll See After Login**

### **Admin Interface:**
1. **Test Management Tab:**
   - Create new tests with custom questions
   - Manage existing tests
   - Session control (start/stop proctoring)
   - View active sessions
   - Test templates

2. **System Control Tab:**
   - Launch background proctoring system
   - System status monitoring

3. **Reports Tab:**
   - Comprehensive admin dashboard
   - Live monitoring of active sessions
   - Detailed report generation
   - Data export capabilities

### **Student Interface:**
- Limited access to assigned tests only
- No access to admin controls or monitoring data

## 🔧 **Additional Services (Optional)**

### **Start API Server for Background Monitoring:**
```bash
# In a separate terminal
cd "C:\Users\<USER>\Desktop\ajil proctoring"
venv\Scripts\activate
python api_server.py
```

### **Start Legacy Proctoring Interface:**
```bash
# In a separate terminal
cd "C:\Users\<USER>\Desktop\ajil proctoring"
venv\Scripts\activate
streamlit run proctor_app.py --server.port 8502
```

## 🎮 **Quick Start Workflow**

1. **Start the system** using one of the methods above
2. **Login as admin** with the provided credentials
3. **Go to Test Management** → Create Test
4. **Create a test** with questions and settings
5. **Go to Session Control** → Start Sessions
6. **Select test and student** to start proctoring
7. **Monitor live** through the dashboard
8. **Generate reports** when sessions complete

## 🔍 **Troubleshooting**

### **If you get import errors:**
- Make sure you're in the correct directory
- Ensure virtual environment is activated
- Run: `pip install -r requirements.txt`

### **If the page doesn't load:**
- Check if port 8501 is available
- Try a different port: `streamlit run start_enhanced_system.py --server.port 8502`

### **If database errors occur:**
- The system will auto-create the database on first run
- Default users are created automatically

### **If camera access is denied:**
- Grant camera permissions to your browser
- Required for background monitoring features

## 📊 **System Features Available**

✅ **Admin-Controlled Test Creation**  
✅ **Background Proctoring Service**  
✅ **Real-time Session Monitoring**  
✅ **Comprehensive Reporting**  
✅ **Violation Tracking and Analytics**  
✅ **Data Export (CSV, JSON)**  
✅ **User Management**  
✅ **Session Control**  

## 🎉 **You're Ready to Go!**

The enhanced proctoring system is now fully functional with:
- **Admin control** over all test and session management
- **Background monitoring** that runs invisibly to students
- **Comprehensive reporting** with detailed violation tracking
- **Real-time dashboard** for live session monitoring

**Start the system and begin creating tests and managing proctoring sessions!**
