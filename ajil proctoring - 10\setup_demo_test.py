"""
Setup a default demo test and fix existing test issues
"""
import json
from datetime import datetime, timezone, timedelta
from database_models import (
    get_db_session, create_test, assign_test_to_student, 
    User, Test, TestAssignment
)

def setup_demo_test():
    """Create and assign a default demo test"""
    
    # Demo test data
    test_data = {
        "title": "Default AI Proctoring Demo Test",
        "description": "A comprehensive demo test to showcase AI proctoring features with multiple question types.",
        "duration_minutes": 15,
        "questions": [
            {
                "id": 1,
                "type": "multiple_choice",
                "question": "What is the primary purpose of AI proctoring in online examinations?",
                "options": [
                    "To replace human proctors entirely",
                    "To monitor student behavior and detect potential cheating",
                    "To grade tests automatically",
                    "To provide technical support during exams"
                ],
                "correct_answer": 1,
                "points": 10,
                "explanation": "AI proctoring primarily monitors student behavior to detect suspicious activities and maintain exam integrity."
            },
            {
                "id": 2,
                "type": "true_false",
                "question": "Students are allowed to switch tabs or applications during a proctored exam.",
                "correct_answer": False,
                "points": 10,
                "explanation": "Tab switching is typically prohibited during proctored exams as it may indicate cheating."
            },
            {
                "id": 3,
                "type": "short_answer",
                "question": "Explain in 2-3 sentences why maintaining eye contact with the camera is important during an AI-proctored exam.",
                "points": 15,
                "sample_answer": "Maintaining eye contact with the camera helps the AI system verify the student's identity and ensures they are focused on the exam. It also helps detect if the student is looking at unauthorized materials or getting help from others. Consistent eye contact demonstrates academic integrity and compliance with exam rules."
            },
            {
                "id": 4,
                "type": "multiple_choice",
                "question": "Which of the following behaviors would likely trigger a violation in an AI-proctored exam?",
                "options": [
                    "Looking directly at the camera",
                    "Speaking to someone off-camera",
                    "Using the provided calculator",
                    "Taking notes on the allowed notepad"
                ],
                "correct_answer": 1,
                "points": 10,
                "explanation": "Speaking to someone off-camera suggests getting unauthorized help, which violates exam integrity."
            },
            {
                "id": 5,
                "type": "true_false",
                "question": "AI proctoring systems can detect if multiple people are present in the room.",
                "correct_answer": True,
                "points": 10,
                "explanation": "Modern AI proctoring systems use computer vision to detect multiple faces or people in the testing environment."
            }
        ],
        "proctoring_settings": {
            "camera_required": True,
            "microphone_required": True,
            "fullscreen_required": True,
            "tab_switching_detection": True,
            "face_detection": True,
            "multiple_person_detection": True,
            "phone_detection": True,
            "violation_threshold": 3
        }
    }
    
    print("🎯 Setting up default demo test...")
    
    # Create the test
    success, result = create_test(
        title=test_data["title"],
        description=test_data["description"],
        duration_minutes=test_data["duration_minutes"],
        questions=test_data["questions"],
        settings=test_data["proctoring_settings"],
        created_by=1  # Admin user ID
    )
    
    if not success:
        print(f"❌ Failed to create test: {result}")
        return False
    
    test_id = result
    print(f"✅ Demo test created successfully! Test ID: {test_id}")
    
    # Activate the test
    db = get_db_session()
    try:
        test = db.query(Test).filter(Test.id == test_id).first()
        if test:
            test.is_active = True
            db.commit()
            print(f"✅ Test activated successfully!")
        else:
            print(f"❌ Could not find test to activate")
    finally:
        db.close()
    
    # Assign to all students
    db = get_db_session()
    try:
        students = db.query(User).filter(
            User.role == 'student', 
            User.is_active == True
        ).all()
        
        if students:
            print(f"📤 Assigning test to {len(students)} students...")
            
            success_count = 0
            for student in students:
                success, token = assign_test_to_student(
                    test_id=test_id,
                    student_id=student.id,
                    deadline_hours=72,  # 3 days to complete
                    attempts_allowed=2
                )
                
                if success:
                    success_count += 1
                    print(f"   ✅ Assigned to {student.name} ({student.email})")
                else:
                    print(f"   ❌ Failed to assign to {student.name}: {token}")
            
            print(f"🎉 Successfully assigned test to {success_count} students!")
        else:
            print("⚠️  No students found to assign test to")
            
    finally:
        db.close()
    
    return True

def fix_existing_test():
    """Fix the existing test that was created but not showing"""
    print("\n🔧 Fixing existing test...")
    
    db = get_db_session()
    try:
        # Find the existing demo test
        test = db.query(Test).filter(Test.title == "AI Proctoring Demo Test").first()
        if test:
            print(f"Found existing test: {test.title} (ID: {test.id})")
            
            # Activate it
            test.is_active = True
            db.commit()
            print("✅ Existing test activated!")
            
            return True
        else:
            print("❌ Existing test not found")
            return False
    finally:
        db.close()

def main():
    """Main setup function"""
    print("=== DEMO TEST SETUP ===")
    
    # First try to fix existing test
    if fix_existing_test():
        print("✅ Existing test fixed!")
    
    # Create new default demo test
    if setup_demo_test():
        print("\n🎉 Demo test setup completed successfully!")
        print("\n📋 Instructions:")
        print("1. Login to student portal: http://localhost:8503")
        print("2. Use credentials: <EMAIL> / student123")
        print("3. The demo test should now be visible and available")
        print("4. Test includes 5 questions with comprehensive proctoring")
    else:
        print("\n❌ Demo test setup failed!")

if __name__ == "__main__":
    main()
