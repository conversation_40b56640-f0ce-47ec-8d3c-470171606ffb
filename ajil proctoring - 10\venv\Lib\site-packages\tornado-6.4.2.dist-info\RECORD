tornado-6.4.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
tornado-6.4.2.dist-info/LICENSE,sha256=Pd-b5cKP4n2tFDpdx27qJSIq0d1ok0oEcGTlbtL6QMU,11560
tornado-6.4.2.dist-info/METADATA,sha256=2VteVriUrhhvfNxM6k9tzSiqlRs_X304s5Icq-Z_HjQ,2556
tornado-6.4.2.dist-info/RECORD,,
tornado-6.4.2.dist-info/WHEEL,sha256=JurVQA8oFj615Jy58GXWYwMxkpZX1_PnKJ214phdUD0,99
tornado-6.4.2.dist-info/top_level.txt,sha256=5QAK1MeNpWgYdqWoU8iYlDuGB8j6NDPgx-uSUHTe0A4,8
tornado/__init__.py,sha256=DBhG7L4j8Q--NzugqmWDAtQg42DY7f9mvjsy-1mCGpM,1828
tornado/__init__.pyi,sha256=T9St7WhqEZKY0h_9IkLssxhBJrsjIHeV4n-TstEpwAE,747
tornado/__pycache__/__init__.cpython-310.pyc,,
tornado/__pycache__/_locale_data.cpython-310.pyc,,
tornado/__pycache__/auth.cpython-310.pyc,,
tornado/__pycache__/autoreload.cpython-310.pyc,,
tornado/__pycache__/concurrent.cpython-310.pyc,,
tornado/__pycache__/curl_httpclient.cpython-310.pyc,,
tornado/__pycache__/escape.cpython-310.pyc,,
tornado/__pycache__/gen.cpython-310.pyc,,
tornado/__pycache__/http1connection.cpython-310.pyc,,
tornado/__pycache__/httpclient.cpython-310.pyc,,
tornado/__pycache__/httpserver.cpython-310.pyc,,
tornado/__pycache__/httputil.cpython-310.pyc,,
tornado/__pycache__/ioloop.cpython-310.pyc,,
tornado/__pycache__/iostream.cpython-310.pyc,,
tornado/__pycache__/locale.cpython-310.pyc,,
tornado/__pycache__/locks.cpython-310.pyc,,
tornado/__pycache__/log.cpython-310.pyc,,
tornado/__pycache__/netutil.cpython-310.pyc,,
tornado/__pycache__/options.cpython-310.pyc,,
tornado/__pycache__/process.cpython-310.pyc,,
tornado/__pycache__/queues.cpython-310.pyc,,
tornado/__pycache__/routing.cpython-310.pyc,,
tornado/__pycache__/simple_httpclient.cpython-310.pyc,,
tornado/__pycache__/tcpclient.cpython-310.pyc,,
tornado/__pycache__/tcpserver.cpython-310.pyc,,
tornado/__pycache__/template.cpython-310.pyc,,
tornado/__pycache__/testing.cpython-310.pyc,,
tornado/__pycache__/util.cpython-310.pyc,,
tornado/__pycache__/web.cpython-310.pyc,,
tornado/__pycache__/websocket.cpython-310.pyc,,
tornado/__pycache__/wsgi.cpython-310.pyc,,
tornado/_locale_data.py,sha256=l3skNzYD5JdaI3wmP1hjIKGU1KH56WRYXS31EaL-PDM,4583
tornado/auth.py,sha256=un3UKva8JYu1WIF9MuCwMrzKvM9PwxSwWgqqxBRMijg,50609
tornado/autoreload.py,sha256=0F5qiBGKkM3WiG-4irXQD8um80pYxLZML-rCNZg5GCc,13486
tornado/concurrent.py,sha256=cUdO2l7QD7EkiZtl3NgCotqFy9DwRCkxUt89t_XBFF4,8579
tornado/curl_httpclient.py,sha256=r8HU2_66MKCtp77-3PNkV3tQnapPjydlIwOjmSJNwDI,25450
tornado/escape.py,sha256=XTNUd70nEDKEN0vyjOENXsvtQLqpmuKaq2Pxbh0fyh0,14660
tornado/gen.py,sha256=qzJlMagib8YechXjuEJHqt5dSubRAanJcoXf36kUTrU,32564
tornado/http1connection.py,sha256=aa0yc7HeV2Bub49YNtAZm5JMw2LIUNKIJ2ueGW0alaU,38574
tornado/httpclient.py,sha256=iSQyW7Cpkkw_mSQoWCKzGlmfEDklqkj3CgPADxvMvPE,32709
tornado/httpserver.py,sha256=TMINrHYyDKD9cMhCC-Eqhpf-0in_2PU42USS0kMRJu0,16547
tornado/httputil.py,sha256=PBmMJSIjKuZSwTvYzDBJLvDAUSwxNvOkBHWPSBjKe7c,36763
tornado/ioloop.py,sha256=5Q8QSDU0OKA6xFD2_w7k8BX-PruiuqfnwvxYAJFfOdA,37796
tornado/iostream.py,sha256=eBYG8Ci9SFxc4yI-k3Ju5m_JjCwj7H6r2AA8qapIL2o,66039
tornado/locale.py,sha256=Aj9_PQ47oqyDTdtbvlNPA5xfAQmJEWJ8jboQixyezDU,21741
tornado/locks.py,sha256=gUWx_OgB6GNMnFQnZbF8jj8ojs28BDx2IIgpVzeFQAU,17928
tornado/log.py,sha256=Nwj8MO2tobSnJrmxQGQxT_RkuCMbiSoXiWtplJ3VMGk,12892
tornado/netutil.py,sha256=W_v-eAzhHXJN76pyIAYsJ-Egq9m9L1Y7JQszdPtqt54,25716
tornado/options.py,sha256=Vn5GtE0claeYlgUWgXSjUHl3NfTQjyej9uR6erFXI94,27004
tornado/platform/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
tornado/platform/__pycache__/__init__.cpython-310.pyc,,
tornado/platform/__pycache__/asyncio.cpython-310.pyc,,
tornado/platform/__pycache__/caresresolver.cpython-310.pyc,,
tornado/platform/__pycache__/twisted.cpython-310.pyc,,
tornado/platform/asyncio.py,sha256=uDZjwNYU3GbHPS78Q9sr_n90a8M2q2OIAatov1qjw7k,27451
tornado/platform/caresresolver.py,sha256=z8GZ7dQtBO0Z5cgnFxdnDzCu6qO87p7Iof3yR-y406A,3594
tornado/platform/twisted.py,sha256=QOXwMqpClrrzBUT8siEnxY4wjLVFmG5XGgZ9fXOERFo,5774
tornado/process.py,sha256=JMWTnGSkSQkmOIHTzg9Yg9aP-5O-2wybv1i3poUU7Po,13075
tornado/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
tornado/queues.py,sha256=FKhSkaDeyhT5PXZmbtd9vPP0xz4iXSdB7gKK3-WTYB4,12952
tornado/routing.py,sha256=FUSGohZnyOCeXRzFJdtauJcZzUWVBPAB_1K-ocZ-30g,25799
tornado/simple_httpclient.py,sha256=oOQoCn59LDNFrbtcN4eO39nKk_zFjpiVVK9NTAziEYk,28502
tornado/speedups.pyd,sha256=YiSy_oYnzjsrS_Kfjybim1JvCYNBugK-jN4j1ch5454,11264
tornado/speedups.pyi,sha256=bOe2V3bnhkOnnqPaO2BDHjDCYuWt8ADYeUNiP73KQbI,60
tornado/tcpclient.py,sha256=SuOynN_e0TpLT9sRIctcZoXGBZC6TLOyOKPJMSHTwYM,12484
tornado/tcpserver.py,sha256=ulqgVBohT5X1rqztXRsAP9B2BrsqEIW7B1-Ws_kaIQs,15423
tornado/template.py,sha256=n51HodBA5BzsQfcHaL0ycw-gRhiAk1D9HYf109aUHnE,38840
tornado/test/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
tornado/test/__main__.py,sha256=nKmboJh9T33b81ngbWWYsa1DQayqfZ3-hHmudIN-4iA,349
tornado/test/__pycache__/__init__.cpython-310.pyc,,
tornado/test/__pycache__/__main__.cpython-310.pyc,,
tornado/test/__pycache__/asyncio_test.cpython-310.pyc,,
tornado/test/__pycache__/auth_test.cpython-310.pyc,,
tornado/test/__pycache__/autoreload_test.cpython-310.pyc,,
tornado/test/__pycache__/circlerefs_test.cpython-310.pyc,,
tornado/test/__pycache__/concurrent_test.cpython-310.pyc,,
tornado/test/__pycache__/curl_httpclient_test.cpython-310.pyc,,
tornado/test/__pycache__/escape_test.cpython-310.pyc,,
tornado/test/__pycache__/gen_test.cpython-310.pyc,,
tornado/test/__pycache__/http1connection_test.cpython-310.pyc,,
tornado/test/__pycache__/httpclient_test.cpython-310.pyc,,
tornado/test/__pycache__/httpserver_test.cpython-310.pyc,,
tornado/test/__pycache__/httputil_test.cpython-310.pyc,,
tornado/test/__pycache__/import_test.cpython-310.pyc,,
tornado/test/__pycache__/ioloop_test.cpython-310.pyc,,
tornado/test/__pycache__/iostream_test.cpython-310.pyc,,
tornado/test/__pycache__/locale_test.cpython-310.pyc,,
tornado/test/__pycache__/locks_test.cpython-310.pyc,,
tornado/test/__pycache__/log_test.cpython-310.pyc,,
tornado/test/__pycache__/netutil_test.cpython-310.pyc,,
tornado/test/__pycache__/options_test.cpython-310.pyc,,
tornado/test/__pycache__/process_test.cpython-310.pyc,,
tornado/test/__pycache__/queues_test.cpython-310.pyc,,
tornado/test/__pycache__/resolve_test_helper.cpython-310.pyc,,
tornado/test/__pycache__/routing_test.cpython-310.pyc,,
tornado/test/__pycache__/runtests.cpython-310.pyc,,
tornado/test/__pycache__/simple_httpclient_test.cpython-310.pyc,,
tornado/test/__pycache__/tcpclient_test.cpython-310.pyc,,
tornado/test/__pycache__/tcpserver_test.cpython-310.pyc,,
tornado/test/__pycache__/template_test.cpython-310.pyc,,
tornado/test/__pycache__/testing_test.cpython-310.pyc,,
tornado/test/__pycache__/twisted_test.cpython-310.pyc,,
tornado/test/__pycache__/util.cpython-310.pyc,,
tornado/test/__pycache__/util_test.cpython-310.pyc,,
tornado/test/__pycache__/web_test.cpython-310.pyc,,
tornado/test/__pycache__/websocket_test.cpython-310.pyc,,
tornado/test/__pycache__/wsgi_test.cpython-310.pyc,,
tornado/test/asyncio_test.py,sha256=PkCspokz8QUFh17L19V_iXqExtgfyGXbSZv-UA-p2C0,10742
tornado/test/auth_test.py,sha256=rheQm9VmfYqPyU4ho__qn8KCHMdOGwmroeJj3xTc51M,23955
tornado/test/autoreload_test.py,sha256=8IXIDmejTXwCi2Diia4Sn_zjGh5pce5UEO4xGmYB7WM,9235
tornado/test/circlerefs_test.py,sha256=3532aG3KG267hmSZ9mf8nwjDk_VhG9RKZaXJIX3hAus,7554
tornado/test/concurrent_test.py,sha256=b0aEhpv99XmWlvygtpSr5SfwxJwSqtWlzCD0Pu6Jw5A,6261
tornado/test/csv_translations/fr_FR.csv,sha256=WCBvcW_OFKBYEXpZsvGKNtirg_A0PnEgomN6wqicCjQ,19
tornado/test/curl_httpclient_test.py,sha256=K-G2shCyQsm9N8gGn3nN19lSfYxfsAMa4abCZJz_oio,4432
tornado/test/escape_test.py,sha256=tdu_eYb04cUWK4lunPRgLsx_gWYudnjnbgQs4Vu9giU,12660
tornado/test/gen_test.py,sha256=6EiALoMpssQ8vVIarcuBeQVWmO3SH04PRU2NTMH7OPo,35222
tornado/test/gettext_translations/fr_FR/LC_MESSAGES/tornado_test.mo,sha256=fl0ZVZIlNwwU9lPx29pgZ4X-HfyEVYphJu7UWtll7jo,665
tornado/test/gettext_translations/fr_FR/LC_MESSAGES/tornado_test.po,sha256=bk7tSxkdiaEJw1rC8z8fASaBPGrHoWllfESGJlTdBfY,1096
tornado/test/http1connection_test.py,sha256=xioZ0K-aQO0R-G8tTX_KEld7ZFqRneALCpwcjCaEJDg,2025
tornado/test/httpclient_test.py,sha256=6uvHYtLyugcp8KEDT79H7c2MrbL-2L1u2m-NIoyX7vI,36925
tornado/test/httpserver_test.py,sha256=WGuwjaTt9IGzzY6ManEy1iQeMIOvy4RCNG8T-L39mfg,53727
tornado/test/httputil_test.py,sha256=MpecUsCtdOwqd61C4eAdIGuhSFkhC2yC8fpycEjTwag,23243
tornado/test/import_test.py,sha256=GvCxO0fGqAlxesNv61W05gcPwjKnQ20WODacFhoE9N4,2296
tornado/test/ioloop_test.py,sha256=ZfkQQig8Jik6tR1-wBnoJ-jkpTyrSOyIbZsq4Ize2eQ,28282
tornado/test/iostream_test.py,sha256=MfBeqES9dUyjqui-sF-Q9oH7vxWRcF2eWTyQ8Oa4QfY,51939
tornado/test/locale_test.py,sha256=pbow499UmnEo3V9tTI_5IE5skNxEi2zI9glZj0NVivQ,6518
tornado/test/locks_test.py,sha256=JopiG6YQc-yUnc7A782ahuokfWtUcafK5MQ5QngnyYs,17545
tornado/test/log_test.py,sha256=JyRYuGG4rT9ktLlNE48-kZN4tlaqHxNAqZv2zsDrsKc,9151
tornado/test/netutil_test.py,sha256=YScEEga-0108WkKJDoMP7JhYvmpplVf1BiFy8h_F3n8,8575
tornado/test/options_test.cfg,sha256=EOZYgjTj0VrCGCE-pzfIkvWyq4MJHr0lnnuFWUlSdYc,76
tornado/test/options_test.py,sha256=anbYFPZ2S1uDIzSaLTZccBXL6PRGtdXXFSMN5jymxV4,12261
tornado/test/options_test_types.cfg,sha256=FMeZsOIjC9bTDJvvK1PTevtjhT_Ho9QzZwVl3oZ5EoA,308
tornado/test/options_test_types_str.cfg,sha256=251O0w2a_NblokfjBmjnUid4Onz7GcNtX8zHURVY-Kc,181
tornado/test/process_test.py,sha256=psxF68Gn7KZ_ZCjPDItW8mlUvEuJdMHQpKLgkjvHfVA,11582
tornado/test/queues_test.py,sha256=9Iqae8cmBY5nkaEkDTGO2oAFTwVdqI4Qe5dTheUOIKY,14412
tornado/test/resolve_test_helper.py,sha256=tIjwg6JPRFga0yZN7XvL0I3WwduSuRwUXOHwK9MYXVQ,420
tornado/test/routing_test.py,sha256=PWrg9CmNpPNwS-95CY246AuD6rM8TtQD29EbxQcW5q8,9103
tornado/test/runtests.py,sha256=26xbcdptdFMztwFI1xSs9JnzoKv09Om9sZpyd1oNS7g,7201
tornado/test/simple_httpclient_test.py,sha256=bkMIns370dfwA7CwS_UQePl4ejfG7iCoahpJg0ag7A4,31945
tornado/test/static/dir/index.html,sha256=tBwBanUSjISUy0BVan_QNKkYdLau8qs__P0G9oAiP78,18
tornado/test/static/robots.txt,sha256=Mx6pCQ2wyfb1l72YQP1bFxgw9uCzuhyyTfqR8Mla7cE,26
tornado/test/static/sample.xml,sha256=NgwvWJr3wwdNCC4hp9wmFKhEUeolS7F9gbppMAEl8lg,689
tornado/test/static/sample.xml.bz2,sha256=2Ql5ccWnaSpDdTyioino7Bw_dcGFkG_RQO5Lm5cfT6A,285
tornado/test/static/sample.xml.gz,sha256=_App0wKpn31lZVA9P_rslytPm4ei5GvNPVKh55r7l28,264
tornado/test/static_foo.txt,sha256=0KHl2RJM2Fu0H6EB98vDTgu18F0i5YhAp7dlI3BYiF8,97
tornado/test/tcpclient_test.py,sha256=O2dKO6OIDbkqhFVzFvbs6z5vybeTAHYdrjp1gCCjZFM,17286
tornado/test/tcpserver_test.py,sha256=zDYmNcX5J9UBz_RKwqy1azuH_DAc2ANPnvsl8Yh_WhE,7950
tornado/test/template_test.py,sha256=ETUVPEUHojFbjfX4ZUs_PS179f0GhpuiO4IE4udlBRI,19193
tornado/test/templates/utf8.html,sha256=9d1eiaw5KCjUTCbRRIl_RLSy0LCJXaO-bzVF2L_32fM,7
tornado/test/test.crt,sha256=aMFU97HLkzfbkXpL35GylbVaW7fTguNk_--sVtnLIZM,1060
tornado/test/test.key,sha256=7KG8QypIxRhd6UjNYJPephukU_HIHRZ5EV8QPzrdEGI,1736
tornado/test/testing_test.py,sha256=Jb-siypQZpIHbetnetyzADl7hQVWTq7P21PzhbM3pfM,10849
tornado/test/twisted_test.py,sha256=Em5b3Q6avHixS_9d3yuPXis3w2GDAZWBPn7BpQvDT34,1749
tornado/test/util.py,sha256=36N59fAKxMw6qMLPliiVHdYB0AEC-JhAw0NR8reF0Po,3768
tornado/test/util_test.py,sha256=tG2GdUdRhYg5Ne3SwNST7u9svduHufNi33G0HYDDNkw,10079
tornado/test/web_test.py,sha256=O3TaxCZ3-rsJ0FP3hsD36ClK3Otf0rzGUSzwejJ5Tlk,124955
tornado/test/websocket_test.py,sha256=rp6Xu1Cm413lVwcOuiGlodwEbwzdeYI4WKqnU274ASQ,30319
tornado/test/wsgi_test.py,sha256=xzRgQAoSzwpJfy5SKgIplhJZQRSu3AJ-LRS7HYrgj38,4034
tornado/testing.py,sha256=nB78FbMzW9O0wuqh38HFrmMb4jqeE452lye7ODCeaak,33712
tornado/util.py,sha256=GuoZYCmtCWysdt1-_C28yyG4wEEi_gpPTNiaEYN54hY,16711
tornado/web.py,sha256=i8LAkmblUpokhy0CPaipflOZs-AgIB4nJ5H5ilCDnnI,146964
tornado/websocket.py,sha256=MrqXv4nEIeK4wrbQzkIJbYj8zdeY-9tQ4BUh5HjwUSA,63384
tornado/wsgi.py,sha256=skFfkZKiGuc4yMV47tpqcgq1ai3iHHWNM3zvsrgCeo8,11085
