"""
Student Test Application
Dedicated app for students to take tests
Run with: streamlit run student_app.py --server.port 8503
"""
import streamlit as st

# Page configuration - must be first
st.set_page_config(
    page_title="Student Test Portal - Ajil Proctoring",
    page_icon="📝",
    layout="wide",
    initial_sidebar_state="collapsed"
)

from student_test_interface import show_student_test_interface

# Hide streamlit menu and footer for cleaner test interface
hide_streamlit_style = """
<style>
#MainMenu {visibility: hidden;}
footer {visibility: hidden;}
header {visibility: hidden;}
.stDeployButton {display:none;}
</style>
"""
st.markdown(hide_streamlit_style, unsafe_allow_html=True)

# Main application
def main():
    show_student_test_interface()

if __name__ == "__main__":
    main()
