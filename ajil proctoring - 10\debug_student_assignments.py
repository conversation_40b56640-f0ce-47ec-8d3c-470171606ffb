"""
Debug script to simulate exactly what the student portal does
"""
from database_models import get_db_session, User, Test, TestAssignment, get_student_assignments
from datetime import datetime, timezone

def debug_student_portal():
    """Simulate the exact student portal logic"""
    db = get_db_session()
    try:
        print("=== STUDENT PORTAL DEBUG ===")
        
        # Get student (same as login process)
        student = db.query(User).filter(
            User.email == '<EMAIL>',
            User.role == 'student',
            User.is_active == True
        ).first()
        
        if not student:
            print("❌ Student not found!")
            return
            
        print(f"✅ Student found: ID {student.id}, Name: {student.name}")
        
        # Get assignments using the same function as student portal
        print(f"\n📋 Getting assignments for student ID: {student.id}")
        assignments = get_student_assignments(student.id)
        
        print(f"📊 Total assignments found: {len(assignments)}")
        
        if not assignments:
            print("❌ No assignments found - this is the issue!")
            return
        
        # Check each assignment (same logic as student portal)
        available_assignments = [a for a in assignments if a.status in ['assigned', 'started']]
        completed_assignments = [a for a in assignments if a.status == 'completed']
        expired_assignments = [a for a in assignments if a.status == 'expired']
        
        print(f"📋 Available assignments: {len(available_assignments)}")
        print(f"✅ Completed assignments: {len(completed_assignments)}")
        print(f"⏰ Expired assignments: {len(expired_assignments)}")
        
        # Check each available assignment in detail
        for i, assignment in enumerate(available_assignments):
            print(f"\n--- Assignment {i+1} ---")
            print(f"Assignment ID: {assignment.id}")
            print(f"Test ID: {assignment.test_id}")
            print(f"Status: {assignment.status}")
            print(f"Deadline: {assignment.deadline}")
            print(f"Deadline type: {type(assignment.deadline)}")
            print(f"Deadline timezone: {assignment.deadline.tzinfo}")
            
            # Get test (same as student portal)
            test = db.query(Test).filter(Test.id == assignment.test_id).first()
            if not test:
                print("❌ Test not found!")
                continue
                
            print(f"✅ Test found: {test.title}")
            print(f"Test active: {test.is_active}")
            
            # Check time calculation (same as student portal)
            current_time = datetime.now(timezone.utc)
            assignment_deadline = assignment.deadline
            if assignment_deadline.tzinfo is None:
                assignment_deadline = assignment_deadline.replace(tzinfo=timezone.utc)
                print("⚠️  Deadline was timezone-naive, converted to UTC")
            
            print(f"Current time: {current_time}")
            print(f"Assignment deadline (adjusted): {assignment_deadline}")
            
            time_remaining = assignment_deadline - current_time
            hours_remaining = int(time_remaining.total_seconds() // 3600)
            minutes_remaining = int((time_remaining.total_seconds() % 3600) // 60)
            
            print(f"Time remaining: {hours_remaining}h {minutes_remaining}m")
            print(f"Total seconds remaining: {time_remaining.total_seconds()}")
            
            # Check if can start test (same logic as student portal)
            can_start = (
                assignment.attempts_used < assignment.attempts_allowed and
                current_time < assignment_deadline
            )
            print(f"Can start test: {can_start}")
            print(f"  - Attempts used: {assignment.attempts_used}/{assignment.attempts_allowed}")
            print(f"  - Deadline not passed: {current_time < assignment_deadline}")
            
    finally:
        db.close()

if __name__ == "__main__":
    debug_student_portal()
