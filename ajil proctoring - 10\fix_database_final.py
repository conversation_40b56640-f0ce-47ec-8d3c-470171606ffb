"""
Fix Database Issues - Final Version
Recreates database with proper schema and fixes all column issues
"""
import os
import sqlite3
import hashlib
from database_models import Base, engine, get_db_session, User

def fix_database_completely():
    """Completely fix all database issues"""
    print("🔧 Fixing all database issues...")
    
    # Remove existing database files
    db_files = ['proctoring_system.db', 'users.db']
    for db_file in db_files:
        if os.path.exists(db_file):
            os.remove(db_file)
            print(f"🗑️ Removed {db_file}")
    
    # Create all tables with proper schema
    print("🏗️ Creating database with proper schema...")
    Base.metadata.create_all(engine)
    print("✅ All tables created successfully")
    
    # Create sample users
    db = get_db_session()
    try:
        # Check if users already exist
        existing_admin = db.query(User).filter(User.email == "<EMAIL>").first()
        if not existing_admin:
            # Create admin user
            admin_password = hashlib.sha256("admin123".encode()).hexdigest()
            admin = User(
                name="Admin User",
                email="<EMAIL>",
                password_hash=admin_password,
                role="admin",
                is_active=True
            )
            db.add(admin)
            print("✅ Created admin user")
        
        # Check if student exists
        existing_student = db.query(User).filter(User.email == "<EMAIL>").first()
        if not existing_student:
            # Create student users
            student_password = hashlib.sha256("student123".encode()).hexdigest()
            
            students = [
                User(
                    name="Test Student",
                    email="<EMAIL>",
                    password_hash=student_password,
                    role="student",
                    is_active=True
                ),
                User(
                    name="John Doe",
                    email="<EMAIL>",
                    password_hash=student_password,
                    role="student",
                    is_active=True
                ),
                User(
                    name="Jane Smith",
                    email="<EMAIL>",
                    password_hash=student_password,
                    role="student",
                    is_active=True
                )
            ]
            
            for student in students:
                db.add(student)
            
            print("✅ Created student users")
        
        db.commit()
        
        # Verify table structure
        print("🔍 Verifying table structure...")
        
        # Connect directly to check tables
        conn = sqlite3.connect('proctoring_system.db')
        cursor = conn.cursor()
        
        # Check monitoring_events table
        cursor.execute("PRAGMA table_info(monitoring_events)")
        columns = cursor.fetchall()
        print(f"📊 monitoring_events columns: {[col[1] for col in columns]}")
        
        # Check if event_data column exists
        column_names = [col[1] for col in columns]
        if 'event_data' not in column_names:
            print("⚠️ event_data column missing, adding it...")
            cursor.execute("ALTER TABLE monitoring_events ADD COLUMN event_data TEXT")
            conn.commit()
            print("✅ Added event_data column")
        
        conn.close()
        
        print("🎉 Database fixed successfully!")
        print("📋 User accounts available:")
        print("   Admin: <EMAIL> / admin123")
        print("   Student: <EMAIL> / student123")
        print("   Student: <EMAIL> / student123")
        print("   Student: <EMAIL> / student123")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    fix_database_completely()
