# Proctoring System with Login

This system now includes a secure login interface that redirects to the proctoring application without modifying the original proctor app.

## Features

- **Secure Authentication**: Email and password-based login system
- **User Registration**: New users can create accounts
- **Session Management**: Secure session handling with Streamlit
- **Default Accounts**: Pre-configured admin and student accounts
- **Seamless Redirect**: Launches proctoring system after successful login

## Quick Start

### Method 1: Using Batch File (Windows)
```bash
# Double-click or run in command prompt
run_login.bat
```

### Method 2: Using PowerShell (Windows)
```powershell
# Right-click and "Run with PowerShell" or run in PowerShell
.\run_login.ps1
```

### Method 3: Manual Command
```bash
python -m streamlit run main_app.py --server.port 8501
```

## Default Login Credentials

### Admin Account
- **Email**: <EMAIL>
- **Password**: admin123

### Student Account
- **Email**: <EMAIL>
- **Password**: student123

## How It Works

1. **Login Page**: Users enter email and password
2. **Authentication**: Credentials are verified against stored user data
3. **Dashboard**: After login, users see a welcome dashboard
4. **Launch Proctoring**: Click "Launch Proctoring System" to start the proctor app
5. **Automatic Redirect**: The original proctor app opens on port 8502

## File Structure

```
├── main_app.py          # Main login application
├── proctor_app.py       # Original proctoring system (unchanged)
├── users.json           # User credentials storage (auto-created)
├── run_login.bat        # Windows batch launcher
├── run_login.ps1        # PowerShell launcher
└── LOGIN_README.md      # This file
```

## User Management

### Adding New Users
1. Use the "Register" tab on the login page
2. Fill in name, email, and password
3. Password must be at least 6 characters
4. New users are automatically assigned "student" role

### User Data Storage
- User credentials are stored in `users.json`
- Passwords are hashed using SHA-256
- In production, consider using a proper database

## Security Features

- **Password Hashing**: All passwords are hashed before storage
- **Session Management**: Secure session state handling
- **Input Validation**: Email and password validation
- **Role-based Access**: Support for different user roles

## Accessing the Systems

### Login System
- **URL**: http://localhost:8501
- **Purpose**: User authentication and dashboard

### Proctoring System
- **URL**: http://localhost:8502 (auto-launched)
- **Purpose**: Camera monitoring and proctoring features

## Troubleshooting

### Port Already in Use
If you get a port error, try:
```bash
# For login system
python -m streamlit run main_app.py --server.port 8503

# For proctoring system
python -m streamlit run proctor_app.py --server.port 8504
```

### Missing Dependencies
Make sure you have all required packages:
```bash
pip install streamlit opencv-python mediapipe plotly pandas
```

### Cannot Launch Proctoring System
If the automatic launch fails:
1. Manually run: `python -m streamlit run proctor_app.py --server.port 8502`
2. Or click the provided link in the dashboard

## Customization

### Adding More Default Users
Edit the `create_default_users()` function in `main_app.py`:

```python
default_users = {
    "<EMAIL>": {
        "password": hash_password("your_password"),
        "name": "Your Name",
        "role": "student"  # or "admin"
    }
}
```

### Changing Ports
Modify the port numbers in:
- `main_app.py` (line with subprocess.Popen)
- `run_login.bat`
- `run_login.ps1`

## Production Considerations

For production deployment:
1. Use a proper database instead of JSON files
2. Implement HTTPS/SSL
3. Add password complexity requirements
4. Implement session timeouts
5. Add logging and monitoring
6. Use environment variables for sensitive data

## Support

If you encounter any issues:
1. Check that all dependencies are installed
2. Ensure ports 8501 and 8502 are available
3. Verify camera permissions are granted
4. Check the console for error messages
