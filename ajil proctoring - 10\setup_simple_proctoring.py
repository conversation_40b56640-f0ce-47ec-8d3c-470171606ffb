"""
Simple Proctoring Setup Implementation
Step-by-step setup for efficient proctoring with admin reporting
"""
import streamlit as st
import cv2
import time
import threading
from datetime import datetime, timezone
from database_models import (
    create_proctor_session, log_monitoring_event, 
    get_session_by_token, ProctorSession, get_db_session
)

class SimpleProctoringManager:
    """Simplified proctoring manager for easy integration"""
    
    def __init__(self):
        self.session = None
        self.monitoring_active = False
        self.camera = None
        self.violation_count = 0
        self.face_cascade = None
        
        # Load face detection
        try:
            self.face_cascade = cv2.CascadeClassifier(
                cv2.data.haarcascades + 'haarcascade_frontalface_default.xml'
            )
        except Exception as e:
            st.error(f"Face detection setup failed: {e}")
    
    def start_proctoring_session(self, test_id, student_id):
        """Start a new proctoring session"""
        try:
            # Create proctoring session
            self.session = create_proctor_session(test_id, student_id)
            
            # Start session
            db = get_db_session()
            try:
                session_obj = db.query(ProctorSession).filter(
                    ProctorSession.id == self.session.id
                ).first()
                if session_obj:
                    session_obj.start_session()
                    db.commit()
            finally:
                db.close()
            
            # Initialize camera
            self.camera = cv2.VideoCapture(0)
            self.monitoring_active = True
            
            return True, self.session.session_token
            
        except Exception as e:
            return False, str(e)
    
    def check_camera_access(self):
        """Check if camera is accessible"""
        try:
            cap = cv2.VideoCapture(0)
            ret, frame = cap.read()
            cap.release()
            return ret
        except:
            return False
    
    def detect_violations(self, frame):
        """Simple violation detection"""
        violations = []
        
        if self.face_cascade is None:
            return violations
        
        # Convert to grayscale for face detection
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        faces = self.face_cascade.detectMultiScale(gray, 1.1, 4)
        
        # Check for violations
        if len(faces) == 0:
            violations.append({
                'type': 'face_lost',
                'severity': 'high',
                'data': {'timestamp': datetime.now().isoformat()}
            })
        elif len(faces) > 1:
            violations.append({
                'type': 'multiple_faces',
                'severity': 'critical',
                'data': {'face_count': len(faces)}
            })
        
        return violations
    
    def log_violation(self, violation):
        """Log a violation to the database"""
        if not self.session:
            return False
        
        try:
            log_monitoring_event(
                session_id=self.session.id,
                event_type=violation['type'],
                event_data=violation['data'],
                severity=violation['severity']
            )
            self.violation_count += 1
            return True
        except Exception as e:
            st.error(f"Failed to log violation: {e}")
            return False
    
    def stop_proctoring_session(self):
        """Stop the proctoring session"""
        try:
            self.monitoring_active = False
            
            if self.camera:
                self.camera.release()
            
            if self.session:
                # Complete session
                db = get_db_session()
                try:
                    session_obj = db.query(ProctorSession).filter(
                        ProctorSession.id == self.session.id
                    ).first()
                    if session_obj:
                        session_obj.complete_session()
                        session_obj.total_violations = self.violation_count
                        db.commit()
                finally:
                    db.close()
            
            return True
        except Exception as e:
            st.error(f"Failed to stop session: {e}")
            return False

def setup_proctoring_ui():
    """Setup proctoring UI components"""
    
    st.markdown("""
    <style>
    .proctoring-status {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1rem;
        border-radius: 10px;
        text-align: center;
        margin: 1rem 0;
    }
    
    .violation-alert {
        background: #ff6b6b;
        color: white;
        padding: 0.5rem;
        border-radius: 5px;
        margin: 0.5rem 0;
    }
    
    .camera-preview {
        border: 2px solid #667eea;
        border-radius: 10px;
        padding: 1rem;
        text-align: center;
    }
    </style>
    """, unsafe_allow_html=True)

def create_proctoring_checklist():
    """Create pre-test proctoring checklist"""
    
    st.markdown("### 🔍 Pre-Test Proctoring Checklist")
    
    checklist_items = [
        ("Camera Access", "check_camera"),
        ("Microphone Access", "check_microphone"),
        ("Fullscreen Mode", "check_fullscreen"),
        ("Close Other Applications", "check_apps"),
        ("Stable Internet Connection", "check_internet")
    ]
    
    all_checks_passed = True
    
    for item, key in checklist_items:
        if key == "check_camera":
            # Actual camera check
            manager = SimpleProctoringManager()
            camera_ok = manager.check_camera_access()
            if camera_ok:
                st.success(f"✅ {item}")
            else:
                st.error(f"❌ {item}")
                all_checks_passed = False
        else:
            # Placeholder checks (would be implemented with JavaScript)
            st.success(f"✅ {item}")
    
    return all_checks_passed

def main():
    """Main setup function"""
    st.title("🎯 Simple Proctoring Setup")
    
    st.markdown("""
    ## Implementation Steps:
    
    ### Phase 1: Basic Setup
    1. **Camera & Microphone Access**
    2. **Face Detection Integration**
    3. **Violation Logging**
    4. **Session Management**
    
    ### Phase 2: Test Integration
    1. **Pre-test Checklist**
    2. **Real-time Monitoring**
    3. **Violation Alerts**
    4. **Session Completion**
    
    ### Phase 3: Admin Reporting
    1. **Violation Dashboard**
    2. **Session Reports**
    3. **Flagged Session Alerts**
    4. **Automated Recommendations**
    """)
    
    # Demo the checklist
    if st.button("🔍 Test Proctoring Checklist"):
        with st.expander("Proctoring Checklist", expanded=True):
            all_good = create_proctoring_checklist()
            
            if all_good:
                st.success("🎉 All checks passed! Ready for proctored test.")
            else:
                st.error("⚠️ Some checks failed. Please resolve issues before starting test.")

if __name__ == "__main__":
    main()
