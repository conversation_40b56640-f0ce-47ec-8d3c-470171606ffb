{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Real-time Proctoring System\n", "\n", "This notebook implements a real-time proctoring system using computer vision to monitor user attention and detect distractions."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: streamlit in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (1.44.1)\n", "Requirement already satisfied: opencv-python-headless in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (*********)\n", "Requirement already satisfied: mediapipe in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (0.10.5)\n", "Requirement already satisfied: numpy in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (1.26.4)\n", "Requirement already satisfied: plotly in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (6.0.1)\n", "Requirement already satisfied: ipywidgets in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (8.1.5)\n", "Requirement already satisfied: altair<6,>=4.0 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from streamlit) (5.5.0)\n", "Requirement already satisfied: blinker<2,>=1.0.0 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from streamlit) (1.9.0)\n", "Requirement already satisfied: cachetools<6,>=4.0 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from streamlit) (5.5.1)\n", "Requirement already satisfied: click<9,>=7.0 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from streamlit) (8.1.7)\n", "Requirement already satisfied: packaging<25,>=20 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from streamlit) (24.2)\n", "Requirement already satisfied: pandas<3,>=1.4.0 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from streamlit) (2.2.3)\n", "Requirement already satisfied: pillow<12,>=7.1.0 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from streamlit) (11.1.0)\n", "Requirement already satisfied: protobuf<6,>=3.20 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from streamlit) (3.20.3)\n", "Requirement already satisfied: pyarrow>=7.0 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from streamlit) (19.0.1)\n", "Requirement already satisfied: requests<3,>=2.27 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from streamlit) (2.32.3)\n", "Requirement already satisfied: tenacity<10,>=8.1.0 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from streamlit) (9.1.2)\n", "Requirement already satisfied: toml<2,>=0.10.1 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from streamlit) (0.10.2)\n", "Requirement already satisfied: typing-extensions<5,>=4.4.0 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from streamlit) (4.12.2)\n", "Requirement already satisfied: watchdog<7,>=2.1.5 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from streamlit) (6.0.0)\n", "Requirement already satisfied: gitpython!=3.1.19,<4,>=3.0.7 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from streamlit) (3.1.44)\n", "Requirement already satisfied: pydeck<1,>=0.8.0b4 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from streamlit) (0.9.1)\n", "Requirement already satisfied: tornado<7,>=6.0.3 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from streamlit) (6.4.2)\n", "Requirement already satisfied: absl-py in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from mediapipe) (2.1.0)\n", "Requirement already satisfied: attrs>=19.1.0 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from mediapipe) (24.3.0)\n", "Requirement already satisfied: flatbuffers>=2.0 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from mediapipe) (24.3.25)\n", "Requirement already satisfied: matplotlib in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from mediapipe) (3.10.1)\n", "Requirement already satisfied: opencv-contrib-python in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from mediapipe) (*********)\n", "Requirement already satisfied: sounddevice>=0.4.4 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from mediapipe) (0.5.1)\n", "Requirement already satisfied: narwhals>=1.15.1 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from plotly) (1.33.0)\n", "Requirement already satisfied: comm>=0.1.3 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from ipywidgets) (0.2.2)\n", "Requirement already satisfied: ipython>=6.1.0 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from ipywidgets) (8.34.0)\n", "Requirement already satisfied: traitlets>=4.3.1 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from ipywidgets) (5.14.3)\n", "Requirement already satisfied: widgetsnbextension~=4.0.12 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from ipywidgets) (4.0.13)\n", "Requirement already satisfied: jupyterlab-widgets~=3.0.12 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from ipywidgets) (3.0.13)\n", "Requirement already satisfied: jinja2 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from altair<6,>=4.0->streamlit) (3.1.6)\n", "Requirement already satisfied: jsonschema>=3.0 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from altair<6,>=4.0->streamlit) (4.23.0)\n", "Requirement already satisfied: colorama in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from click<9,>=7.0->streamlit) (0.4.6)\n", "Requirement already satisfied: gitdb<5,>=4.0.1 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from gitpython!=3.1.19,<4,>=3.0.7->streamlit) (4.0.12)\n", "Requirement already satisfied: decorator in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from ipython>=6.1.0->ipywidgets) (5.2.1)\n", "Requirement already satisfied: exceptiongroup in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from ipython>=6.1.0->ipywidgets) (1.2.2)\n", "Requirement already satisfied: jedi>=0.16 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from ipython>=6.1.0->ipywidgets) (0.19.2)\n", "Requirement already satisfied: matplotlib-inline in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from ipython>=6.1.0->ipywidgets) (0.1.7)\n", "Requirement already satisfied: prompt_toolkit<3.1.0,>=3.0.41 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from ipython>=6.1.0->ipywidgets) (3.0.50)\n", "Requirement already satisfied: pygments>=2.4.0 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from ipython>=6.1.0->ipywidgets) (2.19.1)\n", "Requirement already satisfied: stack_data in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from ipython>=6.1.0->ipywidgets) (0.6.3)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from pandas<3,>=1.4.0->streamlit) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from pandas<3,>=1.4.0->streamlit) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from pandas<3,>=1.4.0->streamlit) (2025.2)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from requests<3,>=2.27->streamlit) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from requests<3,>=2.27->streamlit) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from requests<3,>=2.27->streamlit) (2.3.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from requests<3,>=2.27->streamlit) (2025.1.31)\n", "Requirement already satisfied: CFFI>=1.0 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from sounddevice>=0.4.4->mediapipe) (1.17.1)\n", "Requirement already satisfied: contourpy>=1.0.1 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from matplotlib->mediapipe) (1.3.1)\n", "Requirement already satisfied: cycler>=0.10 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from matplotlib->mediapipe) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from matplotlib->mediapipe) (4.57.0)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from matplotlib->mediapipe) (1.4.8)\n", "Requirement already satisfied: pyparsing>=2.3.1 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from matplotlib->mediapipe) (3.2.3)\n", "Requirement already satisfied: pycparser in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from CFFI>=1.0->sounddevice>=0.4.4->mediapipe) (2.21)\n", "Requirement already satisfied: smmap<6,>=3.0.1 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from gitdb<5,>=4.0.1->gitpython!=3.1.19,<4,>=3.0.7->streamlit) (5.0.2)\n", "Requirement already satisfied: parso<0.9.0,>=0.8.4 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from jedi>=0.16->ipython>=6.1.0->ipywidgets) (0.8.4)\n", "Requirement already satisfied: MarkupSafe>=2.0 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from jinja2->altair<6,>=4.0->streamlit) (3.0.2)\n", "Requirement already satisfied: jsonschema-specifications>=2023.03.6 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from jsonschema>=3.0->altair<6,>=4.0->streamlit) (2024.10.1)\n", "Requirement already satisfied: referencing>=0.28.4 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from jsonschema>=3.0->altair<6,>=4.0->streamlit) (0.36.2)\n", "Requirement already satisfied: rpds-py>=0.7.1 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from jsonschema>=3.0->altair<6,>=4.0->streamlit) (0.24.0)\n", "Requirement already satisfied: wcwidth in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from prompt_toolkit<3.1.0,>=3.0.41->ipython>=6.1.0->ipywidgets) (0.2.13)\n", "Requirement already satisfied: six>=1.5 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from python-dateutil>=2.8.2->pandas<3,>=1.4.0->streamlit) (1.16.0)\n", "Requirement already satisfied: executing>=1.2.0 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from stack_data->ipython>=6.1.0->ipywidgets) (2.1.0)\n", "Requirement already satisfied: asttokens>=2.1.0 in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from stack_data->ipython>=6.1.0->ipywidgets) (3.0.0)\n", "Requirement already satisfied: pure_eval in c:\\users\\<USER>\\anaconda3\\envs\\tf\\lib\\site-packages (from stack_data->ipython>=6.1.0->ipywidgets) (0.2.3)\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["# Install required packages\n", "%pip install streamlit opencv-python-headless mediapipe numpy plotly ipywidgets"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import streamlit as st\n", "import cv2\n", "import mediapipe as mp\n", "import numpy as np\n", "import time\n", "from datetime import datetime\n", "import plotly.graph_objects as go\n", "import logging\n", "from typing import Optional, <PERSON><PERSON>\n", "from IPython.display import display, HTML, clear_output\n", "import ipywidgets as widgets\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO)\n", "logger = logging.getLogger(__name__)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["class ProctorSystem:\n", "    def __init__(self):\n", "        self.setup_mediapipe()\n", "        self.reset_metrics()\n", "        \n", "    def setup_mediapipe(self):\n", "        \"\"\"Initialize MediaPipe components with error handling\"\"\"\n", "        try:\n", "            self.mp_face_mesh = mp.solutions.face_mesh\n", "            self.mp_hands = mp.solutions.hands\n", "            self.mp_drawing = mp.solutions.drawing_utils\n", "            \n", "            self.face_mesh = self.mp_face_mesh.FaceMesh(\n", "                max_num_faces=1,\n", "                refine_landmarks=True,\n", "                min_detection_confidence=0.5,\n", "                min_tracking_confidence=0.5\n", "            )\n", "            \n", "            self.hands = self.mp_hands.Hands(\n", "                max_num_hands=2,\n", "                min_detection_confidence=0.5,\n", "                min_tracking_confidence=0.5\n", "            )\n", "        except Exception as e:\n", "            logger.error(f\"Failed to initialize MediaPipe: {str(e)}\")\n", "            raise\n", "\n", "    def reset_metrics(self):\n", "        \"\"\"Reset all tracking metrics\"\"\"\n", "        self.focus_time = 0.0\n", "        self.distraction_time = 0.0\n", "        self.last_update = time.time()\n", "        self.face_detected = False\n", "        self.looking_away = False\n", "        self.phone_detected = False\n", "        self.events = []\n", "        self.last_face_time = time.time()\n", "\n", "    def process_frame(self, frame: np.ndarray) -> Tuple[np.ndarray, dict]:\n", "        \"\"\"Process a single frame and return the annotated frame and metrics\"\"\"\n", "        if frame is None:\n", "            raise ValueError(\"Invalid frame received\")\n", "\n", "        # Reset status flags\n", "        self.face_detected = False\n", "        self.looking_away = False\n", "        self.phone_detected = False\n", "        \n", "        try:\n", "            # Convert BGR to RGB\n", "            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)\n", "            \n", "            # Process with MediaPipe\n", "            face_results = self.face_mesh.process(frame_rgb)\n", "            hands_results = self.hands.process(frame_rgb)\n", "            \n", "            # Process face detection\n", "            if face_results.multi_face_landmarks:\n", "                self.face_detected = True\n", "                self.last_face_time = time.time()\n", "                \n", "                # Draw face landmarks\n", "                for face_landmarks in face_results.multi_face_landmarks:\n", "                    self.mp_drawing.draw_landmarks(\n", "                        image=frame,\n", "                        landmark_list=face_landmarks,\n", "                        connections=self.mp_face_mesh.FACEMESH_TESSELATION,\n", "                        landmark_drawing_spec=None,\n", "                        connection_drawing_spec=self.mp_drawing.DrawingSpec(\n", "                            color=(0, 255, 0), thickness=1, circle_radius=1\n", "                        )\n", "                    )\n", "                    \n", "                    # Check head pose\n", "                    nose_tip = face_landmarks.landmark[4]\n", "                    if abs(nose_tip.x - 0.5) > 0.2:\n", "                        self.looking_away = True\n", "            \n", "            # Process hand detection\n", "            if hands_results.multi_hand_landmarks:\n", "                for hand_landmarks in hands_results.multi_hand_landmarks:\n", "                    self.mp_drawing.draw_landmarks(\n", "                        frame,\n", "                        hand_landmarks,\n", "                        self.mp_hands.HAND_CONNECTIONS\n", "                    )\n", "                    \n", "                    # Check if hands are near face\n", "                    if face_results.multi_face_landmarks:\n", "                        wrist = hand_landmarks.landmark[0]\n", "                        nose = face_results.multi_face_landmarks[0].landmark[4]\n", "                        if abs(wrist.y - nose.y) < 0.2:\n", "                            self.phone_detected = True\n", "            \n", "            # Update metrics\n", "            current_time = time.time()\n", "            time_diff = current_time - self.last_update\n", "            \n", "            if self.face_detected and not (self.looking_away or self.phone_detected):\n", "                self.focus_time += time_diff\n", "                event_type = \"Focused\"\n", "            else:\n", "                self.distraction_time += time_diff\n", "                event_type = \"Distracted\"\n", "            \n", "            self.events.append((event_type, datetime.now()))\n", "            self.last_update = current_time\n", "            \n", "            # Keep only last 100 events\n", "            if len(self.events) > 100:\n", "                self.events.pop(0)\n", "            \n", "            return frame, self.get_metrics()\n", "            \n", "        except Exception as e:\n", "            logger.error(f\"Error processing frame: {str(e)}\")\n", "            raise\n", "\n", "    def get_metrics(self) -> dict:\n", "        \"\"\"Return current metrics\"\"\"\n", "        total_time = max(self.focus_time + self.distraction_time, 0.001)\n", "        return {\n", "            \"focus_time\": self.focus_time,\n", "            \"distraction_time\": self.distraction_time,\n", "            \"focus_percentage\": (self.focus_time / total_time) * 100,\n", "            \"face_detected\": self.face_detected,\n", "            \"looking_away\": self.looking_away,\n", "            \"phone_detected\": self.phone_detected,\n", "            \"events\": self.events\n", "        }"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def create_focus_chart(events):\n", "    \"\"\"Create focus timeline chart\"\"\"\n", "    if not events:\n", "        return None\n", "    \n", "    fig = go.Figure()\n", "    fig.add_trace(go.<PERSON>(\n", "        x=[t for _, t in events],\n", "        y=[1 if e == \"Focused\" else 0 for e, _ in events],\n", "        mode='lines',\n", "        name='Focus Status'\n", "    ))\n", "    \n", "    fig.update_layout(\n", "        title=\"Focus Timeline\",\n", "        yaxis=dict(\n", "            ticktext=[\"Distracted\", \"Focused\"],\n", "            tickvals=[0, 1],\n", "            range=[-0.1, 1.1]\n", "        ),\n", "        height=200,\n", "        margin=dict(l=0, r=0, t=30, b=0)\n", "    )\n", "    \n", "    return fig"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["def run_proctoring_system():\n", "    # Initialize widgets with better styling\n", "    header = widgets.HTML(\n", "        value='<h1 style=\"text-align:center;color:#2962FF\">AI Proctoring System</h1>'\n", "    )\n", "    \n", "    # Control panel\n", "    control_panel = widgets.HBox([\n", "        widgets.Button(description='Start', button_style='success', icon='play'),\n", "        widgets.Button(description='Stop', button_style='danger', icon='stop'),\n", "        widgets.Button(description='Reset Stats', button_style='warning', icon='refresh'),\n", "        widgets.ToggleButton(description='Record Session', icon='video-camera')\n", "    ])\n", "    \n", "    # Status indicators\n", "    status_panel = widgets.GridBox([\n", "        widgets.HTML(value='<h3>Face Detection</h3>'),\n", "        widgets.HTML(value='<h3>Focus Status</h3>'),\n", "        widgets.HTML(value='<h3>Phone Detection</h3>'),\n", "        widgets.HTML(value='<h3>Eye Tracking</h3>')\n", "    ], layout=widgets.Layout(grid_template_columns='repeat(4, 1fr)', grid_gap='20px'))\n", "    \n", "    # Metrics dashboard\n", "    metrics_panel = widgets.VBox([\n", "        widgets.HTML(value='<h2>Session Metrics</h2>'),\n", "        widgets.GridBox([\n", "            widgets.FloatProgress(description='Focus %', max=100),\n", "            widgets.FloatProgress(description='Attention %', max=100),\n", "            widgets.Label(value='Focus Time: 0s'),\n", "            widgets.Label(value='Distraction Time: 0s')\n", "        ], layout=widgets.Layout(grid_template_columns='repeat(2, 1fr)', grid_gap='10px'))\n", "    ])\n", "    \n", "    # Video feed\n", "    video_widget = widgets.Image(format='jpeg', width=640, height=480)\n", "    \n", "    # Initialize plots\n", "    focus_fig = go.FigureWidget()\n", "    focus_fig.add_scatter(name='Focus Level')\n", "    focus_fig.update_layout(\n", "        title='Real-time Focus Tracking',\n", "        xaxis_title='Time',\n", "        yaxis_title='Focus Level',\n", "        yaxis_range=[0, 1]\n", "    )\n", "    \n", "    # Layout everything\n", "    display(widgets.VBox([\n", "        header,\n", "        control_panel,\n", "        widgets.HBox([\n", "            video_widget,\n", "            widgets.VBox([status_panel, metrics_panel])\n", "        ]),\n", "        focus_fig\n", "    ]))\n", "    \n", "    # Initialize camera and proctor\n", "    cap = cv2.VideoCapture(0)\n", "    proctor = ProctorSystem()\n", "    running = False\n", "    recording = False\n", "    start_time = time.time()\n", "    focus_data = {'times': [], 'values': []}\n", "    \n", "    def update_ui(metrics):\n", "        # Update status indicators with animations\n", "        status_panel.children[0].value = create_status_badge(\n", "            \"Face Detection\", metrics[\"face_detected\"])\n", "        status_panel.children[1].value = create_status_badge(\n", "            \"Focus Status\", not metrics[\"looking_away\"])\n", "        status_panel.children[2].value = create_status_badge(\n", "            \"Phone Detection\", not metrics[\"phone_detected\"])\n", "        status_panel.children[3].value = create_status_badge(\n", "            \"Eye Tracking\", metrics[\"face_detected\"] and not metrics[\"looking_away\"])\n", "        \n", "        # Update metrics\n", "        metrics_panel.children[1].children[0].value = metrics[\"focus_percentage\"]\n", "        metrics_panel.children[1].children[1].value = calculate_attention_score(metrics)\n", "        metrics_panel.children[1].children[2].value = f'Focus Time: {metrics[\"focus_time\"]:.1f}s'\n", "        metrics_panel.children[1].children[3].value = f'Distraction Time: {metrics[\"distraction_time\"]:.1f}s'\n", "        \n", "        # Update focus plot\n", "        current_time = time.time() - start_time\n", "        focus_data['times'].append(current_time)\n", "        focus_data['values'].append(1 if metrics[\"face_detected\"] and not (metrics[\"looking_away\"] or metrics[\"phone_detected\"]) else 0)\n", "        \n", "        # Keep only last 60 seconds of data\n", "        if len(focus_data['times']) > 600:  # 10 minutes at 0.1s intervals\n", "            focus_data['times'].pop(0)\n", "            focus_data['values'].pop(0)\n", "        \n", "        focus_fig.data[0].x = focus_data['times']\n", "        focus_fig.data[0].y = focus_data['values']\n", "    \n", "    def calculate_attention_score(metrics):\n", "        # Weighted scoring system\n", "        weights = {\n", "            'face_detected': 0.4,\n", "            'looking_away': 0.3,\n", "            'phone_detected': 0.3\n", "        }\n", "        score = (\n", "            weights['face_detected'] * (1 if metrics['face_detected'] else 0) +\n", "            weights['looking_away'] * (0 if metrics['looking_away'] else 1) +\n", "            weights['phone_detected'] * (0 if metrics['phone_detected'] else 1)\n", "        ) * 100\n", "        return score\n", "    \n", "    def create_status_badge(label, status):\n", "        color = '#4CAF50' if status else '#F44336'\n", "        icon = '✓' if status else '✗'\n", "        return f'''\n", "        <div style=\"\n", "            padding: 10px;\n", "            border-radius: 5px;\n", "            background: {color};\n", "            color: white;\n", "            text-align: center;\n", "            margin: 5px;\n", "        \">\n", "            <h4>{label}</h4>\n", "            <span style=\"font-size: 24px\">{icon}</span>\n", "        </div>\n", "        '''\n", "    \n", "    def on_start_button_clicked(b):\n", "        nonlocal running, start_time\n", "        running = True\n", "        start_time = time.time()\n", "        focus_data['times'].clear()\n", "        focus_data['values'].clear()\n", "    \n", "    def on_stop_button_clicked(b):\n", "        nonlocal running\n", "        running = False\n", "    \n", "    def on_reset_button_clicked(b):\n", "        proctor.reset_metrics()\n", "        focus_data['times'].clear()\n", "        focus_data['values'].clear()\n", "    \n", "    def on_record_button_clicked(b):\n", "        nonlocal recording\n", "        recording = b.value\n", "    \n", "    # Connect button callbacks\n", "    control_panel.children[0].on_click(on_start_button_clicked)\n", "    control_panel.children[1].on_click(on_stop_button_clicked)\n", "    control_panel.children[2].on_click(on_reset_button_clicked)\n", "    control_panel.children[3].observe(on_record_button_clicked, 'value')\n", "    \n", "    video_writer = None\n", "    \n", "    try:\n", "        while True:\n", "            if not running:\n", "                time.sleep(0.1)\n", "                continue\n", "                \n", "            ret, frame = cap.read()\n", "            if not ret:\n", "                print(\"Error: Failed to read frame\")\n", "                break\n", "            \n", "            # Process frame\n", "            frame, metrics = proctor.process_frame(frame)\n", "            \n", "            # Record video if enabled\n", "            if recording:\n", "                if video_writer is None:\n", "                    fourcc = cv2.VideoWriter_fourcc(*'XVID')\n", "                    video_writer = cv2.VideoWriter(\n", "                        f'proctoring_session_{int(time.time())}.avi',\n", "                        fourcc, 10.0, (frame.shape[1], frame.shape[0])\n", "                    )\n", "                video_writer.write(frame)\n", "            \n", "            # Update display\n", "            _, jpeg = cv2.imencode('.jpg', cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))\n", "            video_widget.value = jpeg.tobytes()\n", "            \n", "            # Update UI elements\n", "            update_ui(metrics)\n", "            \n", "            time.sleep(0.1)  # Prevent excessive CPU usage\n", "            \n", "    except Exception as e:\n", "        print(f\"An error occurred: {str(e)}\")\n", "        logger.error(f\"Runtime error: {str(e)}\", exc_info=True)\n", "        \n", "    finally:\n", "        cap.release()\n", "        if video_writer is not None:\n", "            video_writer.release()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Run the Proctoring System\n", "\n", "Click the cell below and run it to start the proctoring system. Click the 'Stop' button to end the session."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install anywidget if not already installed\n", "# %pip install anywidget\n", "\n", "# Run the system\n", "run_proctoring_system()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "tf", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 4}