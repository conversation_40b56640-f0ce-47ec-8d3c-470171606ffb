"""
Start Main Dashboard with Proctoring
===================================

Launch the main dashboard on localhost:8501 with integrated student proctoring
"""

import subprocess
import sys
import time
import os

def start_main_dashboard():
    """Start the main dashboard application"""
    print("🚀 Starting Main Dashboard with Integrated Proctoring...")
    print("=" * 60)
    
    try:
        # Change to the correct directory
        os.chdir(r"D:\Capstone Project\ajil proctoring - 10")
        
        # Start the main application
        cmd = [
            sys.executable, "-m", "streamlit", "run", 
            "main_app.py", 
            "--server.port", "8501",
            "--server.headless", "true",
            "--browser.gatherUsageStats", "false"
        ]
        
        print("📱 Starting application...")
        print(f"🔗 URL: http://localhost:8501")
        print("⏳ Please wait for the application to start...")
        
        # Start the process
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Wait a moment for startup
        time.sleep(3)
        
        # Check if process is running
        if process.poll() is None:
            print("✅ Main dashboard started successfully!")
            print()
            print("🎯 TESTING INSTRUCTIONS:")
            print("-" * 40)
            print("1. 🌐 Open: http://localhost:8501")
            print("2. 🔑 Login as STUDENT: <EMAIL> / student123")
            print("3. 📚 You should see 'Student Test Dashboard'")
            print("4. 🚀 Click 'Start Proctored Test'")
            print()
            print("📹 Expected Features:")
            print("   • Camera on top left corner")
            print("   • Automatic fullscreen")
            print("   • Tab switch detection")
            print("   • Real-time violation counter")
            print()
            print("Press Ctrl+C to stop the server...")
            
            # Keep the process running
            try:
                process.wait()
            except KeyboardInterrupt:
                print("\n🛑 Stopping server...")
                process.terminate()
                process.wait()
                print("✅ Server stopped.")
        else:
            # Process failed to start
            stdout, stderr = process.communicate()
            print("❌ Failed to start main dashboard")
            print("STDOUT:", stdout)
            print("STDERR:", stderr)
            
    except Exception as e:
        print(f"❌ Error starting main dashboard: {e}")

if __name__ == "__main__":
    start_main_dashboard()
