"""
Comprehensive Report Generation Engine
Generates detailed reports with all monitoring data, violations, and session statistics
"""
import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from datetime import datetime, timezone, timedelta
import json
import io
from database_models import (
    Test, ProctorSession, MonitoringEvent, SessionMetrics, get_db_session, User
)


def show_report_generator():
    """Main report generation interface"""
    st.header("📊 Report Generation Engine")
    
    # Check if user is admin
    if st.session_state.get('user_role') != 'admin':
        st.error("Access denied. Admin privileges required.")
        return
    
    # Report configuration
    st.subheader("📋 Report Configuration")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        report_type = st.selectbox(
            "Report Type",
            [
                "Comprehensive Session Report",
                "Violation Analysis Report", 
                "Student Performance Report",
                "Test Analytics Report",
                "Real-time Monitoring Report",
                "Custom Data Export"
            ]
        )
    
    with col2:
        start_date = st.date_input(
            "Start Date", 
            value=datetime.now().date() - timedelta(days=30)
        )
    
    with col3:
        end_date = st.date_input(
            "End Date", 
            value=datetime.now().date()
        )
    
    # Additional filters
    with st.expander("🔍 Advanced Filters", expanded=False):
        col1, col2, col3 = st.columns(3)
        
        with col1:
            # Test filter
            db = get_db_session()
            try:
                tests = db.query(Test).filter(Test.is_active == True).all()
                test_options = ["All Tests"] + [f"{test.title} (ID: {test.id})" for test in tests]
                selected_test = st.selectbox("Filter by Test", test_options)
            finally:
                db.close()
        
        with col2:
            # Student filter
            db = get_db_session()
            try:
                students = db.query(User).filter(User.role == 'student', User.is_active == True).all()
                student_options = ["All Students"] + [f"{student.name} ({student.email})" for student in students]
                selected_student = st.selectbox("Filter by Student", student_options)
            finally:
                db.close()
        
        with col3:
            # Status filter
            status_options = ["All Statuses", "Active", "Completed", "Terminated"]
            selected_status = st.selectbox("Filter by Status", status_options)
    
    # Generate report button
    if st.button("📊 Generate Report", type="primary"):
        filters = {
            'start_date': start_date,
            'end_date': end_date,
            'test': selected_test if selected_test != "All Tests" else None,
            'student': selected_student if selected_student != "All Students" else None,
            'status': selected_status.lower() if selected_status != "All Statuses" else None
        }
        
        if report_type == "Comprehensive Session Report":
            generate_comprehensive_session_report(filters)
        elif report_type == "Violation Analysis Report":
            generate_violation_analysis_report(filters)
        elif report_type == "Student Performance Report":
            generate_student_performance_report(filters)
        elif report_type == "Test Analytics Report":
            generate_test_analytics_report(filters)
        elif report_type == "Real-time Monitoring Report":
            generate_realtime_monitoring_report(filters)
        elif report_type == "Custom Data Export":
            generate_custom_data_export(filters)


def generate_comprehensive_session_report(filters):
    """Generate comprehensive session report with all data"""
    st.subheader("📋 Comprehensive Session Report")
    
    db = get_db_session()
    try:
        # Build query with filters
        query = db.query(ProctorSession).filter(
            ProctorSession.created_at >= filters['start_date'],
            ProctorSession.created_at <= filters['end_date'] + timedelta(days=1)
        )
        
        if filters['status']:
            query = query.filter(ProctorSession.status == filters['status'])
        
        sessions = query.all()
        
        if not sessions:
            st.info("No sessions found matching the criteria.")
            return
        
        # Prepare comprehensive data
        session_data = []
        violation_data = []
        metrics_data = []
        
        for session in sessions:
            # Get related data
            test = db.query(Test).filter(Test.id == session.test_id).first()
            student = db.query(User).filter(User.id == session.student_id).first()
            
            # Apply additional filters
            if filters['test'] and test and f"{test.title} (ID: {test.id})" != filters['test']:
                continue
            if filters['student'] and student and f"{student.name} ({student.email})" != filters['student']:
                continue
            
            # Session data
            session_info = {
                'Session ID': session.id,
                'Session Token': session.session_token,
                'Test Title': test.title if test else 'Unknown',
                'Test Duration (min)': test.duration_minutes if test else 0,
                'Student Name': student.name if student else 'Unknown',
                'Student Email': student.email if student else 'Unknown',
                'Status': session.status.title(),
                'Created At': session.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                'Started At': session.started_at.strftime('%Y-%m-%d %H:%M:%S') if session.started_at else 'Not Started',
                'Completed At': session.completed_at.strftime('%Y-%m-%d %H:%M:%S') if session.completed_at else 'Not Completed',
                'Total Duration (min)': round(session.total_duration / 60) if session.total_duration else 0,
                'Total Violations': session.total_violations,
                'Face Detection Score': f"{session.face_detection_score:.2%}",
                'Attention Score': f"{session.attention_score:.2%}",
                'Recording Path': session.recording_path or 'No Recording'
            }
            session_data.append(session_info)
            
            # Violation data for this session
            violations = db.query(MonitoringEvent).filter(
                MonitoringEvent.session_id == session.id
            ).all()
            
            for violation in violations:
                violation_info = {
                    'Session ID': session.id,
                    'Student Name': student.name if student else 'Unknown',
                    'Test Title': test.title if test else 'Unknown',
                    'Violation Type': violation.event_type.replace('_', ' ').title(),
                    'Severity': violation.severity.title(),
                    'Timestamp': violation.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                    'Event Data': str(violation.get_event_data()) if violation.event_data else 'N/A'
                }
                violation_data.append(violation_info)
            
            # Metrics data for this session
            metrics = db.query(SessionMetrics).filter(
                SessionMetrics.session_id == session.id
            ).all()
            
            for metric in metrics:
                metric_info = {
                    'Session ID': session.id,
                    'Student Name': student.name if student else 'Unknown',
                    'Timestamp': metric.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                    'Face Detected': metric.face_detected,
                    'Face Count': metric.face_count,
                    'Face Confidence': f"{metric.face_confidence:.2%}",
                    'Looking Away': metric.looking_away,
                    'Attention Score': f"{metric.attention_score:.2%}",
                    'Tab Switches': metric.tab_switches,
                    'Fullscreen Exits': metric.fullscreen_exits
                }
                metrics_data.append(metric_info)
        
        # Display data in tabs
        tab1, tab2, tab3, tab4 = st.tabs(["📊 Summary", "📋 Sessions", "🚨 Violations", "📈 Metrics"])
        
        with tab1:
            show_report_summary(session_data, violation_data, metrics_data)
        
        with tab2:
            show_sessions_table(session_data)
        
        with tab3:
            show_violations_table(violation_data)
        
        with tab4:
            show_metrics_table(metrics_data)
    
    finally:
        db.close()


def show_report_summary(session_data, violation_data, metrics_data):
    """Show report summary with key statistics"""
    st.subheader("📊 Report Summary")
    
    # Key metrics
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Total Sessions", len(session_data))
    
    with col2:
        completed_sessions = len([s for s in session_data if s['Status'] == 'Completed'])
        st.metric("Completed Sessions", completed_sessions)
    
    with col3:
        st.metric("Total Violations", len(violation_data))
    
    with col4:
        total_duration = sum([s['Total Duration (min)'] for s in session_data])
        st.metric("Total Duration", f"{total_duration} min")
    
    # Charts
    col1, col2 = st.columns(2)
    
    with col1:
        # Session status distribution
        if session_data:
            status_counts = {}
            for session in session_data:
                status = session['Status']
                status_counts[status] = status_counts.get(status, 0) + 1
            
            fig = px.pie(
                values=list(status_counts.values()),
                names=list(status_counts.keys()),
                title="Session Status Distribution"
            )
            st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        # Violation types distribution
        if violation_data:
            violation_counts = {}
            for violation in violation_data:
                v_type = violation['Violation Type']
                violation_counts[v_type] = violation_counts.get(v_type, 0) + 1
            
            fig = px.bar(
                x=list(violation_counts.keys()),
                y=list(violation_counts.values()),
                title="Violation Types Distribution"
            )
            fig.update_xaxis(tickangle=45)
            st.plotly_chart(fig, use_container_width=True)


def show_sessions_table(session_data):
    """Display sessions data in a table"""
    st.subheader("📋 Session Details")
    
    if session_data:
        df = pd.DataFrame(session_data)
        st.dataframe(df, use_container_width=True, hide_index=True)
        
        # Download button
        csv = df.to_csv(index=False)
        st.download_button(
            label="📥 Download Sessions CSV",
            data=csv,
            file_name=f"sessions_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
            mime="text/csv"
        )
    else:
        st.info("No session data available.")


def show_violations_table(violation_data):
    """Display violations data in a table"""
    st.subheader("🚨 Violation Details")
    
    if violation_data:
        df = pd.DataFrame(violation_data)
        st.dataframe(df, use_container_width=True, hide_index=True)
        
        # Download button
        csv = df.to_csv(index=False)
        st.download_button(
            label="📥 Download Violations CSV",
            data=csv,
            file_name=f"violations_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
            mime="text/csv"
        )
        
        # Violation analysis
        st.subheader("📈 Violation Analysis")
        
        col1, col2 = st.columns(2)
        
        with col1:
            # Violations by student
            student_violations = {}
            for violation in violation_data:
                student = violation['Student Name']
                student_violations[student] = student_violations.get(student, 0) + 1
            
            if student_violations:
                fig = px.bar(
                    x=list(student_violations.keys()),
                    y=list(student_violations.values()),
                    title="Violations by Student"
                )
                fig.update_xaxis(tickangle=45)
                st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            # Violations by severity
            severity_violations = {}
            for violation in violation_data:
                severity = violation['Severity']
                severity_violations[severity] = severity_violations.get(severity, 0) + 1
            
            if severity_violations:
                fig = px.pie(
                    values=list(severity_violations.values()),
                    names=list(severity_violations.keys()),
                    title="Violations by Severity"
                )
                st.plotly_chart(fig, use_container_width=True)
    else:
        st.info("No violation data available.")


def show_metrics_table(metrics_data):
    """Display metrics data in a table"""
    st.subheader("📈 Detailed Metrics")
    
    if metrics_data:
        df = pd.DataFrame(metrics_data)
        st.dataframe(df, use_container_width=True, hide_index=True)
        
        # Download button
        csv = df.to_csv(index=False)
        st.download_button(
            label="📥 Download Metrics CSV",
            data=csv,
            file_name=f"metrics_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
            mime="text/csv"
        )
        
        # Metrics analysis
        st.subheader("📊 Metrics Analysis")
        
        # Convert percentage strings back to floats for analysis
        df_analysis = df.copy()
        df_analysis['Attention Score Numeric'] = df_analysis['Attention Score'].str.rstrip('%').astype(float) / 100
        df_analysis['Face Confidence Numeric'] = df_analysis['Face Confidence'].str.rstrip('%').astype(float) / 100
        
        col1, col2 = st.columns(2)
        
        with col1:
            # Average attention score by student
            avg_attention = df_analysis.groupby('Student Name')['Attention Score Numeric'].mean()
            
            fig = px.bar(
                x=avg_attention.index,
                y=avg_attention.values,
                title="Average Attention Score by Student"
            )
            fig.update_yaxis(tickformat='.1%')
            fig.update_xaxis(tickangle=45)
            st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            # Face detection trends
            df_analysis['Timestamp'] = pd.to_datetime(df_analysis['Timestamp'])
            face_detection_trend = df_analysis.groupby(df_analysis['Timestamp'].dt.hour)['Face Detected'].mean()
            
            fig = px.line(
                x=face_detection_trend.index,
                y=face_detection_trend.values,
                title="Face Detection Rate by Hour"
            )
            fig.update_xaxis(title="Hour of Day")
            fig.update_yaxis(title="Face Detection Rate", tickformat='.1%')
            st.plotly_chart(fig, use_container_width=True)
    else:
        st.info("No metrics data available.")


def generate_violation_analysis_report(filters):
    """Generate detailed violation analysis report"""
    st.subheader("🚨 Violation Analysis Report")
    
    db = get_db_session()
    try:
        # Query violations with filters
        query = db.query(MonitoringEvent).filter(
            MonitoringEvent.timestamp >= filters['start_date'],
            MonitoringEvent.timestamp <= filters['end_date'] + timedelta(days=1)
        )
        
        violations = query.all()
        
        if not violations:
            st.info("No violations found matching the criteria.")
            return
        
        # Prepare violation analysis data
        violation_analysis = []
        
        for violation in violations:
            session = db.query(ProctorSession).filter(ProctorSession.id == violation.session_id).first()
            if session:
                test = db.query(Test).filter(Test.id == session.test_id).first()
                student = db.query(User).filter(User.id == session.student_id).first()
                
                violation_info = {
                    'Violation ID': violation.id,
                    'Session ID': violation.session_id,
                    'Test': test.title if test else 'Unknown',
                    'Student': student.name if student else 'Unknown',
                    'Violation Type': violation.event_type.replace('_', ' ').title(),
                    'Severity': violation.severity.title(),
                    'Timestamp': violation.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                    'Date': violation.timestamp.strftime('%Y-%m-%d'),
                    'Time': violation.timestamp.strftime('%H:%M:%S'),
                    'Hour': violation.timestamp.hour,
                    'Day of Week': violation.timestamp.strftime('%A'),
                    'Event Data': str(violation.get_event_data()) if violation.event_data else 'N/A'
                }
                violation_analysis.append(violation_info)
        
        # Display analysis
        df = pd.DataFrame(violation_analysis)
        
        # Summary statistics
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("Total Violations", len(violation_analysis))
        
        with col2:
            unique_students = len(df['Student'].unique())
            st.metric("Students with Violations", unique_students)
        
        with col3:
            most_common_violation = df['Violation Type'].mode().iloc[0] if not df.empty else 'N/A'
            st.metric("Most Common Violation", most_common_violation)
        
        with col4:
            high_severity_count = len(df[df['Severity'].isin(['High', 'Critical'])])
            st.metric("High/Critical Violations", high_severity_count)
        
        # Detailed analysis charts
        col1, col2 = st.columns(2)
        
        with col1:
            # Violations by hour
            hourly_violations = df.groupby('Hour').size()
            fig = px.bar(
                x=hourly_violations.index,
                y=hourly_violations.values,
                title="Violations by Hour of Day"
            )
            fig.update_xaxis(title="Hour")
            fig.update_yaxis(title="Number of Violations")
            st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            # Violations by day of week
            daily_violations = df.groupby('Day of Week').size()
            fig = px.bar(
                x=daily_violations.index,
                y=daily_violations.values,
                title="Violations by Day of Week"
            )
            fig.update_xaxis(tickangle=45)
            st.plotly_chart(fig, use_container_width=True)
        
        # Detailed table
        st.subheader("📋 Detailed Violation Data")
        st.dataframe(df, use_container_width=True, hide_index=True)
        
        # Download button
        csv = df.to_csv(index=False)
        st.download_button(
            label="📥 Download Violation Analysis CSV",
            data=csv,
            file_name=f"violation_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
            mime="text/csv"
        )
    
    finally:
        db.close()


def generate_student_performance_report(filters):
    """Generate student performance analysis report"""
    st.subheader("👥 Student Performance Report")
    
    db = get_db_session()
    try:
        # Get all students
        students = db.query(User).filter(User.role == 'student', User.is_active == True).all()
        
        student_performance = []
        
        for student in students:
            # Get sessions for this student in date range
            sessions = db.query(ProctorSession).filter(
                ProctorSession.student_id == student.id,
                ProctorSession.created_at >= filters['start_date'],
                ProctorSession.created_at <= filters['end_date'] + timedelta(days=1)
            ).all()
            
            if sessions:
                # Calculate performance metrics
                total_sessions = len(sessions)
                completed_sessions = len([s for s in sessions if s.status == 'completed'])
                completion_rate = (completed_sessions / total_sessions) * 100
                
                total_violations = sum([
                    db.query(MonitoringEvent).filter(MonitoringEvent.session_id == s.id).count()
                    for s in sessions
                ])
                
                avg_violations_per_session = total_violations / total_sessions
                
                # Calculate average scores
                attention_scores = [s.attention_score for s in sessions if s.attention_score is not None]
                face_scores = [s.face_detection_score for s in sessions if s.face_detection_score is not None]
                
                avg_attention = sum(attention_scores) / len(attention_scores) if attention_scores else 0
                avg_face_detection = sum(face_scores) / len(face_scores) if face_scores else 0
                
                # Calculate total test time
                total_duration = sum([s.total_duration for s in sessions if s.total_duration]) / 60  # in minutes
                
                performance_data = {
                    'Student Name': student.name,
                    'Email': student.email,
                    'Total Sessions': total_sessions,
                    'Completed Sessions': completed_sessions,
                    'Completion Rate': f"{completion_rate:.1f}%",
                    'Total Violations': total_violations,
                    'Avg Violations/Session': f"{avg_violations_per_session:.1f}",
                    'Total Test Time (min)': f"{total_duration:.0f}",
                    'Avg Attention Score': f"{avg_attention:.1%}",
                    'Avg Face Detection': f"{avg_face_detection:.1%}",
                    'Performance Grade': calculate_performance_grade(completion_rate, avg_violations_per_session, avg_attention)
                }
                student_performance.append(performance_data)
        
        if student_performance:
            df = pd.DataFrame(student_performance)
            
            # Summary metrics
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.metric("Total Students", len(student_performance))
            
            with col2:
                avg_completion = df['Completion Rate'].str.rstrip('%').astype(float).mean()
                st.metric("Avg Completion Rate", f"{avg_completion:.1f}%")
            
            with col3:
                total_violations = df['Total Violations'].sum()
                st.metric("Total Violations", total_violations)
            
            with col4:
                avg_attention = df['Avg Attention Score'].str.rstrip('%').astype(float).mean()
                st.metric("Avg Attention Score", f"{avg_attention:.1f}%")
            
            # Performance charts
            col1, col2 = st.columns(2)
            
            with col1:
                # Performance grade distribution
                grade_counts = df['Performance Grade'].value_counts()
                fig = px.pie(
                    values=grade_counts.values,
                    names=grade_counts.index,
                    title="Performance Grade Distribution"
                )
                st.plotly_chart(fig, use_container_width=True)
            
            with col2:
                # Completion rate vs violations
                df_plot = df.copy()
                df_plot['Completion Rate Numeric'] = df_plot['Completion Rate'].str.rstrip('%').astype(float)
                df_plot['Avg Violations Numeric'] = df_plot['Avg Violations/Session'].astype(float)
                
                fig = px.scatter(
                    df_plot,
                    x='Completion Rate Numeric',
                    y='Avg Violations Numeric',
                    hover_data=['Student Name'],
                    title="Completion Rate vs Average Violations"
                )
                fig.update_xaxis(title="Completion Rate (%)")
                fig.update_yaxis(title="Average Violations per Session")
                st.plotly_chart(fig, use_container_width=True)
            
            # Detailed table
            st.subheader("📋 Student Performance Details")
            st.dataframe(df, use_container_width=True, hide_index=True)
            
            # Download button
            csv = df.to_csv(index=False)
            st.download_button(
                label="📥 Download Student Performance CSV",
                data=csv,
                file_name=f"student_performance_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                mime="text/csv"
            )
        else:
            st.info("No student performance data found in the selected date range.")
    
    finally:
        db.close()


def calculate_performance_grade(completion_rate, avg_violations, avg_attention):
    """Calculate performance grade based on metrics"""
    score = 0
    
    # Completion rate (40% weight)
    if completion_rate >= 90:
        score += 40
    elif completion_rate >= 80:
        score += 32
    elif completion_rate >= 70:
        score += 24
    elif completion_rate >= 60:
        score += 16
    else:
        score += 8
    
    # Violations (30% weight) - lower is better
    if avg_violations <= 1:
        score += 30
    elif avg_violations <= 2:
        score += 24
    elif avg_violations <= 3:
        score += 18
    elif avg_violations <= 5:
        score += 12
    else:
        score += 6
    
    # Attention score (30% weight)
    attention_percent = avg_attention * 100
    if attention_percent >= 90:
        score += 30
    elif attention_percent >= 80:
        score += 24
    elif attention_percent >= 70:
        score += 18
    elif attention_percent >= 60:
        score += 12
    else:
        score += 6
    
    # Convert to letter grade
    if score >= 90:
        return "A"
    elif score >= 80:
        return "B"
    elif score >= 70:
        return "C"
    elif score >= 60:
        return "D"
    else:
        return "F"


def generate_test_analytics_report(filters):
    """Generate test analytics report"""
    st.subheader("📝 Test Analytics Report")
    
    db = get_db_session()
    try:
        tests = db.query(Test).filter(Test.is_active == True).all()
        
        test_analytics = []
        
        for test in tests:
            sessions = db.query(ProctorSession).filter(
                ProctorSession.test_id == test.id,
                ProctorSession.created_at >= filters['start_date'],
                ProctorSession.created_at <= filters['end_date'] + timedelta(days=1)
            ).all()
            
            if sessions:
                total_sessions = len(sessions)
                completed_sessions = len([s for s in sessions if s.status == 'completed'])
                completion_rate = (completed_sessions / total_sessions) * 100
                
                total_violations = sum([
                    db.query(MonitoringEvent).filter(MonitoringEvent.session_id == s.id).count()
                    for s in sessions
                ])
                
                avg_duration = sum([s.total_duration for s in sessions if s.total_duration]) / len(sessions) / 60
                
                test_data = {
                    'Test Title': test.title,
                    'Test Duration (min)': test.duration_minutes,
                    'Total Sessions': total_sessions,
                    'Completed Sessions': completed_sessions,
                    'Completion Rate': f"{completion_rate:.1f}%",
                    'Total Violations': total_violations,
                    'Avg Violations/Session': f"{total_violations / total_sessions:.1f}",
                    'Avg Actual Duration (min)': f"{avg_duration:.1f}",
                    'Created Date': test.created_at.strftime('%Y-%m-%d'),
                    'Questions Count': len(test.get_questions())
                }
                test_analytics.append(test_data)
        
        if test_analytics:
            df = pd.DataFrame(test_analytics)
            
            # Display table
            st.dataframe(df, use_container_width=True, hide_index=True)
            
            # Download button
            csv = df.to_csv(index=False)
            st.download_button(
                label="📥 Download Test Analytics CSV",
                data=csv,
                file_name=f"test_analytics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                mime="text/csv"
            )
        else:
            st.info("No test analytics data found.")
    
    finally:
        db.close()


def generate_realtime_monitoring_report(filters):
    """Generate real-time monitoring report"""
    st.subheader("🔍 Real-time Monitoring Report")
    
    # Auto-refresh option
    auto_refresh = st.checkbox("Auto-refresh every 30 seconds", value=True)
    
    if auto_refresh:
        import time
        time.sleep(30)
        st.rerun()
    
    # Current active sessions
    from database_models import get_active_sessions
    active_sessions = get_active_sessions()
    
    if active_sessions:
        st.write(f"**Currently Active Sessions: {len(active_sessions)}**")
        
        for session in active_sessions:
            with st.container():
                db = get_db_session()
                try:
                    test = db.query(Test).filter(Test.id == session.test_id).first()
                    student = db.query(User).filter(User.id == session.student_id).first()
                    
                    col1, col2, col3 = st.columns([2, 1, 1])
                    
                    with col1:
                        st.write(f"**Session {session.id}**")
                        st.write(f"Test: {test.title if test else 'Unknown'}")
                        st.write(f"Student: {student.name if student else 'Unknown'}")
                        
                        if session.started_at:
                            duration = datetime.now(timezone.utc) - session.started_at
                            st.write(f"Duration: {str(duration).split('.')[0]}")
                    
                    with col2:
                        st.metric("Violations", session.total_violations)
                        st.metric("Face Score", f"{session.face_detection_score:.1%}")
                    
                    with col3:
                        st.metric("Attention", f"{session.attention_score:.1%}")
                        
                        # Recent violations
                        recent_violations = db.query(MonitoringEvent).filter(
                            MonitoringEvent.session_id == session.id
                        ).order_by(MonitoringEvent.timestamp.desc()).limit(3).all()
                        
                        if recent_violations:
                            st.write("**Recent Events:**")
                            for violation in recent_violations:
                                severity_emoji = {'low': '🟢', 'medium': '🟡', 'high': '🔴', 'critical': '🚨'}.get(violation.severity, '⚪')
                                st.write(f"{severity_emoji} {violation.event_type.replace('_', ' ').title()}")
                
                finally:
                    db.close()
                
                st.divider()
    else:
        st.info("No active sessions currently running.")


def generate_custom_data_export(filters):
    """Generate custom data export with user-selected fields"""
    st.subheader("📤 Custom Data Export")
    
    # Data selection
    st.write("**Select data to export:**")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        export_sessions = st.checkbox("Session Data", value=True)
        export_violations = st.checkbox("Violation Data", value=True)
    
    with col2:
        export_metrics = st.checkbox("Metrics Data", value=False)
        export_tests = st.checkbox("Test Data", value=False)
    
    with col3:
        export_users = st.checkbox("User Data", value=False)
        
    # Export format
    export_format = st.selectbox("Export Format", ["CSV", "JSON", "Excel"])
    
    if st.button("📤 Export Data"):
        db = get_db_session()
        try:
            export_data = {}
            
            if export_sessions:
                sessions = db.query(ProctorSession).filter(
                    ProctorSession.created_at >= filters['start_date'],
                    ProctorSession.created_at <= filters['end_date'] + timedelta(days=1)
                ).all()
                
                session_data = []
                for session in sessions:
                    test = db.query(Test).filter(Test.id == session.test_id).first()
                    student = db.query(User).filter(User.id == session.student_id).first()
                    
                    session_data.append({
                        'id': session.id,
                        'test_title': test.title if test else None,
                        'student_name': student.name if student else None,
                        'status': session.status,
                        'created_at': session.created_at.isoformat(),
                        'started_at': session.started_at.isoformat() if session.started_at else None,
                        'completed_at': session.completed_at.isoformat() if session.completed_at else None,
                        'total_duration': session.total_duration,
                        'total_violations': session.total_violations,
                        'face_detection_score': session.face_detection_score,
                        'attention_score': session.attention_score
                    })
                
                export_data['sessions'] = session_data
            
            if export_violations:
                violations = db.query(MonitoringEvent).filter(
                    MonitoringEvent.timestamp >= filters['start_date'],
                    MonitoringEvent.timestamp <= filters['end_date'] + timedelta(days=1)
                ).all()
                
                violation_data = []
                for violation in violations:
                    violation_data.append({
                        'id': violation.id,
                        'session_id': violation.session_id,
                        'event_type': violation.event_type,
                        'severity': violation.severity,
                        'timestamp': violation.timestamp.isoformat(),
                        'event_data': violation.get_event_data()
                    })
                
                export_data['violations'] = violation_data
            
            # Add other data types as requested...
            
            # Generate download based on format
            if export_format == "JSON":
                json_data = json.dumps(export_data, indent=2)
                st.download_button(
                    label="📥 Download JSON",
                    data=json_data,
                    file_name=f"proctoring_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                    mime="application/json"
                )
            
            elif export_format == "CSV":
                # Create separate CSV files for each data type
                if export_sessions and 'sessions' in export_data:
                    df = pd.DataFrame(export_data['sessions'])
                    csv = df.to_csv(index=False)
                    st.download_button(
                        label="📥 Download Sessions CSV",
                        data=csv,
                        file_name=f"sessions_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                        mime="text/csv"
                    )
                
                if export_violations and 'violations' in export_data:
                    df = pd.DataFrame(export_data['violations'])
                    csv = df.to_csv(index=False)
                    st.download_button(
                        label="📥 Download Violations CSV",
                        data=csv,
                        file_name=f"violations_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                        mime="text/csv"
                    )
            
            st.success("Data export prepared successfully!")
        
        finally:
            db.close()
