"""
Test Simple Proctoring Setup Implementation
==========================================

Verify that the simple proctoring setup is working correctly with:
- Camera initialization
- Automatic fullscreen
- Tab switch detection
- Camera positioned on top left
"""

import time
from database_models import (
    get_db_session, assign_test_to_student, User, Test, TestAssignment
)

def verify_simple_proctoring_implementation():
    """Verify the simple proctoring implementation"""
    print("🔍 VERIFYING SIMPLE PROCTORING SETUP")
    print("=" * 50)
    
    # Check if the simple proctoring interface exists
    try:
        with open('student_test_interface.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        required_components = [
            ('show_simple_proctoring_interface', 'Simple proctoring interface function'),
            ('SimpleProctoringMonitor', 'Simple proctoring JavaScript class'),
            ('proctoring-camera', 'Camera element on top left'),
            ('proctoring-status', 'Live proctoring status indicator'),
            ('violation-counter', 'Violation counter display'),
            ('tabSwitchCount', 'Tab switch detection counter'),
            ('enableFullscreen', 'Automatic fullscreen function'),
            ('initializeCamera', 'Camera initialization'),
            ('logViolation', 'Violation logging function'),
            ('updateViolationCounter', 'Violation counter update')
        ]
        
        print("📋 Component Verification:")
        all_present = True
        
        for component, description in required_components:
            if component in content:
                print(f"   ✅ {description}")
            else:
                print(f"   ❌ {description} - MISSING")
                all_present = False
        
        return all_present
        
    except Exception as e:
        print(f"❌ Error verifying implementation: {e}")
        return False

def verify_camera_positioning():
    """Verify camera is positioned on top left"""
    print("\n📹 CAMERA POSITIONING VERIFICATION")
    print("-" * 40)
    
    try:
        with open('student_test_interface.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        positioning_checks = [
            ('top: 20px', 'Camera positioned at top'),
            ('left: 20px', 'Camera positioned at left'),
            ('position: fixed', 'Camera has fixed positioning'),
            ('z-index: 1000', 'Camera has proper z-index'),
            ('width: 200px', 'Camera has correct width'),
            ('height: 150px', 'Camera has correct height')
        ]
        
        print("📍 Position Checks:")
        all_correct = True
        
        for check, description in positioning_checks:
            if check in content:
                print(f"   ✅ {description}")
            else:
                print(f"   ❌ {description} - MISSING")
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ Error verifying camera positioning: {e}")
        return False

def verify_monitoring_features():
    """Verify monitoring features are implemented"""
    print("\n🔍 MONITORING FEATURES VERIFICATION")
    print("-" * 40)
    
    try:
        with open('student_test_interface.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        monitoring_features = [
            ('visibilitychange', 'Tab switch detection'),
            ('fullscreenchange', 'Fullscreen monitoring'),
            ('window_blur', 'Window focus detection'),
            ('contextmenu', 'Right-click prevention'),
            ('keydown', 'Keyboard shortcut blocking'),
            ('enableFullscreen', 'Automatic fullscreen enforcement'),
            ('setInterval', 'Periodic monitoring checks')
        ]
        
        print("🔍 Feature Checks:")
        all_implemented = True
        
        for feature, description in monitoring_features:
            if feature in content:
                print(f"   ✅ {description}")
            else:
                print(f"   ❌ {description} - MISSING")
                all_implemented = False
        
        return all_implemented
        
    except Exception as e:
        print(f"❌ Error verifying monitoring features: {e}")
        return False

def setup_test_assignment():
    """Setup test assignment for testing"""
    print("\n📤 SETTING UP TEST ASSIGNMENT")
    print("-" * 40)
    
    db = get_db_session()
    try:
        # Get test student
        student = db.query(User).filter(
            User.email == '<EMAIL>',
            User.role == 'student'
        ).first()
        
        if not student:
            print("❌ Test student not found")
            return False
        
        # Get first available test
        test = db.query(Test).filter(Test.is_active == True).first()
        
        if not test:
            print("❌ No tests available")
            return False
        
        print(f"✅ Found student: {student.name}")
        print(f"✅ Found test: {test.title}")
        
        # Clear existing assignments
        existing_assignments = db.query(TestAssignment).filter(
            TestAssignment.student_id == student.id,
            TestAssignment.test_id == test.id
        ).all()
        
        for assignment in existing_assignments:
            db.delete(assignment)
        
        db.commit()
        
        # Create new assignment
        success, message = assign_test_to_student(
            test_id=test.id,
            student_id=student.id,
            deadline_hours=24,
            attempts_allowed=3
        )
        
        if success:
            print(f"✅ Test assigned successfully!")
            return True
        else:
            print(f"❌ Failed to assign test: {message}")
            return False
            
    except Exception as e:
        print(f"❌ Error setting up test: {e}")
        return False
    finally:
        db.close()

def show_testing_instructions():
    """Show testing instructions"""
    print("\n" + "=" * 50)
    print("🧪 SIMPLE PROCTORING TESTING INSTRUCTIONS")
    print("=" * 50)
    
    print("1. 🌐 Open student portal: http://localhost:8503")
    print("2. 🔑 Login with: <EMAIL> / student123")
    print("3. ▶️ Click 'Start Test' on the assigned test")
    print()
    print("🔍 What to Test:")
    print("   📹 Camera Preview:")
    print("      • Should appear on TOP LEFT corner")
    print("      • Live video feed should start automatically")
    print("      • Size: 200x150 pixels with green border")
    print()
    print("   🖥️ Automatic Fullscreen:")
    print("      • Should activate immediately when test starts")
    print("      • If you exit fullscreen, should re-enable automatically")
    print("      • Check every 5 seconds for enforcement")
    print()
    print("   🔄 Tab Switch Detection:")
    print("      • Switch to another tab/window")
    print("      • Should log violation and increment counter")
    print("      • Counter should update in real-time")
    print()
    print("   ⚠️ Violation Counter:")
    print("      • Located below camera on top left")
    print("      • Should start at 'Violations: 0'")
    print("      • Color changes: Green → Yellow → Red")
    print()
    print("📊 Monitor in Browser Console:")
    print("   • Press F12 to open developer tools")
    print("   • Check Console tab for proctoring logs")
    print("   • Look for: '✅ Simple proctoring initialized'")
    print("   • Violation logs: '🚨 Violation logged'")

def main():
    """Main testing function"""
    print("🚀 SIMPLE PROCTORING SETUP VERIFICATION")
    print("=" * 50)
    
    # Verify implementation
    implementation_ok = verify_simple_proctoring_implementation()
    camera_positioning_ok = verify_camera_positioning()
    monitoring_ok = verify_monitoring_features()
    
    print("\n" + "=" * 50)
    print("📊 VERIFICATION RESULTS")
    print("=" * 50)
    
    if implementation_ok:
        print("✅ Simple proctoring implementation: VERIFIED")
    else:
        print("❌ Simple proctoring implementation: ISSUES FOUND")
    
    if camera_positioning_ok:
        print("✅ Camera positioning (top left): VERIFIED")
    else:
        print("❌ Camera positioning: ISSUES FOUND")
    
    if monitoring_ok:
        print("✅ Monitoring features: VERIFIED")
    else:
        print("❌ Monitoring features: ISSUES FOUND")
    
    if implementation_ok and camera_positioning_ok and monitoring_ok:
        print("\n🎉 ALL VERIFICATIONS PASSED!")
        
        # Setup test assignment
        assignment_ok = setup_test_assignment()
        
        if assignment_ok:
            show_testing_instructions()
            
            print("\n" + "=" * 50)
            print("🎯 SIMPLE PROCTORING FEATURES IMPLEMENTED:")
            print("=" * 50)
            print("✅ Camera on top left corner (200x150px)")
            print("✅ Automatic camera initialization")
            print("✅ Automatic fullscreen enforcement")
            print("✅ Tab switch detection with counter")
            print("✅ Real-time violation logging")
            print("✅ Visual violation counter")
            print("✅ Simplified, clean interface")
            print("✅ Cross-browser compatibility")
            print("✅ Automatic proctoring startup")
            print("✅ Periodic monitoring checks")
        else:
            print("❌ Test assignment setup failed")
    else:
        print("\n❌ SOME VERIFICATIONS FAILED - CHECK IMPLEMENTATION")

if __name__ == "__main__":
    main()
