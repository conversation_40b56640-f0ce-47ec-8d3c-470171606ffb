"""
Update Database with New Tables
Adds TestAssignment and TestResult tables for the enhanced test management system
"""
from database_models import (
    Base, engine, User, Test, ProctorSession, MonitoringEvent, SessionMetrics,
    TestAssignment, TestResult
)

def update_database():
    """Create new tables for test assignments and results"""
    print("🔄 Updating database with new tables...")
    
    try:
        # Create all tables (will only create new ones)
        Base.metadata.create_all(engine)
        print("✅ Database updated successfully!")
        print("📋 New tables created:")
        print("   - test_assignments (for managing test assignments with deadlines)")
        print("   - test_results (for storing student answers and scores)")
        
    except Exception as e:
        print(f"❌ Error updating database: {e}")

if __name__ == "__main__":
    update_database()
