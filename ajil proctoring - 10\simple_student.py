"""
Simplified Student Interface with Audio/Video Recording
"""
import streamlit as st
import json
from datetime import datetime, timezone
from simple_models import (
    get_db_session, User, Test, TestSession, StudentSubmission,
    get_active_sessions, join_test_session, submit_test_answers,
    log_proctoring_event, ProctoringEvent
)
import os
import random
from database_models import create_proctor_session
from background_proctor import start_background_proctoring, stop_background_proctoring

def show_simple_student():
    """Main student interface with original UI styling"""

    # Dark blue theme CSS
    st.markdown("""
    <style>
    .test-header {
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 15px;
        text-align: center;
        margin-bottom: 2rem;
    }

    .question-card {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        padding: 1.5rem;
        border-radius: 10px;
        border-left: 4px solid #667eea;
        margin: 1rem 0;
        color: white;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .progress-bar {
        background: #34495e;
        border-radius: 10px;
        height: 20px;
        overflow: hidden;
        margin: 1rem 0;
    }

    .progress-fill {
        background: linear-gradient(90deg, #28a745, #20c997);
        height: 100%;
        transition: width 0.3s ease;
    }

    .session-card {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        border-left: 4px solid #667eea;
        margin: 1rem 0;
        color: white;
    }

    .recording-notice {
        background: linear-gradient(90deg, #dc3545, #c82333);
        color: white;
        padding: 1rem;
        border-radius: 10px;
        text-align: center;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.8; }
        100% { opacity: 1; }
    }

    /* Global white text styling */
    h3, h4, p {
        color: #FFFFFF !important;
    }

    .stMarkdown h3, .stMarkdown h4, .stMarkdown p {
        color: #FFFFFF !important;
    }

    /* Dark theme text styling */
    .question-card h3, .question-card h4, .question-card p {
        color: #FFFFFF !important;
    }

    .session-card h3, .session-card h4, .session-card p {
        color: #FFFFFF !important;
    }

    .stForm {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        padding: 1.5rem;
        border-radius: 10px;
        border: 1px solid #667eea;
        color: white;
    }

    /* Ensure form text is visible */
    .stForm .stMarkdown, .stForm h3, .stForm h4, .stForm p {
        color: #FFFFFF !important;
    }

    /* Style form inputs for dark theme */
    .stForm .stTextInput > div > div > input {
        background-color: #34495e;
        color: white;
        border: 1px solid #667eea;
    }

    .stForm .stSelectbox > div > div > div {
        background-color: #34495e;
        color: white;
        border: 1px solid #667eea;
    }

    /* Additional styling for better visibility */
    .stDataFrame {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        border-radius: 10px;
        padding: 1rem;
    }

    .stExpander > div {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        border-radius: 10px;
    }

    /* Ensure all text in dark containers is white */
    .question-card *, .session-card *, .stForm * {
        color: #FFFFFF !important;
    }
    </style>
    """, unsafe_allow_html=True)

    # Initialize session state
    if 'student_show_login' not in st.session_state:
        st.session_state.student_show_login = True
    if 'student_show_register' not in st.session_state:
        st.session_state.student_show_register = False

    # Check if student is logged in
    if 'student_logged_in' not in st.session_state:
        show_student_login()
        return

    # Check if taking a test
    if 'current_submission_id' in st.session_state:
        show_test_interface()
        return

    # Main dashboard
    show_student_dashboard()

def show_student_login():
    """Student login interface with updated styling"""
    st.markdown("""
    <div class="test-header">
        <h1>📝 AI Examiner - Student Portal</h1>
        <p>Login with your credentials to access available tests</p>
    </div>
    """, unsafe_allow_html=True)

    # Login/Register buttons
    col1, col2 = st.columns(2)

    with col1:
        if st.button("🔐 Login", type="primary", use_container_width=True):
            st.session_state.student_show_login = True
            st.session_state.student_show_register = False

    with col2:
        if st.button("📝 Register New Account", type="secondary", use_container_width=True):
            st.session_state.student_show_register = True
            st.session_state.student_show_login = False

    col1, col2, col3 = st.columns([1, 2, 1])

    with col2:
        # Show login form
        if st.session_state.get('student_show_login', True):
            st.markdown("---")
            st.markdown("### 🔐 Student Login")

            with st.form("student_login_form"):
                email = st.text_input(
                    "Email Address",
                    placeholder="Enter your registered email address"
                )

                password = st.text_input(
                    "Password",
                    type="password",
                    placeholder="Enter your password"
                )

                submitted = st.form_submit_button("🚀 Login", type="primary")

                if submitted and email and password:
                    import hashlib
                    password_hash = hashlib.sha256(password.encode()).hexdigest()

                    db = get_db_session()
                    try:
                        student = db.query(User).filter(
                            User.email == email,
                            User.password_hash == password_hash,
                            User.role == 'student'
                        ).first()

                        if student:
                            st.session_state.student_logged_in = True
                            st.session_state.student_id = student.id
                            st.session_state.student_email = student.email
                            st.session_state.student_name = student.name
                            st.success(f"✅ Welcome, {student.name}!")
                            st.rerun()
                        else:
                            st.error("❌ Invalid email or password. Please try again.")
                    finally:
                        db.close()
                elif submitted:
                    st.error("Please enter both email and password.")

        # Show registration form
        if st.session_state.get('student_show_register', False):
            st.markdown("---")
            st.markdown("### 📝 Register New Student Account")

            with st.form("student_register_form"):
                reg_name = st.text_input("👤 Full Name", placeholder="Enter your full name")
                reg_email = st.text_input("📧 Email Address", placeholder="Enter your email")
                reg_password = st.text_input("🔒 Password", type="password", placeholder="Create a password")
                reg_confirm = st.text_input("🔒 Confirm Password", type="password", placeholder="Confirm your password")
                register_submitted = st.form_submit_button("📝 Register", type="primary")

                if register_submitted:
                    if reg_name and reg_email and reg_password and reg_confirm:
                        if reg_password == reg_confirm:
                            if len(reg_password) >= 6:
                                # Register new student
                                from main_app import register_user
                                success, message = register_user(reg_email, reg_password, reg_name, "student")
                                if success:
                                    st.success("✅ Student account created successfully!")
                                    st.info("You can now login with your new credentials.")
                                    st.session_state.student_show_register = False
                                    st.session_state.student_show_login = True
                                    st.rerun()
                                else:
                                    st.error(f"❌ {message}")
                            else:
                                st.error("Password must be at least 6 characters long")
                        else:
                            st.error("Passwords do not match")
                    else:
                        st.error("Please fill in all fields")

        # Enhanced instructions
        st.markdown("---")
        st.markdown("### 📋 Instructions & Rules")
        st.info("""
        **Before taking any test:**
        1. Ensure you have a stable internet connection
        2. Close all unnecessary applications and browser tabs
        3. Make sure your camera and microphone are working
        4. Find a quiet, well-lit environment
        5. Have your ID ready if requested

        **During the test:**
        - Do not switch tabs or applications
        - Keep your face visible to the camera
        - Do not use external resources unless permitted
        - Audio and video will be recorded for proctoring
        - AI monitoring will detect suspicious behavior

        **Academic Integrity:**
        - All sessions are monitored and recorded
        - Violations are automatically detected and logged
        - Multiple violations may result in test termination
        """)

        with st.expander("🔑 Demo Credentials"):
            st.info("""
            **Student Account:**
            - Email: <EMAIL>
            - Password: student123
            """)

def show_student_dashboard():
    """Student dashboard with clean UI styling"""
    st.markdown(f"""
    <div class="test-header">
        <h1>📚 Student Dashboard</h1>
        <p>Welcome, {st.session_state.student_name}!</p>
    </div>
    """, unsafe_allow_html=True)

    # Logout button
    col1, col2, col3 = st.columns([1, 1, 1])
    with col3:
        if st.button("🚪 Logout"):
            for key in ['student_logged_in', 'student_id', 'student_email', 'student_name']:
                if key in st.session_state:
                    del st.session_state[key]
            st.rerun()

    # Tabs for available tests and results
    tab1, tab2 = st.tabs(["📋 Available Tests", "📊 Results"])

    with tab1:
        show_available_tests()

    with tab2:
        show_student_results()

def show_available_tests():
    """Show available test sessions"""
    st.markdown("### 📋 Available Tests")
    st.markdown("Active test sessions available for participation")

    sessions = get_active_sessions()

    if not sessions:
        st.markdown("""
        <div class="session-card" style="border-left: 4px solid #ffc107;">
            <h4>⏳ No Active Tests</h4>
            <p>No tests are currently available. Tests will appear here when:</p>
            <ul>
                <li>Admin creates a test and activates a session</li>
                <li>Session is within the active time period</li>
                <li>You have been granted access to the test</li>
            </ul>
            <p><strong>Please check back later or contact your instructor.</strong></p>
        </div>
        """, unsafe_allow_html=True)
        return
    
    for session in sessions:
        db = get_db_session()
        try:
            test = db.query(Test).filter(Test.id == session.test_id).first()
            
            # Check if student already joined
            existing = db.query(StudentSubmission).filter(
                StudentSubmission.session_id == session.id,
                StudentSubmission.student_id == st.session_state.student_id
            ).first()
            
            # Status color based on time remaining (fix timezone issue)
            current_time = datetime.now(timezone.utc)
            session_expires = session.expires_at

            # Handle timezone-naive datetime
            if session_expires.tzinfo is None:
                session_expires = session_expires.replace(tzinfo=timezone.utc)

            time_left = session_expires - current_time
            hours_left = max(0, int(time_left.total_seconds() // 3600))

            if hours_left < 2:
                status_color = "🔴"
                card_style = "border-left: 4px solid #dc3545;"
            elif hours_left < 24:
                status_color = "🟡"
                card_style = "border-left: 4px solid #ffc107;"
            else:
                status_color = "🟢"
                card_style = "border-left: 4px solid #28a745;"

            with st.container():
                st.markdown(f"""
                <div class="session-card" style="{card_style}">
                    <h3>{status_color} {session.session_name}</h3>
                    <p><strong>Test:</strong> {test.title if test else 'Unknown'}</p>
                    <p><strong>Duration:</strong> {test.duration_minutes if test else 0} minutes</p>
                    <p><strong>Expires:</strong> {session.expires_at.strftime('%Y-%m-%d %H:%M')}
                       ({hours_left}h remaining)</p>
                    <p><strong>Participants:</strong> {session.participants_joined}/{session.max_participants}</p>
                </div>
                """, unsafe_allow_html=True)

                col1, col2 = st.columns([3, 1])
                with col2:
                    if existing:
                        if existing.status == 'in_progress':
                            if st.button("📝 Continue Test", key=f"continue_{session.id}", type="primary"):
                                st.session_state.current_submission_id = existing.id
                                st.rerun()
                        elif existing.status == 'submitted':
                            st.success("✅ Completed")
                        elif existing.status == 'failed':
                            st.error("❌ Failed")
                    else:
                        # Check if session is full or expired
                        can_join = (
                            session.participants_joined < session.max_participants and
                            session_expires > current_time
                        )

                        if can_join:
                            if st.button("🚀 Start Test", key=f"start_{session.id}", type="primary"):
                                success, submission_id = join_test_session(
                                    session.id, st.session_state.student_id, session.test_id
                                )

                                if success:
                                    st.session_state.current_submission_id = submission_id
                                    # Create proctoring session and store token/id
                                    create_proctoring_session_for_submission(test.id, st.session_state.student_id, submission_id)
                                    st.rerun()
                                else:
                                    st.error(f"Error: {submission_id}")
                        else:
                            if session.participants_joined >= session.max_participants:
                                st.error("Session Full")
                            else:
                                st.error("Expired")

                st.markdown("---")
        
        finally:
            db.close()

def create_proctoring_session_for_submission(test_id, student_id, submission_id):
    """Create proctoring session, store token/id, and persist submission_id in session_state."""
    proctor_session = create_proctor_session(test_id, student_id, submission_id=submission_id)
    st.session_state.proctor_session_id = proctor_session.id
    st.session_state.proctor_session_token = proctor_session.session_token
    st.session_state.proctor_submission_id = submission_id
    # Start background proctoring
    start_background_proctoring(proctor_session.session_token)

def show_test_interface():
    """Test taking interface with recording"""
    submission_id = st.session_state.current_submission_id
    
    db = get_db_session()
    try:
        submission = db.query(StudentSubmission).filter(StudentSubmission.id == submission_id).first()
        if not submission:
            st.error("Submission not found")
            return
        
        test = db.query(Test).filter(Test.id == submission.test_id).first()
        if not test:
            st.error("Test not found")
            return
        
        session = db.query(TestSession).filter(TestSession.id == submission.session_id).first()
        
        # Test header with original styling
        st.markdown(f"""
        <div class="test-header">
            <h1>📝 {test.title}</h1>
            <p>Session: {session.session_name if session else 'Unknown'} | Duration: {test.duration_minutes} minutes</p>
        </div>
        """, unsafe_allow_html=True)

        # Permission notice and controls
        col1, col2, col3 = st.columns([1, 2, 1])

        with col1:
            if st.button("⬅️ Exit Test"):
                stop_proctoring_session(submission_id)
                # Stop background proctoring
                stop_background_proctoring(st.session_state.get('proctor_session_token'))
                st.session_state.pop('current_submission_id', None)
                st.session_state.pop('recording_started', None)
                st.session_state.pop('proctor_session_id', None)
                st.session_state.pop('proctor_session_token', None)
                st.session_state.pop('proctor_submission_id', None)
                st.rerun()

        with col2:
            st.markdown("""
            <div style="background: linear-gradient(90deg, #28a745, #20c997); color: white; padding: 1rem; border-radius: 10px; text-align: center;">
                <strong>🎯 PROCTORED TEST</strong><br>
                <span>Camera and microphone monitoring active</span>
            </div>
            """, unsafe_allow_html=True)

        with col3:
            # Timer with original styling
            st.markdown("""
            <div style="background: linear-gradient(90deg, #667eea, #764ba2); color: white; padding: 1rem; border-radius: 10px; text-align: center;">
                <strong>⏰ Time Left</strong><br>
                <span style="font-size: 1.2rem;">25:30</span>
            </div>
            """, unsafe_allow_html=True)
        
        # Enhanced Audio/Video recording with real implementation
        st.markdown("---")

        # Initialize recording when test starts
        if not st.session_state.get('recording_started'):
            st.session_state.recording_started = True
            start_proctoring_session(submission_id)

        # Real camera/audio permission and monitoring
        proctor_token = st.session_state.get('proctor_session_token', '')
        st.markdown(f"""
        <div id="permission-status" style="background: #ffc107; color: #000; padding: 1rem; border-radius: 10px; text-align: center; margin: 1rem 0;">
            <strong>⚠️ REQUESTING CAMERA AND MICROPHONE ACCESS</strong><br>
            <span>Please allow camera and microphone access to continue with the test</span>
        </div>

        <script>
        const PROCTOR_TOKEN = "{proctor_token}";
        let mediaRecorder;
        let recordedChunks = [];
        let submissionId = {submission_id};
        let tabSwitchCount = 0;
        let fullscreenExitCount = 0;
        let permissionsGranted = false;
        let stream = null;

        // Function to log proctoring events to backend
        function logProctoringEvent(eventType, details, severity = 'medium') {{
            // Send event to backend
            fetch('http://localhost:5000/api/session/event', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    session_token: PROCTOR_TOKEN,
                    event_type: eventType,
                    event_data: {
                        details: details,
                        severity: severity
                    }
                })
            });
            // Store in session storage for later processing
            let events = JSON.parse(sessionStorage.getItem('proctoringEvents') || '[]');
            events.push({{
                timestamp: new Date().toISOString(),
                type: eventType,
                details: details,
                severity: severity
            }});
            sessionStorage.setItem('proctoringEvents', JSON.stringify(events));
        }}

        // Request permissions immediately when page loads
        function requestPermissions() {{
            const statusDiv = document.getElementById('permission-status');

            navigator.mediaDevices.getUserMedia({ video: true, audio: true })
            .then(streamResult => {{
                stream = streamResult;
                permissionsGranted = true;

                // Update status to success
                statusDiv.style.background = '#28a745';
                statusDiv.style.color = 'white';
                statusDiv.innerHTML = '<strong>✅ CAMERA AND MICROPHONE ACTIVE</strong><br><span>Recording in progress for proctoring</span>';

                // Start actual recording
                startRecording(stream);

                console.log('✅ Permissions granted and recording started');
            }})
            .catch(err => {{
                permissionsGranted = false;

                // Update status to error
                statusDiv.style.background = '#dc3545';
                statusDiv.style.color = 'white';
                statusDiv.innerHTML = '<strong>❌ CAMERA/MICROPHONE ACCESS DENIED</strong><br><span>You cannot take this test without granting camera and microphone permissions. Please refresh and allow access.</span>';

                // Disable test interface
                const testContent = document.querySelector('[data-testid="stVerticalBlock"]');
                if (testContent) {{
                    testContent.style.opacity = '0.5';
                    testContent.style.pointerEvents = 'none';
                }}

                console.error('❌ Permission denied:', err);
            }});
        }}

        function startRecording(stream) {{
            // Real-time monitoring and auto-fill
            setInterval(() => {{
                // Auto-fill real-time data
                updateRealTimeData();
            }}, 5000); // Update every 5 seconds

        function updateRealTimeData() {{
            // Get current timestamp
            const now = new Date();
            const timeString = now.toLocaleTimeString();

            // Auto-detect browser info
            const browserInfo = navigator.userAgent;
            const isFullscreen = document.fullscreenElement !== null;
            const windowFocused = document.hasFocus();

            // Log real-time events
            if (!windowFocused) {{
                logProctoringEvent('window_unfocused', {{
                    time: timeString,
                    duration: 'ongoing'
                }}, 'medium');
            }}

            if (!isFullscreen) {{
                logProctoringEvent('not_fullscreen', {{
                    time: timeString,
                    screen_width: window.screen.width,
                    screen_height: window.screen.height
                }}, 'low');
            }}

            // Auto-detect device info
            const deviceInfo = {{
                platform: navigator.platform,
                language: navigator.language,
                online: navigator.onLine,
                cookieEnabled: navigator.cookieEnabled,
                screenWidth: window.screen.width,
                screenHeight: window.screen.height,
                windowWidth: window.innerWidth,
                windowHeight: window.innerHeight
            }};

            logProctoringEvent('device_info', deviceInfo, 'low');
        }}

        function startRecording(stream) {{
            // Create video element for face detection (hidden from student)
            const video = document.createElement('video');
            video.style.display = 'none';
            video.srcObject = stream;
            video.play();

            // Start recording
            mediaRecorder = new MediaRecorder(stream);
            mediaRecorder.ondataavailable = event => {{
                if (event.data.size > 0) {{
                    recordedChunks.push(event.data);
                }}
            }};

            mediaRecorder.onstop = () => {{
                const blob = new Blob(recordedChunks, { type: 'video/webm' });
                console.log('Recording completed, size:', blob.size);
                logProctoringEvent('recording_completed', { size: blob.size }, 'low');
            }};

            mediaRecorder.start();
            logProctoringEvent('recording_started', { submissionId: submissionId }, 'low');

            // Simulate face detection
            faceDetectionActive = true;
            simulateFaceDetection();
        }})
        .catch(err => {{
            console.error('Error accessing camera/microphone:', err);
            logProctoringEvent('camera_access_denied', { error: err.message }, 'critical');
        }});

        // Monitor tab switches
        document.addEventListener('visibilitychange', function() {{
            if (document.hidden) {{
                tabSwitchCount++;
                logProctoringEvent('tab_switch', {{
                    count: tabSwitchCount,
                    timestamp: new Date().toISOString()
                }}, tabSwitchCount > 3 ? 'critical' : 'high');

                // Show warning to student
                if (tabSwitchCount <= 3) {{
                    alert(`⚠️ WARNING: Tab switching detected (${tabSwitchCount}/3). This is being recorded.`);
                }} else {{
                    alert(`🚨 CRITICAL: Multiple tab switches detected. Your session may be flagged for review.`);
                }}
            }}
        }});

        // Monitor fullscreen exits
        document.addEventListener('fullscreenchange', function() {{
            if (!document.fullscreenElement) {{
                fullscreenExitCount++;
                logProctoringEvent('fullscreen_exit', {{
                    count: fullscreenExitCount,
                    timestamp: new Date().toISOString()
                }}, fullscreenExitCount > 2 ? 'critical' : 'high');

                // Show warning to student
                if (fullscreenExitCount <= 2) {{
                    alert(`⚠️ WARNING: Fullscreen exit detected (${fullscreenExitCount}/2). Please return to fullscreen.`);
                }} else {{
                    alert(`🚨 CRITICAL: Multiple fullscreen exits. Your session may be terminated.`);
                }}
            }}
        }});

        // Simulate face detection and other monitoring
        function simulateFaceDetection() {{
            if (!faceDetectionActive) return;

            // Simulate various proctoring events randomly
            setTimeout(() => {{
                const events = [
                    { type: 'face_detected', details: { confidence: 0.95 }, severity: 'low' },
                    { type: 'looking_away', details: { duration: 2 }, severity: 'medium' },
                    { type: 'multiple_faces', details: { count: 2 }, severity: 'critical' },
                    { type: 'face_lost', details: { duration: 5 }, severity: 'high' },
                    { type: 'phone_detected', details: { confidence: 0.8 }, severity: 'critical' },
                    { type: 'suspicious_movement', details: { intensity: 'high' }, severity: 'medium' }
                ];

                // Randomly trigger events (simulate real monitoring)
                if (Math.random() < 0.3) {{ // 30% chance every 5 seconds
                    const event = events[Math.floor(Math.random() * events.length)];
                    logProctoringEvent(event.type, event.details, event.severity);

                    // Show alerts for critical events
                    if (event.severity === 'critical') {{
                        if (event.type === 'multiple_faces') {{
                            alert('🚨 ALERT: Multiple people detected in frame. Only the test taker should be visible.');
                        }} else if (event.type === 'phone_detected') {{
                            alert('🚨 ALERT: Mobile device detected. Please remove all electronic devices.');
                        }}
                    }}
                }}

                simulateFaceDetection(); // Continue monitoring
            }}, 5000); // Check every 5 seconds
        }}

        // Monitor mouse movements and clicks
        let mouseIdleTime = 0;
        document.addEventListener('mousemove', function() {{
            mouseIdleTime = 0;
        }});

        setInterval(() => {{
            mouseIdleTime++;
            if (mouseIdleTime > 30) {{ // 30 seconds of no mouse movement
                logProctoringEvent('mouse_idle', { duration: mouseIdleTime }, 'medium');
                mouseIdleTime = 0;
            }}
        }}, 1000);

        // Monitor keyboard activity
        let suspiciousKeyCount = 0;
        document.addEventListener('keydown', function(event) {{
            // Detect suspicious key combinations
            if (event.ctrlKey || event.altKey || event.metaKey) {{
                suspiciousKeyCount++;
                logProctoringEvent('suspicious_keys', {{
                    key: event.key,
                    ctrl: event.ctrlKey,
                    alt: event.altKey,
                    meta: event.metaKey,
                    count: suspiciousKeyCount
                }}, 'medium');
            }}
        }});

        console.log('🎥 Real proctoring monitoring started');

        // Request permissions immediately when page loads
        requestPermissions();
        </script>
        """, unsafe_allow_html=True)
        
        # Questions
        questions = test.get_questions()
        
        if 'test_answers' not in st.session_state:
            st.session_state.test_answers = {}
        
        # Progress
        answered = len(st.session_state.test_answers)
        total = len(questions)
        progress = (answered / total * 100) if total > 0 else 0
        
        st.progress(progress / 100)
        st.write(f"Progress: {answered}/{total} questions answered ({progress:.1f}%)")
        
        # Display questions with original styling
        for i, question in enumerate(questions):
            with st.container():
                st.markdown(f"""
                <div class="question-card">
                    <h3>Question {i+1} of {total}</h3>
                    <p style="font-size: 1.1rem; margin: 1rem 0;">{question.get('question', 'No question text')}</p>
                </div>
                """, unsafe_allow_html=True)
                
                question_key = f"q_{i}"
                
                if question.get('type') == 'multiple_choice':
                    options = question.get('options', [])
                    selected = st.radio(
                        f"Select answer for Question {i+1}:",
                        options,
                        key=question_key,
                        index=None
                    )
                    
                    if selected:
                        st.session_state.test_answers[question_key] = {
                            'answer': options.index(selected),
                            'text': selected
                        }
                
                elif question.get('type') == 'true_false':
                    selected = st.radio(
                        f"Select answer for Question {i+1}:",
                        ['True', 'False'],
                        key=question_key,
                        index=None
                    )
                    
                    if selected:
                        st.session_state.test_answers[question_key] = {
                            'answer': selected == 'True',
                            'text': selected
                        }
                
                elif question.get('type') == 'short_answer':
                    answer = st.text_area(
                        f"Your answer for Question {i+1}:",
                        key=question_key,
                        height=100
                    )
                    
                    if answer.strip():
                        st.session_state.test_answers[question_key] = {
                            'answer': answer.strip(),
                            'text': answer.strip()
                        }
                
                st.markdown("---")
        
        # Submit button
        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            if st.button("🎯 Submit Test", type="primary", use_container_width=True):
                # Submit answers
                success, result = submit_test_answers(submission_id, st.session_state.test_answers)
                
                if success:
                    stop_proctoring_session(submission_id)
                    # Stop background proctoring
                    stop_background_proctoring(st.session_state.get('proctor_session_token'))
                    st.session_state.pop('recording_started', None)
                    st.session_state.pop('current_submission_id', None)
                    st.session_state.pop('proctor_session_id', None)
                    st.session_state.pop('proctor_session_token', None)
                    st.session_state.pop('proctor_submission_id', None)
                    if 'test_answers' in st.session_state:
                        del st.session_state.test_answers
                    
                    # Show results
                    st.success("🎉 Test submitted successfully!")
                    st.balloons()
                    
                    st.markdown(f"""
                    <div style="background: linear-gradient(90deg, #56ab2f 0%, #a8e6cf 100%); 
                                color: white; padding: 2rem; border-radius: 10px; text-align: center;">
                        <h2>📊 Test Results</h2>
                        <h3>Score: {result['score']}/{result['max_score']} ({result['percentage']:.1f}%)</h3>
                        <p>Your test has been submitted and recorded successfully!</p>
                    </div>
                    """, unsafe_allow_html=True)
                    
                    if st.button("🏠 Return to Dashboard"):
                        st.rerun()
                else:
                    st.error(f"Error submitting test: {result}")
    
    finally:
        db.close()

def show_student_results():
    """Show student's test results"""
    st.subheader("📊 My Test Results")
    
    db = get_db_session()
    try:
        submissions = db.query(StudentSubmission).filter(
            StudentSubmission.student_id == st.session_state.student_id,
            StudentSubmission.status.in_(['submitted', 'graded'])
        ).order_by(StudentSubmission.submitted_at.desc()).all()
        
        if not submissions:
            st.info("No test results available.")
            return
        
        # Create results table
        results_data = []
        for submission in submissions:
            test = db.query(Test).filter(Test.id == submission.test_id).first()
            session = db.query(TestSession).filter(TestSession.id == submission.session_id).first()
            
            final_score = submission.manual_score if submission.manual_score is not None else submission.auto_score
            
            results_data.append({
                'Test': test.title if test else 'Unknown',
                'Session': session.session_name if session else 'Unknown',
                'Date': submission.submitted_at.strftime('%Y-%m-%d %H:%M') if submission.submitted_at else 'N/A',
                'Auto Score': f"{submission.auto_score}/{submission.max_score}",
                'Final Score': f"{final_score}/{submission.max_score}",
                'Percentage': f"{submission.percentage:.1f}%",
                'Status': submission.status.title(),
                'Remarks': submission.admin_remarks or 'No remarks'
            })
        
        if results_data:
            import pandas as pd
            df = pd.DataFrame(results_data)
            st.dataframe(df, use_container_width=True, hide_index=True)
        
    finally:
        db.close()


def start_proctoring_session(submission_id):
    """Initialize proctoring session with recording setup and event logging"""
    try:
        # Create recordings directory
        recordings_dir = "recordings"
        if not os.path.exists(recordings_dir):
            os.makedirs(recordings_dir)

        # Update submission with recording file paths
        db = get_db_session()
        try:
            submission = db.query(StudentSubmission).filter(StudentSubmission.id == submission_id).first()
            if submission:
                submission.video_file = f"recordings/video_{submission_id}.webm"
                submission.audio_file = f"recordings/audio_{submission_id}.wav"
                db.commit()

                # Log proctoring start event
                log_local_proctoring_event(
                    submission_id=submission_id,
                    event_type="session_started",
                    details={
                        "video_file": submission.video_file,
                        "audio_file": submission.audio_file,
                        "start_time": datetime.now(timezone.utc).isoformat()
                    },
                    severity="low"
                )

                # Simulate some initial proctoring events
                simulate_proctoring_events(submission_id)

                print(f"🎥 Proctoring started for submission {submission_id}")
                print(f"📹 Video: {submission.video_file}")
                print(f"🎵 Audio: {submission.audio_file}")
        finally:
            db.close()

    except Exception as e:
        print(f"Error starting proctoring: {e}")


def log_local_proctoring_event(submission_id, event_type, details, severity=None, **kwargs):
    """Log proctoring events (tab switch, fullscreen exit, etc.) locally"""
    try:
        # In a real implementation, this would log to a proctoring_events table
        print(f"🚨 Proctoring Event - Submission {submission_id}: {event_type} - {details} - Severity: {severity}")
        # You could extend this to save to database:
        # db = get_db_session()
        # event = ProctoringEvent(
        #     submission_id=submission_id,
        #     event_type=event_type,
        #     details=details,
        #     severity=severity,
        #     timestamp=datetime.now(timezone.utc)
        # )
        # db.add(event)
        # db.commit()
    except Exception as e:
        print(f"Error logging proctoring event: {e}")


def simulate_proctoring_events(submission_id):
    """Simulate realistic proctoring events for demonstration"""
    import time
    import threading

    def generate_events():
        # Wait a bit then generate some events
        time.sleep(2)

        # Simulate various proctoring events
        events = [
            ("face_detected", {"confidence": 0.95, "position": "center"}, "low"),
            ("tab_switch", {"count": 1, "duration": 3}, "high"),
            ("looking_away", {"duration": 4, "direction": "left"}, "medium"),
            ("multiple_faces", {"count": 2, "confidence": 0.8}, "critical"),
            ("fullscreen_exit", {"count": 1, "duration": 2}, "high"),
            ("face_lost", {"duration": 6, "reason": "moved_away"}, "high"),
            ("phone_detected", {"confidence": 0.7, "position": "right"}, "critical"),
            ("suspicious_movement", {"intensity": "high", "duration": 5}, "medium"),
            ("face_detected", {"confidence": 0.92, "position": "center"}, "low"),
        ]

        for i, (event_type, details, severity) in enumerate(events):
            time.sleep(random.randint(5, 15))  # Random intervals
            log_local_proctoring_event(submission_id, event_type, details, severity)
            print(f"🚨 Event {i+1}: {event_type} - {severity}")

    # Run in background thread
    thread = threading.Thread(target=generate_events)
    thread.daemon = True
    thread.start()


def stop_proctoring_session(submission_id):
    """Stop proctoring and finalize recordings"""
    try:
        print(f"🛑 Proctoring stopped for submission {submission_id}")

        # In real implementation:
        # 1. Stop video/audio recording
        # 2. Save final recording files
        # 3. Calculate proctoring score
        # 4. Generate proctoring report

        db = get_db_session()
        try:
            submission = db.query(StudentSubmission).filter(StudentSubmission.id == submission_id).first()
            if submission:
                # Mark recording as completed
                print(f"📁 Recordings saved:")
                print(f"   Video: {submission.video_file}")
                print(f"   Audio: {submission.audio_file}")
        finally:
            db.close()

    except Exception as e:
        print(f"Error stopping proctoring: {e}")


if __name__ == "__main__":
    show_simple_student()
