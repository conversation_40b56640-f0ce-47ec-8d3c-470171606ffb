"""
Run Student Interface Only
Dedicated student portal on port 8502
"""
import streamlit as st
from simple_student import show_simple_student

def main():
    """Student-only interface"""
    st.set_page_config(
        page_title="Student Portal - Proctoring System",
        page_icon="📝",
        layout="wide",
        initial_sidebar_state="collapsed"
    )
    
    # Force student interface
    show_simple_student()

if __name__ == "__main__":
    main()
