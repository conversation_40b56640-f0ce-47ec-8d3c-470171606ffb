"""
Fix student assignments and create fresh demo tests
"""
import json
from datetime import datetime, timezone, timedelta
from database_models import (
    get_db_session, create_test, assign_test_to_student, 
    User, Test, TestAssignment
)

def reset_student_assignments():
    """Reset assignments for the test student and create fresh ones"""
    print("🔧 Resetting student assignments...")
    
    db = get_db_session()
    try:
        # Find the test student
        student = db.query(User).filter(User.email == '<EMAIL>').first()
        if not student:
            print("❌ Test student not found!")
            return False
        
        print(f"✅ Found student: {student.name} (ID: {student.id})")
        
        # Reset existing assignments to allow retaking
        assignments = db.query(TestAssignment).filter(
            TestAssignment.student_id == student.id
        ).all()
        
        for assignment in assignments:
            if assignment.status == 'completed':
                assignment.status = 'assigned'
                assignment.attempts_used = 0
                print(f"   🔄 Reset assignment for test ID {assignment.test_id}")
        
        db.commit()
        print(f"✅ Reset {len(assignments)} assignments")
        
        return True
        
    except Exception as e:
        print(f"❌ Error resetting assignments: {e}")
        return False
    finally:
        db.close()

def create_fresh_demo_test():
    """Create a new fresh demo test"""
    print("\n🎯 Creating fresh demo test...")
    
    # Fresh demo test data
    test_data = {
        "title": "Fresh AI Proctoring Demo Test",
        "description": "A brand new demo test to showcase AI proctoring features. Perfect for testing the system!",
        "duration_minutes": 12,
        "questions": [
            {
                "id": 1,
                "type": "multiple_choice",
                "question": "Which technology is primarily used for AI proctoring?",
                "options": [
                    "Blockchain technology",
                    "Computer vision and machine learning",
                    "Virtual reality systems",
                    "Quantum computing"
                ],
                "correct_answer": 1,
                "points": 10,
                "explanation": "AI proctoring relies heavily on computer vision to analyze video feeds and machine learning to detect suspicious behavior."
            },
            {
                "id": 2,
                "type": "true_false",
                "question": "AI proctoring can detect when a student looks away from the screen for extended periods.",
                "correct_answer": True,
                "points": 10,
                "explanation": "AI proctoring systems use eye tracking and gaze detection to monitor where students are looking."
            },
            {
                "id": 3,
                "type": "multiple_choice",
                "question": "What should students do if they experience technical issues during a proctored exam?",
                "options": [
                    "Continue the exam and report issues later",
                    "Immediately contact technical support or the instructor",
                    "Restart their computer and begin again",
                    "Switch to a different device"
                ],
                "correct_answer": 1,
                "points": 10,
                "explanation": "Students should immediately contact support to avoid any academic integrity issues and get proper assistance."
            },
            {
                "id": 4,
                "type": "short_answer",
                "question": "List three things students should prepare before starting an AI-proctored exam.",
                "points": 15,
                "sample_answer": "Students should: 1) Ensure stable internet connection, 2) Test camera and microphone functionality, 3) Find a quiet, well-lit environment with minimal distractions."
            }
        ],
        "proctoring_settings": {
            "camera_required": True,
            "microphone_required": True,
            "fullscreen_required": True,
            "tab_switching_detection": True,
            "face_detection": True,
            "multiple_person_detection": True,
            "phone_detection": True,
            "violation_threshold": 3
        }
    }
    
    # Create the test
    success, result = create_test(
        title=test_data["title"],
        description=test_data["description"],
        duration_minutes=test_data["duration_minutes"],
        questions=test_data["questions"],
        settings=test_data["proctoring_settings"],
        created_by=1  # Admin user ID
    )
    
    if not success:
        print(f"❌ Failed to create test: {result}")
        return False
    
    test_id = result
    print(f"✅ Fresh demo test created successfully! Test ID: {test_id}")
    
    # Activate the test
    db = get_db_session()
    try:
        test = db.query(Test).filter(Test.id == test_id).first()
        if test:
            test.is_active = True
            db.commit()
            print(f"✅ Test activated successfully!")
        else:
            print(f"❌ Could not find test to activate")
            return False
    finally:
        db.close()
    
    return test_id

def assign_fresh_test_to_students(test_id):
    """Assign the fresh test to all students"""
    print(f"\n📤 Assigning fresh test (ID: {test_id}) to students...")
    
    db = get_db_session()
    try:
        students = db.query(User).filter(
            User.role == 'student', 
            User.is_active == True
        ).all()
        
        if students:
            success_count = 0
            for student in students:
                success, token = assign_test_to_student(
                    test_id=test_id,
                    student_id=student.id,
                    deadline_hours=96,  # 4 days to complete
                    attempts_allowed=3  # Allow multiple attempts
                )
                
                if success:
                    success_count += 1
                    print(f"   ✅ Assigned to {student.name} ({student.email})")
                else:
                    print(f"   ❌ Failed to assign to {student.name}: {token}")
            
            print(f"🎉 Successfully assigned fresh test to {success_count} students!")
            return True
        else:
            print("⚠️  No students found to assign test to")
            return False
            
    finally:
        db.close()

def main():
    """Main fix function"""
    print("=== FIXING STUDENT ASSIGNMENTS ===")
    
    # Step 1: Reset existing assignments
    if reset_student_assignments():
        print("✅ Student assignments reset successfully!")
    else:
        print("❌ Failed to reset student assignments")
        return
    
    # Step 2: Create fresh demo test
    fresh_test_id = create_fresh_demo_test()
    if fresh_test_id:
        print("✅ Fresh demo test created successfully!")
    else:
        print("❌ Failed to create fresh demo test")
        return
    
    # Step 3: Assign fresh test to all students
    if assign_fresh_test_to_students(fresh_test_id):
        print("✅ Fresh test assigned successfully!")
    else:
        print("❌ Failed to assign fresh test")
        return
    
    print("\n🎉 STUDENT ASSIGNMENT FIX COMPLETED!")
    print("\n📋 Summary:")
    print("1. ✅ Reset existing assignments (students can retake)")
    print("2. ✅ Created new 'Fresh AI Proctoring Demo Test' (4 questions, 12 minutes)")
    print("3. ✅ Assigned to all students with 3 attempts and 4-day deadline")
    print("\n🔗 Test the fix:")
    print("1. Login to student portal: http://localhost:8503")
    print("2. Use credentials: <EMAIL> / student123")
    print("3. You should now see multiple available tests!")

if __name__ == "__main__":
    main()
