"""
Create Sample Test and Session
Quick script to create sample data for testing
"""
import json
from database_models import create_test, create_proctor_session, get_db_session, User

def create_sample_data():
    """Create sample test and session for demonstration"""
    print("🎯 Creating sample test and session...")
    
    # Sample questions
    sample_questions = [
        {
            'question': 'What is the primary factor that influences the efficiency of a system in achieving its objectives?',
            'type': 'multiple_choice',
            'options': [
                'The alignment of its components towards a common goal',
                'The amount of resources allocated to each component',
                'The speed at which the components operate independently',
                'The historical performance of the system in past scenarios'
            ],
            'correct_answer': 0,
            'points': 1,
            'difficulty': 'Medium'
        },
        {
            'question': 'True or False: Effective communication is essential for team collaboration.',
            'type': 'true_false',
            'correct_answer': True,
            'points': 1,
            'difficulty': 'Easy'
        },
        {
            'question': 'Explain the importance of feedback mechanisms in organizational systems.',
            'type': 'short_answer',
            'correct_answer': 'Feedback mechanisms are crucial for continuous improvement, error correction, and adaptive learning in organizational systems.',
            'points': 3,
            'difficulty': 'Hard'
        },
        {
            'question': 'Which of the following best describes innovation distinction?',
            'type': 'multiple_choice',
            'options': [
                'The ability to copy existing solutions',
                'The capacity to create novel and valuable solutions',
                'The process of implementing standard procedures',
                'The method of following established protocols'
            ],
            'correct_answer': 1,
            'points': 1,
            'difficulty': 'Medium'
        },
        {
            'question': 'Collaboration in projects requires clear role definition.',
            'type': 'true_false',
            'correct_answer': True,
            'points': 1,
            'difficulty': 'Easy'
        }
    ]
    
    # Test configuration
    test_config = {
        'title': 'System Efficiency Assessment',
        'format': 'Mixed',
        'total_questions': 5,
        'difficulty': 'Medium',
        'time_limit': 30,
        'passing_score': 70,
        'proctoring': {
            'camera': True,
            'screen_recording': False,
            'face_detection': True,
            'strict_mode': True
        },
        'settings': {
            'negative_marking': False,
            'randomize': True,
            'auto_submit': True,
            'show_timer': True,
            'warning_time': 5,
            'grace_period': 30
        }
    }
    
    # Create test
    success, message = create_test(
        title="System Efficiency Assessment",
        description="A comprehensive test covering system efficiency, communication, and innovation concepts",
        duration_minutes=30,
        questions=json.dumps({
            'config': test_config,
            'questions': sample_questions
        }),
        settings=json.dumps(test_config['settings']),
        created_by="<EMAIL>"
    )
    
    if success:
        print(f"✅ Sample test created successfully! Test ID: {message}")
        test_id = message
        
        # Get a student to create session for
        db = get_db_session()
        try:
            student = db.query(User).filter(
                User.role == 'student',
                User.is_active == True
            ).first()
            
            if student:
                # Create a proctoring session
                session = create_proctor_session(test_id, student.id)
                print(f"✅ Sample session created! Session Token: {session.session_token}")
                print(f"📝 Student Test URL: http://localhost:8503?token={session.session_token}")
                print(f"👤 Student Email: {student.email}")
                
                print("\n🎉 Sample data created successfully!")
                print("\n📋 Quick Test Instructions:")
                print("1. Make sure the student portal is running: http://localhost:8503")
                print("2. Use the test URL above or enter the session token manually")
                print(f"3. Login with student email: {student.email}")
                print("4. Take the test and see the results!")
                
                return session.session_token, student.email
            else:
                print("❌ No student found. Please create a student user first.")
                return None, None
        
        finally:
            db.close()
    else:
        print(f"❌ Error creating test: {message}")
        return None, None

if __name__ == "__main__":
    create_sample_data()
