from flask import Flask, jsonify, request
from flask_cors import CORS
import threading
import time
import logging
import json

# Import background proctoring functionality
try:
    from background_proctor import (
        start_background_proctoring, stop_background_proctoring,
        log_browser_event, proctor_manager
    )
    from database_models import get_session_by_token
except ImportError as e:
    print(f"Warning: Could not import enhanced modules: {e}")
    # Fallback for basic functionality
    def start_background_proctoring(token): return False
    def stop_background_proctoring(token): return False
    def log_browser_event(token, event, data): return False
    def get_session_by_token(token): return None
    class MockManager:
        def get_active_sessions(self): return []
    proctor_manager = MockManager()

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Store test data (legacy support)
test_data = {
    "tab_switches": 0,
    "fullscreen_exits": 0,
    "duration": 0,
    "test_completed": False
}

# Legacy endpoint for backward compatibility
@app.route('/api/test-data', methods=['GET'])
def get_test_data():
    logger.info(f"GET /api/test-data - Returning: {test_data}")
    return jsonify(test_data)


# New enhanced API endpoints
@app.route('/api/session/start', methods=['POST'])
def start_session():
    """Start a background proctoring session"""
    try:
        data = request.get_json()
        session_token = data.get('session_token')

        if not session_token:
            return jsonify({'error': 'session_token is required'}), 400

        # Verify session exists
        session = get_session_by_token(session_token)
        if not session:
            return jsonify({'error': 'Invalid session token'}), 404

        # Start background proctoring
        if start_background_proctoring(session_token):
            logger.info(f"Started background proctoring for session {session_token}")
            return jsonify({
                'success': True,
                'message': 'Background proctoring started',
                'session_id': session.id
            })
        else:
            return jsonify({'error': 'Failed to start background proctoring'}), 500

    except Exception as e:
        logger.error(f"Error starting session: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/session/stop', methods=['POST'])
def stop_session():
    """Stop a background proctoring session"""
    try:
        data = request.get_json()
        session_token = data.get('session_token')

        if not session_token:
            return jsonify({'error': 'session_token is required'}), 400

        # Stop background proctoring
        if stop_background_proctoring(session_token):
            logger.info(f"Stopped background proctoring for session {session_token}")
            return jsonify({
                'success': True,
                'message': 'Background proctoring stopped'
            })
        else:
            return jsonify({'error': 'Session not found or already stopped'}), 404

    except Exception as e:
        logger.error(f"Error stopping session: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/session/event', methods=['POST'])
def log_session_event():
    """Log a browser event for a proctoring session"""
    try:
        data = request.get_json()
        session_token = data.get('session_token')
        event_type = data.get('event_type')
        event_data = data.get('event_data', {})

        if not session_token or not event_type:
            return jsonify({'error': 'session_token and event_type are required'}), 400

        # Log the browser event
        if log_browser_event(session_token, event_type, event_data):
            logger.info(f"Logged event {event_type} for session {session_token}")
            return jsonify({
                'success': True,
                'message': 'Event logged successfully'
            })
        else:
            return jsonify({'error': 'Session not found'}), 404

    except Exception as e:
        logger.error(f"Error logging event: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/session/status', methods=['GET'])
def get_session_status():
    """Get status of active proctoring sessions"""
    try:
        active_sessions = proctor_manager.get_active_sessions()
        return jsonify({
            'active_sessions': active_sessions,
            'total_active': len(active_sessions)
        })

    except Exception as e:
        logger.error(f"Error getting session status: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/test-data', methods=['POST'])
def update_test_data():
    global test_data
    try:
        data = request.json
        logger.info(f"POST /api/test-data - Received: {data}")
        test_data.update(data) # type: ignore
        return jsonify({"status": "success"})
    except Exception as e:
        logger.error(f"Error in update_test_data: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/api/test-complete', methods=['POST'])
def complete_test():
    global test_data
    try:
        data = request.json
        logger.info(f"POST /api/test-complete - Received: {data}")
        test_data.update(data) # type: ignore
        test_data["test_completed"] = True
        return jsonify({"status": "success"})
    except Exception as e:
        logger.error(f"Error in complete_test: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/api/ping', methods=['GET'])
def ping():
    """Simple endpoint to test if the server is running"""
    logger.info("Ping received")
    return jsonify({"status": "ok", "message": "API server is running"})

def start_api_server(port=5000):
    """Start the Flask API server in a separate thread with better error handling"""
    def run_app():
        try:
            logger.info(f"Starting API server on port {port}")
            app.run(host='0.0.0.0', port=port, debug=False, threaded=True, use_reloader=False)
        except OSError as e:
            if "Address already in use" in str(e):
                logger.error(f"Port {port} is already in use")
                raise
            else:
                logger.error(f"OS error starting API server: {e}")
                raise
        except Exception as e:
            logger.error(f"Error starting API server: {e}")
            raise

    # Test if port is available before starting
    import socket
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    try:
        sock.bind(('localhost', port))
        sock.close()
    except OSError:
        logger.error(f"Port {port} is not available")
        raise OSError(f"Port {port} is already in use")

    thread = threading.Thread(target=run_app)
    thread.daemon = True  # Make thread exit when main thread exits
    thread.start()

    # Wait a moment to ensure server is up
    time.sleep(1)

    # Log a test message
    logger.info(f"API server started on port {port}")
    logger.info(f"Initial test_data: {test_data}")

    return port

if __name__ == "__main__":
    port = start_api_server()
    logger.info(f"API server running on port {port}. Press Ctrl+C to exit.")
    
    # Keep the main thread alive
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("Server shutting down...")


