"""
Test Complete Proctoring Workflow
Comprehensive test of the proctoring system integration
"""
import time
import requests
from database_models import (
    get_db_session, create_proctor_session, log_monitoring_event,
    ProctorSession, MonitoringEvent, User, Test, TestAssignment
)
from automated_flagging_system import start_flagging_system, stop_flagging_system

def test_database_functions():
    """Test database proctoring functions"""
    print("🔍 Testing Database Functions...")
    
    db = get_db_session()
    try:
        # Get a test and student for testing
        test = db.query(Test).first()
        student = db.query(User).filter(User.role == 'student').first()
        
        if not test or not student:
            print("❌ No test or student found in database")
            return False
        
        print(f"✅ Found test: {test.title}")
        print(f"✅ Found student: {student.name}")
        
        # Test proctoring session creation
        session = create_proctor_session(test.id, student.id)
        print(f"✅ Created proctoring session: {session.id}")
        
        # Test violation logging
        log_monitoring_event(
            session_id=session.id,
            event_type='test_violation',
            event_data={'test': 'data'},
            severity='high'
        )
        print("✅ Logged test violation")
        
        # Verify violation was logged
        violations = db.query(MonitoringEvent).filter(
            MonitoringEvent.session_id == session.id
        ).all()
        print(f"✅ Found {len(violations)} violations for session")
        
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False
    finally:
        db.close()

def test_flagging_system():
    """Test automated flagging system"""
    print("\n🚨 Testing Automated Flagging System...")
    
    try:
        # Start flagging system
        start_flagging_system()
        print("✅ Flagging system started")
        
        # Wait a moment
        time.sleep(2)
        
        # Stop flagging system
        stop_flagging_system()
        print("✅ Flagging system stopped")
        
        return True
        
    except Exception as e:
        print(f"❌ Flagging system test failed: {e}")
        return False

def test_streamlit_interfaces():
    """Test Streamlit interfaces are running"""
    print("\n🌐 Testing Streamlit Interfaces...")
    
    interfaces = [
        ("Student Portal", "http://localhost:8503"),
        ("Admin Dashboard", "http://localhost:8501"),
        ("Proctoring Monitor", "http://localhost:8502")
    ]
    
    results = []
    
    for name, url in interfaces:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"✅ {name} is running at {url}")
                results.append(True)
            else:
                print(f"⚠️ {name} returned status {response.status_code}")
                results.append(False)
        except requests.exceptions.RequestException as e:
            print(f"❌ {name} is not accessible: {e}")
            results.append(False)
    
    return all(results)

def test_proctoring_session_workflow():
    """Test complete proctoring session workflow"""
    print("\n🎯 Testing Complete Proctoring Workflow...")
    
    db = get_db_session()
    try:
        # Get test data
        test = db.query(Test).first()
        student = db.query(User).filter(User.role == 'student').first()
        
        if not test or not student:
            print("❌ Missing test data")
            return False
        
        # 1. Create proctoring session
        session = create_proctor_session(test.id, student.id)
        print(f"✅ Step 1: Created session {session.id}")
        
        # 2. Start session
        session_obj = db.query(ProctorSession).filter(
            ProctorSession.id == session.id
        ).first()
        session_obj.start_session()
        db.commit()
        print("✅ Step 2: Started session")
        
        # 3. Log various violations
        violations = [
            ('tab_switch', 'high'),
            ('face_lost', 'high'),
            ('multiple_faces', 'critical'),
            ('fullscreen_exit', 'medium')
        ]
        
        for violation_type, severity in violations:
            log_monitoring_event(
                session_id=session.id,
                event_type=violation_type,
                event_data={'test': 'workflow'},
                severity=severity
            )
            print(f"✅ Step 3: Logged {violation_type} violation")
        
        # 4. Check violations were logged
        logged_violations = db.query(MonitoringEvent).filter(
            MonitoringEvent.session_id == session.id
        ).count()
        print(f"✅ Step 4: Verified {logged_violations} violations logged")
        
        # 5. End session
        session_obj.complete_session()
        db.commit()
        print("✅ Step 5: Ended session")
        
        return True
        
    except Exception as e:
        print(f"❌ Workflow test failed: {e}")
        return False
    finally:
        db.close()

def test_violation_detection():
    """Test violation detection and flagging"""
    print("\n⚠️ Testing Violation Detection...")
    
    db = get_db_session()
    try:
        # Create a session with many violations to trigger flagging
        test = db.query(Test).first()
        student = db.query(User).filter(User.role == 'student').first()
        
        session = create_proctor_session(test.id, student.id)
        
        # Log multiple high-severity violations
        for i in range(5):
            log_monitoring_event(
                session_id=session.id,
                event_type='tab_switch',
                event_data={'violation_number': i+1},
                severity='high'
            )
        
        # Log a critical violation
        log_monitoring_event(
            session_id=session.id,
            event_type='multiple_faces',
            event_data={'faces_detected': 3},
            severity='critical'
        )
        
        print("✅ Created session with multiple violations")
        
        # Check violation counts
        violations = db.query(MonitoringEvent).filter(
            MonitoringEvent.session_id == session.id
        ).all()
        
        high_count = len([v for v in violations if v.severity == 'high'])
        critical_count = len([v for v in violations if v.severity == 'critical'])
        
        print(f"✅ Session has {high_count} high and {critical_count} critical violations")
        
        # This session should be flagged by the automated system
        if critical_count >= 1 or high_count >= 3:
            print("✅ Session meets flagging criteria")
            return True
        else:
            print("⚠️ Session does not meet flagging criteria")
            return False
            
    except Exception as e:
        print(f"❌ Violation detection test failed: {e}")
        return False
    finally:
        db.close()

def run_comprehensive_test():
    """Run all proctoring tests"""
    print("🚀 Starting Comprehensive Proctoring System Test")
    print("=" * 60)
    
    tests = [
        ("Database Functions", test_database_functions),
        ("Flagging System", test_flagging_system),
        ("Streamlit Interfaces", test_streamlit_interfaces),
        ("Proctoring Workflow", test_proctoring_session_workflow),
        ("Violation Detection", test_violation_detection)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} Test...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"TOTAL: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Proctoring system is ready!")
    else:
        print("⚠️ Some tests failed. Please review the issues above.")
    
    return passed == total

if __name__ == "__main__":
    success = run_comprehensive_test()
    
    if success:
        print("\n🎯 PROCTORING SYSTEM IMPLEMENTATION COMPLETE!")
        print("\n📋 Next Steps:")
        print("1. Start student portal: python -m streamlit run student_test_interface.py --server.port 8503")
        print("2. Start admin dashboard: python -m streamlit run admin_dashboard.py --server.port 8501")
        print("3. Start flagging system: python automated_flagging_system.py")
        print("4. Test with student login: <EMAIL> / student123")
        print("5. Monitor violations in admin dashboard")
    else:
        print("\n❌ Please fix the failing tests before deployment.")
