#!/usr/bin/env python3
"""
Database Management Script for AI Examiner Login System
Provides utilities for managing the SQLite database
"""

import sys
import hashlib
from datetime import datetime
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

DATABASE_URL = "sqlite:///users.db"
engine = create_engine(DATABASE_URL, echo=False)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def hash_password(password):
    """Hash password using SHA-256"""
    return hashlib.sha256(password.encode()).hexdigest()

def init_database():
    """Initialize database and create tables"""
    Base.metadata.create_all(bind=engine)
    print("✅ Database initialized successfully")

def get_db_session():
    """Get database session"""
    return SessionLocal()

def create_user(email, password, name, role='student'):
    """Create a new user"""
    db = get_db_session()
    try:
        # Check if user already exists
        existing_user = db.query(User).filter(User.email == email).first()
        if existing_user:
            print(f"❌ User with email {email} already exists")
            return False
        
        # Create new user
        new_user = User(
            email=email,
            password_hash=hash_password(password),
            name=name,
            role=role
        )
        
        db.add(new_user)
        db.commit()
        print(f"✅ User {email} created successfully")
        return True
        
    except Exception as e:
        db.rollback()
        print(f"❌ Error creating user: {e}")
        return False
    finally:
        db.close()

def list_users():
    """List all users"""
    db = get_db_session()
    try:
        users = db.query(User).filter(User.is_active == True).all()
        if users:
            print("\n📋 Active Users:")
            print("-" * 80)
            print(f"{'ID':<5} {'Email':<30} {'Name':<20} {'Role':<10} {'Created':<20}")
            print("-" * 80)
            for user in users:
                created = user.created_at.strftime('%Y-%m-%d %H:%M') if user.created_at else 'N/A' # type: ignore
                print(f"{user.id:<5} {user.email:<30} {user.name:<20} {user.role:<10} {created:<20}")
        else:
            print("📭 No active users found")
    except Exception as e:
        print(f"❌ Error listing users: {e}")
    finally:
        db.close()

def delete_user(email):
    """Soft delete a user (mark as inactive)"""
    db = get_db_session()
    try:
        user = db.query(User).filter(User.email == email).first()
        if user:
            user.is_active = False # type: ignore
            db.commit()
            print(f"✅ User {email} deactivated successfully")
            return True
        else:
            print(f"❌ User {email} not found")
            return False
    except Exception as e:
        db.rollback()
        print(f"❌ Error deactivating user: {e}")
        return False
    finally:
        db.close()

def reset_password(email, new_password):
    """Reset user password"""
    db = get_db_session()
    try:
        user = db.query(User).filter(User.email == email, User.is_active == True).first()
        if user:
            user.password_hash = hash_password(new_password) # type: ignore
            db.commit()
            print(f"✅ Password reset for {email}")
            return True
        else:
            print(f"❌ Active user {email} not found")
            return False
    except Exception as e:
        db.rollback()
        print(f"❌ Error resetting password: {e}")
        return False
    finally:
        db.close()

def create_default_users():
    """Create default admin and student users"""
    print("🔧 Creating default users...")
    
    # Create admin user
    admin_created = create_user(
        email="<EMAIL>",
        password="admin123",
        name="Administrator",
        role="admin"
    )
    
    # Create student user
    student_created = create_user(
        email="<EMAIL>",
        password="student123",
        name="Test Student",
        role="student"
    )
    
    if admin_created or student_created:
        print("\n📋 Default Credentials:")
        if admin_created:
            print("👤 Admin: <EMAIL> / admin123")
        if student_created:
            print("👤 Student: <EMAIL> / student123")

def show_help():
    """Show help information"""
    print("""
🔧 Database Manager for AI Examiner Login System

Available commands:
    init        - Initialize database and create tables
    create      - Create a new user (interactive)
    list        - List all active users
    delete      - Deactivate a user
    reset       - Reset user password
    defaults    - Create default admin and student users
    help        - Show this help message

Usage examples:
    python db_manager.py init
    python db_manager.py list
    python db_manager.py create
    python db_manager.<NAME_EMAIL>
    python db_manager.<NAME_EMAIL> newpassword
    """)

def interactive_create_user():
    """Interactive user creation"""
    print("\n👤 Create New User")
    print("-" * 30)
    
    email = input("Email: ").strip()
    if not email:
        print("❌ Email is required")
        return
    
    name = input("Full Name: ").strip()
    if not name:
        print("❌ Name is required")
        return
    
    password = input("Password: ").strip()
    if not password:
        print("❌ Password is required")
        return
    
    role = input("Role (admin/student) [student]: ").strip().lower()
    if role not in ['admin', 'student']:
        role = 'student'
    
    create_user(email, password, name, role)

def main():
    """Main function"""
    if len(sys.argv) < 2:
        show_help()
        return
    
    command = sys.argv[1].lower()
    
    if command == 'init':
        init_database()
    elif command == 'create':
        interactive_create_user()
    elif command == 'list':
        list_users()
    elif command == 'delete':
        if len(sys.argv) < 3:
            print("❌ Usage: python db_manager.py delete <email>")
        else:
            delete_user(sys.argv[2])
    elif command == 'reset':
        if len(sys.argv) < 4:
            print("❌ Usage: python db_manager.py reset <email> <new_password>")
        else:
            reset_password(sys.argv[2], sys.argv[3])
    elif command == 'defaults':
        init_database()
        create_default_users()
    elif command == 'help':
        show_help()
    else:
        print(f"❌ Unknown command: {command}")
        show_help()

if __name__ == "__main__":
    main()
