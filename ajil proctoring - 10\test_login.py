#!/usr/bin/env python3
"""
Test script to verify database-based login functionality
"""

import os
from main_app import (
    init_database, create_default_users, authenticate_user,
    register_user, get_all_users, hash_password
)

def test_database_system():
    """Test the database-based login system functionality"""
    print("🧪 Testing Database Login System...")
    print("=" * 60)

    # Test 1: Initialize database
    print("1. Testing database initialization...")
    try:
        init_database()
        print("   ✅ Database initialized successfully")
    except Exception as e:
        print(f"   ❌ Database initialization failed: {e}")
        return False

    # Test 2: Create default users
    print("\n2. Testing default user creation...")
    try:
        create_default_users()
        print("   ✅ Default users created/verified")
    except Exception as e:
        print(f"   ❌ Default user creation failed: {e}")
        return False

    # Test 3: Load users from database
    print("\n3. Testing user loading from database...")
    users = get_all_users()
    if users:
        print(f"   ✅ Successfully loaded {len(users)} users from database")
        for user in users:
            print(f"   - {user['email']}: {user['name']} ({user['role']})")
    else:
        print("   ❌ No users found in database")
        return False
    
    # Test 4: Test password hashing
    print("\n4. Testing password hashing...")
    test_password = "admin123"
    hashed = hash_password(test_password)
    expected_hash = "240be518fabd2724ddb6f04eeb1da5967448d7e831c08c8fa822809f74c720a9"
    if hashed == expected_hash:
        print("   ✅ Password hashing works correctly")
    else:
        print(f"   ❌ Password hashing failed. Got: {hashed}")
        return False
    
    # Test 5: Test authentication with valid credentials
    print("\n5. Testing valid authentication...")
    test_cases = [
        ("<EMAIL>", "admin123", "Administrator"),
        ("<EMAIL>", "student123", "Test Student")
    ]

    for email, password, expected_name in test_cases:
        user = authenticate_user(email, password)
        if user and user['name'] == expected_name:
            print(f"   ✅ {email} authenticated successfully")
        else:
            print(f"   ❌ {email} authentication failed")
            return False
    
    # Test 6: Test authentication with invalid credentials
    print("\n6. Testing invalid authentication...")
    invalid_cases = [
        ("<EMAIL>", "wrongpassword"),
        ("<EMAIL>", "anypassword"),
        ("<EMAIL>", "wrongpass")
    ]

    for email, password in invalid_cases:
        user = authenticate_user(email, password)
        if user is None:
            print(f"   ✅ {email} correctly rejected")
        else:
            print(f"   ❌ {email} incorrectly authenticated")
            return False

    # Test 7: Test user registration
    print("\n7. Testing user registration...")
    test_email = "<EMAIL>"
    success, message = register_user(test_email, "testpass123", "Test User")
    if success:
        print(f"   ✅ User registration successful: {message}")

        # Test authentication with new user
        user = authenticate_user(test_email, "testpass123")
        if user and user['name'] == "Test User":
            print(f"   ✅ New user authentication successful")
        else:
            print(f"   ❌ New user authentication failed")
            return False
    else:
        print(f"   ❌ User registration failed: {message}")
        return False

    # Test 8: Test database file creation
    print("\n8. Testing database file...")
    if os.path.exists("users.db"):
        print("   ✅ Database file created successfully")
    else:
        print("   ❌ Database file not found")
        return False

    print("\n" + "=" * 60)
    print("🎉 All database tests passed! Login system is working correctly.")
    print("\nDatabase Features:")
    print("✅ SQLite database with SQLAlchemy ORM")
    print("✅ Secure password hashing")
    print("✅ User registration and authentication")
    print("✅ Role-based access control")
    print("✅ Session management")
    print("\nDefault credentials:")
    print("📧 Admin: <EMAIL> / admin123")
    print("📧 Student: <EMAIL> / student123")
    return True

if __name__ == "__main__":
    test_database_system()
