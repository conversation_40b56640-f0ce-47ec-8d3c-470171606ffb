import{by as q,bz as J,r as c,bA as Q,ap as Y,bB as ee,bC as l,j as E,aE as te,bu as re,bD as ne,bv as oe,bc as ie,bE as ae,bw as ue,bF as le,br as ce,b8 as P,b7 as x,ba as se}from"./index.DKN5MVff.js";import{a as fe}from"./useBasicWidgetState.DB3vMS9V.js";import"./FormClearHelper.DF4gFAOO.js";var pe={secondary:"secondary"},N={default:"default"},de={default:"default"},k=Object.freeze({radio:"radio",checkbox:"checkbox"}),z=q("div",function(e){var t=e.$shape,r=e.$length,n=e.$theme,o=r===1?void 0:t!==N.default?"-".concat(n.sizing.scale100):"-0.5px";return{display:"flex",marginLeft:o,marginRight:o}});z.displayName="StyledRoot";z.displayName="StyledRoot";function M(e){"@babel/helpers - typeof";return M=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},M(e)}function $(){return $=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},$.apply(this,arguments)}function X(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,n)}return r}function ye(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?X(Object(r),!0).forEach(function(n){V(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):X(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function be(e,t){return ge(e)||Se(e,t)||me(e,t)||he()}function he(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function me(e,t){if(e){if(typeof e=="string")return F(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return F(e,t)}}function F(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Se(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n=[],o=!0,i=!1,a,d;try{for(r=r.call(e);!(o=(a=r.next()).done)&&(n.push(a.value),!(t&&n.length===t));o=!0);}catch(s){i=!0,d=s}finally{try{!o&&r.return!=null&&r.return()}finally{if(i)throw d}}return n}}function ge(e){if(Array.isArray(e))return e}function ve(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Oe(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function Ee(e,t,r){return t&&Oe(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function _e(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&G(e,t)}function G(e,t){return G=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,o){return n.__proto__=o,n},G(e,t)}function we(e){var t=Re();return function(){var n=B(e),o;if(t){var i=B(this).constructor;o=Reflect.construct(n,arguments,i)}else o=n.apply(this,arguments);return Le(this,o)}}function Le(e,t){if(t&&(M(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return K(e)}function K(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Re(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function B(e){return B=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},B(e)}function V(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Ce(e,t){return!Array.isArray(e)&&typeof e!="number"?!1:Array.isArray(e)?e.includes(t):e===t}var H=function(e){_e(r,e);var t=we(r);function r(){var n;ve(this,r);for(var o=arguments.length,i=new Array(o),a=0;a<o;a++)i[a]=arguments[a];return n=t.call.apply(t,[this].concat(i)),V(K(n),"childRefs",{}),n}return Ee(r,[{key:"render",value:function(){var o=this,i=this.props,a=i.overrides,d=a===void 0?{}:a,s=i.mode,y=s===void 0?k.checkbox:s,g=i.children,b=i.selected,_=i.disabled,f=i.onClick,h=i.kind,v=i.shape,m=i.size,A=J(d.Root,z),w=be(A,2),I=w[0],R=w[1],L=this.props["aria-label"]||this.props.ariaLabel,p=y===k.radio,U=c.Children.count(g);return c.createElement(Q.Consumer,null,function(Z){return c.createElement(I,$({"aria-label":L||Z.buttongroup.ariaLabel,"data-baseweb":"button-group",role:p?"radiogroup":"group",$shape:v,$length:g.length},R),c.Children.map(g,function(S,C){if(!c.isValidElement(S))return null;var T=S.props.isSelected?S.props.isSelected:Ce(b,C);return p&&(o.childRefs[C]=c.createRef()),c.cloneElement(S,{disabled:_||S.props.disabled,isSelected:T,ref:p?o.childRefs[C]:void 0,tabIndex:!p||T||p&&(!b||b===-1)&&C===0?0:-1,onKeyDown:function(u){if(p){var O=Number(b)?Number(b):0;if(u.key==="ArrowUp"||u.key==="ArrowLeft"){u.preventDefault&&u.preventDefault();var D=O-1<0?U-1:O-1;f&&f(u,D),o.childRefs[D].current&&o.childRefs[D].current.focus()}if(u.key==="ArrowDown"||u.key==="ArrowRight"){u.preventDefault&&u.preventDefault();var j=O+1>U-1?0:O+1;f&&f(u,j),o.childRefs[j].current&&o.childRefs[j].current.focus()}}},kind:h,onClick:function(u){_||(S.props.onClick&&S.props.onClick(u),f&&f(u,C))},shape:v,size:m,overrides:ye({BaseButton:{style:function(u){var O=u.$theme;return g.length===1?{}:v!==N.default?{marginLeft:O.sizing.scale100,marginRight:O.sizing.scale100}:{marginLeft:"0.5px",marginRight:"0.5px"}},props:{"aria-checked":T,role:p?"radio":"checkbox"}}},S.props.overrides)})}))})}}]),r}(c.Component);V(H,"defaultProps",{disabled:!1,onClick:function(){},shape:N.default,size:de.default,kind:pe.secondary});function Pe(e,t){return t.includes(e)?t.filter(r=>r!==e):[...t,e]}function ke(e,t,r){return e==l.ClickMode.MULTI_SELECT?Pe(t,r??[]):r?.includes(t)?[]:[t]}function Be(e){return e.length===0?-1:e[0]}function Ae(e,t,r,n){t.setIntArrayValue(e,r.value,{fromUi:r.fromUi},n)}function Ie(e,t,r){const n=r===l.Style.PILLS?P.PILLS:r===l.Style.BORDERLESS?P.BORDERLESS_ICON:P.SEGMENTED_CONTROL,o=r===l.Style.BORDERLESS?x.XSMALL:x.MEDIUM,i=n===P.PILLS||n===P.SEGMENTED_CONTROL,a=r===l.Style.BORDERLESS?"lg":"base";return{element:E(se,{icon:t,label:e,iconSize:a,useSmallerFont:i}),kind:n,size:o}}function Te(e,t,r,n){return r.indexOf(n)>-1?!0:t!==l.ClickMode.SINGLE_SELECT||e!==l.SelectionVisualization.ALL_UP_TO_SELECTED?!1:r.length>0&&n<r[0]}function De(e,t){return e&&(t=`${t}Active`),t}function je(e,t,r){const n={flexWrap:"wrap",maxWidth:"100%",margin:"0 0"},o=r?"100%":"auto",i=r?{}:{content:"''",flex:1e4};switch(e){case l.Style.BORDERLESS:return{...n,columnGap:t.threeXS,rowGap:t.threeXS};case l.Style.PILLS:return{...n,columnGap:t.twoXS,rowGap:t.twoXS,width:o};case l.Style.SEGMENTED_CONTROL:return{...n,columnGap:t.none,rowGap:t.twoXS,"::after":i,width:o};default:return n}}function Me(e,t,r,n,o,i,a){const d=Te(r,n,o,t);let s=e.content,y=e.contentIcon;return d&&(s=e.selectedContent?e.selectedContent:s,y=e.selectedContentIcon?e.selectedContentIcon:y),c.forwardRef(function(b,_){const{element:f,kind:h,size:v}=Ie(s??"",y??void 0,i),m=De(!!(d&&!e.selectedContent&&!e.selectedContentIcon),h);return E(ce,{...b,size:v,kind:m,containerWidth:a,children:f})})}function $e(e,t){return e.getIntArrayValue(t)}function Ge(e){return e.default??null}function Ne(e){return e.value??null}function ze(e){const{disabled:t,element:r,fragmentId:n,widgetMgr:o,widthConfig:i}=e,{clickMode:a,options:d,selectionVisualization:s,style:y,label:g,labelVisibility:b,help:_}=r,f=Y(),[h,v]=fe({getStateFromWidgetMgr:$e,getDefaultStateFromProto:Ge,getCurrStateFromProto:Ne,updateWidgetMgrState:Ae,element:r,widgetMgr:o,fragmentId:n}),m=ee(i),A=(R,L)=>{const p=ke(a,L,h);v({value:p,fromUi:!0})};let w;a===l.ClickMode.SINGLE_SELECT?w=k.radio:a===l.ClickMode.MULTI_SELECT&&(w=k.checkbox);const I=c.useMemo(()=>d.map((R,L)=>{const p=Me(R,L,s,a,h,y,m);return E(p,{},`${R.content}-${L}`)}),[a,d,s,y,h,m]);return te(le,{className:"stButtonGroup","data-testid":"stButtonGroup",containerWidth:m,children:[E(ue,{label:g,disabled:t,labelVisibility:re(b?.value??ae.LabelVisibilityOptions.COLLAPSED),children:_&&E(ne,{children:E(oe,{content:_,placement:ie.TOP})})}),E(H,{disabled:t,mode:w,onClick:A,selected:a===l.ClickMode.MULTI_SELECT?h:Be(h),overrides:{Root:{style:c.useCallback(()=>je(y,f.spacing,m),[y,f.spacing,m])}},children:I})]})}const xe=c.memo(ze);export{xe as default};
