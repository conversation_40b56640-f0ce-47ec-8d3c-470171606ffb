"""
Simplified Database Models for Enhanced Proctoring System
"""
import json
import uuid
from datetime import datetime, timezone, timedelta
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, Float, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from db_manager import engine, SessionLocal
Base = declarative_base()

def get_db_session():
    """Get database session"""
    return SessionLocal()

# Models
class User(Base):
    __tablename__ = 'users'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(255), nullable=False)
    email = Column(String(255), unique=True, nullable=False)
    password_hash = Column(String(255), nullable=False)
    role = Column(String(50), default='student')  # admin, student
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))

class Test(Base):
    __tablename__ = 'tests'
    
    id = Column(Integer, primary_key=True)
    title = Column(String(255), nullable=False)
    duration_minutes = Column(Integer, nullable=False)
    questions_data = Column(Text)  # JSON string
    created_by = Column(String(255), nullable=False)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    is_active = Column(Boolean, default=True)
    
    def get_questions(self):
        """Parse questions JSON"""
        try:
            data = json.loads(self.questions_data) if self.questions_data else {}
            return data.get('questions', [])
        except:
            return []

class TestSession(Base):
    __tablename__ = 'test_sessions'
    
    id = Column(Integer, primary_key=True)
    test_id = Column(Integer, ForeignKey('tests.id'), nullable=False)
    session_name = Column(String(255), nullable=False)
    max_participants = Column(Integer, default=50)
    session_duration_hours = Column(Integer, default=24)  # How long session stays active
    created_by = Column(String(255), nullable=False)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    expires_at = Column(DateTime, nullable=False)
    is_active = Column(Boolean, default=True)
    participants_joined = Column(Integer, default=0)

class StudentSubmission(Base):
    __tablename__ = 'student_submissions'
    
    id = Column(Integer, primary_key=True)
    session_id = Column(Integer, ForeignKey('test_sessions.id'), nullable=False)
    student_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    test_id = Column(Integer, ForeignKey('tests.id'), nullable=False)
    answers_data = Column(Text)  # JSON string of answers
    started_at = Column(DateTime, nullable=False)
    submitted_at = Column(DateTime)
    auto_score = Column(Float, default=0.0)
    manual_score = Column(Float)
    max_score = Column(Float, default=0.0)
    percentage = Column(Float, default=0.0)
    status = Column(String(50), default='in_progress')  # in_progress, submitted, graded
    video_file = Column(String(500))  # Path to recorded video
    audio_file = Column(String(500))  # Path to recorded audio
    admin_remarks = Column(Text)
    graded_by = Column(String(255))
    graded_at = Column(DateTime)
    
    def get_answers(self):
        """Parse answers JSON"""
        try:
            return json.loads(self.answers_data) if self.answers_data else {}
        except:
            return {}
    
    def set_answers(self, answers_dict):
        """Set answers as JSON"""
        self.answers_data = json.dumps(answers_dict)


class ProctoringEvent(Base):
    __tablename__ = 'proctoring_events'

    id = Column(Integer, primary_key=True)
    submission_id = Column(Integer, ForeignKey('student_submissions.id'), nullable=False)
    event_type = Column(String(100), nullable=False)  # tab_switch, face_lost, multiple_faces, etc.
    event_details = Column(Text)  # JSON with additional details
    severity = Column(String(20), default='medium')  # low, medium, high, critical
    timestamp = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    screenshot_path = Column(String(500))  # Path to screenshot if captured

    def get_details(self):
        """Parse event details JSON"""
        try:
            return json.loads(self.event_details) if self.event_details else {}
        except:
            return {}

    def set_details(self, details_dict):
        """Set event details as JSON"""
        self.event_details = json.dumps(details_dict)

# Utility Functions
def create_test(title, duration_minutes, questions_data, created_by):
    """Create a new test"""
    db = get_db_session()
    try:
        test = Test(
            title=title,
            duration_minutes=duration_minutes,
            questions_data=questions_data,
            created_by=created_by
        )
        db.add(test)
        db.commit()
        db.refresh(test)
        return True, test.id
    except Exception as e:
        db.rollback()
        return False, str(e)
    finally:
        db.close()

def create_test_session(test_id, session_name, max_participants, session_duration_hours, created_by):
    """Create a new test session"""
    db = get_db_session()
    try:
        expires_at = datetime.now(timezone.utc) + timedelta(hours=session_duration_hours)
        
        session = TestSession(
            test_id=test_id,
            session_name=session_name,
            max_participants=max_participants,
            session_duration_hours=session_duration_hours,
            created_by=created_by,
            expires_at=expires_at
        )
        db.add(session)
        db.commit()
        db.refresh(session)
        return True, session.id
    except Exception as e:
        db.rollback()
        return False, str(e)
    finally:
        db.close()

def get_active_tests():
    """Get all active tests"""
    db = get_db_session()
    try:
        return db.query(Test).filter(Test.is_active == True).all()
    finally:
        db.close()

def get_active_sessions():
    """Get all active test sessions"""
    db = get_db_session()
    try:
        current_time = datetime.now(timezone.utc)
        sessions = db.query(TestSession).filter(TestSession.is_active == True).all()

        # Filter sessions that haven't expired (handle timezone issues)
        active_sessions = []
        for session in sessions:
            session_expires = session.expires_at
            if session_expires.tzinfo is None:
                session_expires = session_expires.replace(tzinfo=timezone.utc)

            if session_expires > current_time:
                active_sessions.append(session)

        return active_sessions
    finally:
        db.close()

def join_test_session(session_id, student_id, test_id):
    """Student joins a test session"""
    db = get_db_session()
    try:
        # Check if session is still active
        session = db.query(TestSession).filter(TestSession.id == session_id).first()
        if not session or not session.is_active:
            return False, "Session not found or inactive"
        
        current_time = datetime.now(timezone.utc)

        # Handle timezone-naive datetime
        session_expires = session.expires_at
        if session_expires.tzinfo is None:
            session_expires = session_expires.replace(tzinfo=timezone.utc)

        if session_expires < current_time:
            return False, "Session has expired"
        
        if session.participants_joined >= session.max_participants:
            return False, "Session is full"
        
        # Check if student already joined
        existing = db.query(StudentSubmission).filter(
            StudentSubmission.session_id == session_id,
            StudentSubmission.student_id == student_id
        ).first()
        
        if existing:
            return False, "Already joined this session"
        
        # Create submission record
        submission = StudentSubmission(
            session_id=session_id,
            student_id=student_id,
            test_id=test_id,
            started_at=current_time
        )
        
        # Update participant count
        session.participants_joined += 1
        
        db.add(submission)
        db.commit()
        db.refresh(submission)
        
        return True, submission.id
    except Exception as e:
        db.rollback()
        return False, str(e)
    finally:
        db.close()


def log_proctoring_event(submission_id, event_type, details=None, severity='medium', screenshot_path=None):
    """Log a proctoring event"""
    db = get_db_session()
    try:
        event = ProctoringEvent(
            submission_id=submission_id,
            event_type=event_type,
            event_details=json.dumps(details) if details else None,
            severity=severity,
            screenshot_path=screenshot_path
        )
        db.add(event)
        db.commit()
        return True, event.id
    except Exception as e:
        db.rollback()
        return False, str(e)
    finally:
        db.close()


def get_proctoring_events(submission_id):
    """Get all proctoring events for a submission"""
    db = get_db_session()
    try:
        events = db.query(ProctoringEvent).filter(
            ProctoringEvent.submission_id == submission_id
        ).order_by(ProctoringEvent.timestamp).all()
        return events
    finally:
        db.close()


def calculate_proctoring_score(submission_id):
    """Calculate proctoring integrity score based on events"""
    events = get_proctoring_events(submission_id)

    if not events:
        return 100  # Perfect score if no violations

    # Scoring system
    base_score = 100
    deductions = {
        'tab_switch': 10,
        'fullscreen_exit': 15,
        'face_lost': 5,
        'multiple_faces': 20,
        'phone_detected': 25,
        'looking_away': 3,
        'suspicious_movement': 8
    }

    # Calculate deductions
    total_deduction = 0
    for event in events:
        deduction = deductions.get(event.event_type, 5)

        # Severity multiplier
        if event.severity == 'critical':
            deduction *= 2
        elif event.severity == 'high':
            deduction *= 1.5
        elif event.severity == 'low':
            deduction *= 0.5

        total_deduction += deduction

    # Final score (minimum 0)
    final_score = max(0, base_score - total_deduction)
    return round(final_score, 1)


def get_cheating_analysis(submission_id):
    """Get detailed cheating analysis"""
    events = get_proctoring_events(submission_id)

    analysis = {
        'total_events': len(events),
        'tab_switches': 0,
        'fullscreen_exits': 0,
        'face_violations': 0,
        'multiple_faces': 0,
        'suspicious_activities': 0,
        'critical_violations': 0,
        'timeline': [],
        'risk_level': 'low'
    }

    for event in events:
        # Count by type
        if event.event_type == 'tab_switch':
            analysis['tab_switches'] += 1
        elif event.event_type == 'fullscreen_exit':
            analysis['fullscreen_exits'] += 1
        elif event.event_type in ['face_lost', 'face_not_visible']:
            analysis['face_violations'] += 1
        elif event.event_type == 'multiple_faces':
            analysis['multiple_faces'] += 1
        else:
            analysis['suspicious_activities'] += 1

        # Count by severity
        if event.severity == 'critical':
            analysis['critical_violations'] += 1

        # Add to timeline
        analysis['timeline'].append({
            'time': event.timestamp.strftime('%H:%M:%S'),
            'type': event.event_type,
            'severity': event.severity,
            'details': event.get_details()
        })

    # Determine risk level
    score = calculate_proctoring_score(submission_id)
    if score >= 80:
        analysis['risk_level'] = 'low'
    elif score >= 60:
        analysis['risk_level'] = 'medium'
    elif score >= 40:
        analysis['risk_level'] = 'high'
    else:
        analysis['risk_level'] = 'critical'

    return analysis

def submit_test_answers(submission_id, answers_dict):
    """Submit test answers and calculate score"""
    db = get_db_session()
    try:
        submission = db.query(StudentSubmission).filter(StudentSubmission.id == submission_id).first()
        if not submission:
            return False, "Submission not found"
        
        # Get test questions for scoring
        test = db.query(Test).filter(Test.id == submission.test_id).first()
        if not test:
            return False, "Test not found"
        
        questions = test.get_questions()
        
        # Calculate score
        total_score = 0
        max_score = 0
        
        for i, question in enumerate(questions):
            question_key = f"q_{i}"
            max_score += question.get('points', 1)
            
            if question_key in answers_dict:
                user_answer = answers_dict[question_key]['answer']
                correct_answer = question.get('correct_answer')
                
                if user_answer == correct_answer:
                    total_score += question.get('points', 1)
        
        percentage = (total_score / max_score * 100) if max_score > 0 else 0
        
        # Update submission
        submission.set_answers(answers_dict)
        submission.auto_score = total_score
        submission.max_score = max_score
        submission.percentage = percentage
        submission.submitted_at = datetime.now(timezone.utc)
        submission.status = 'submitted'
        
        db.commit()
        
        return True, {
            'score': total_score,
            'max_score': max_score,
            'percentage': percentage
        }
    except Exception as e:
        db.rollback()
        return False, str(e)
    finally:
        db.close()

def close_test_session(session_id):
    """Close a test session"""
    db = get_db_session()
    try:
        session = db.query(TestSession).filter(TestSession.id == session_id).first()
        if session:
            session.is_active = False
            
            # Mark all unsubmitted as failed
            unsubmitted = db.query(StudentSubmission).filter(
                StudentSubmission.session_id == session_id,
                StudentSubmission.status == 'in_progress'
            ).all()
            
            for submission in unsubmitted:
                submission.status = 'failed'
                submission.submitted_at = datetime.now(timezone.utc)
            
            db.commit()
            return True, f"Session closed. {len(unsubmitted)} unsubmitted marked as failed."
        return False, "Session not found"
    except Exception as e:
        db.rollback()
        return False, str(e)
    finally:
        db.close()

# Create tables
if __name__ == "__main__":
    Base.metadata.create_all(engine)
    print("✅ Simple database created successfully!")
