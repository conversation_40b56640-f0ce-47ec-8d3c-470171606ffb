"""
Demo script to assign the new demo tests to students
"""
from database_models import (
    get_db_session, get_tests_by_admin, assign_test_to_student, User, Test
)

def assign_demo_tests_to_students():
    """Assign all 3 demo tests to all students"""
    print("📤 Assigning Demo Tests to Students...")
    
    db = get_db_session()
    try:
        # Get admin and tests
        admin = db.query(User).filter(User.role == 'admin').first()
        if not admin:
            print("❌ No admin user found")
            return False
        
        tests = get_tests_by_admin(admin.email)
        if not tests:
            print("❌ No tests found for admin")
            return False
        
        # Get all active students
        students = db.query(User).filter(
            User.role == 'student',
            User.is_active == True
        ).all()
        
        if not students:
            print("❌ No active students found")
            return False
        
        print(f"✅ Found {len(tests)} tests and {len(students)} students")
        print()
        
        # Assign each test to all students
        total_assignments = 0
        
        for test in tests:
            print(f"📋 Assigning '{test.title}' to students...")
            success_count = 0
            
            for student in students:
                success, message = assign_test_to_student(
                    test_id=test.id,
                    student_id=student.id,
                    deadline_hours=72,  # 3 days to complete
                    attempts_allowed=2  # Allow 2 attempts
                )
                
                if success:
                    success_count += 1
                    print(f"   ✅ Assigned to {student.name} ({student.email})")
                else:
                    print(f"   ❌ Failed to assign to {student.name}: {message}")
            
            print(f"   📊 Successfully assigned to {success_count}/{len(students)} students")
            total_assignments += success_count
            print()
        
        print(f"🎉 Total assignments created: {total_assignments}")
        return True
        
    except Exception as e:
        print(f"❌ Error assigning tests: {e}")
        return False
    finally:
        db.close()

def show_assignment_summary():
    """Show summary of current assignments"""
    print("📊 Assignment Summary...")
    
    db = get_db_session()
    try:
        from database_models import TestAssignment
        
        # Get all assignments
        assignments = db.query(TestAssignment).all()
        
        if assignments:
            print(f"✅ Total active assignments: {len(assignments)}")
            
            # Group by test
            test_assignments = {}
            for assignment in assignments:
                test = db.query(Test).filter(Test.id == assignment.test_id).first()
                student = db.query(User).filter(User.id == assignment.student_id).first()
                
                if test:
                    if test.title not in test_assignments:
                        test_assignments[test.title] = []
                    test_assignments[test.title].append({
                        'student': student.name if student else 'Unknown',
                        'deadline': assignment.deadline,
                        'attempts_used': assignment.attempts_used,
                        'attempts_allowed': assignment.attempts_allowed
                    })
            
            print("\n📋 Assignments by Test:")
            print("-" * 50)
            
            for test_title, assignments in test_assignments.items():
                print(f"\n🎯 {test_title}")
                print(f"   Assigned to {len(assignments)} students:")
                
                for assignment in assignments:
                    status = f"{assignment['attempts_used']}/{assignment['attempts_allowed']} attempts"
                    deadline = assignment['deadline'].strftime('%Y-%m-%d %H:%M')
                    print(f"   • {assignment['student']} - {status} - Due: {deadline}")
        else:
            print("ℹ️ No assignments found")
            
    except Exception as e:
        print(f"❌ Error showing assignment summary: {e}")
    finally:
        db.close()

def main():
    """Main function"""
    print("🚀 Demo Test Assignment")
    print("=" * 60)
    
    # Option 1: Auto-assign to all students
    print("Would you like to auto-assign all demo tests to all students?")
    print("This will assign:")
    print("• Python Programming Fundamentals")
    print("• Mathematics & Logic Assessment")
    print("• General Knowledge & Critical Thinking")
    print()
    print("Each test will be assigned with:")
    print("• 72 hours deadline")
    print("• 2 attempts allowed")
    print()
    
    response = input("Proceed with auto-assignment? (y/n): ").lower().strip()
    
    if response == 'y' or response == 'yes':
        success = assign_demo_tests_to_students()
        
        if success:
            print("\n" + "=" * 60)
            show_assignment_summary()
            
            print("\n" + "=" * 60)
            print("✅ ASSIGNMENT COMPLETE!")
            print("=" * 60)
            print("🎯 Students can now access their assigned tests at:")
            print("   http://localhost:8503")
            print()
            print("📋 Admin can monitor assignments at:")
            print("   http://localhost:8501")
            print("   → Test Management → Assign Tests")
        else:
            print("❌ Assignment failed")
    else:
        print("ℹ️ Auto-assignment cancelled")
        print("💡 You can manually assign tests through the admin dashboard:")
        print("   1. Go to http://localhost:8501")
        print("   2. Test Management → Assign Tests")
        print("   3. Select test and students manually")

if __name__ == "__main__":
    main()
