"""
Simple System Launcher
Main entry point for the simplified proctoring system
"""
import streamlit as st
import hashlib
from simple_models import get_db_session, User, Base, engine

def main():
    """Main application launcher with original UI styling"""
    st.set_page_config(
        page_title="Ajil Proctoring System",
        page_icon="🎯",
        layout="wide"
    )

    # Initialize database
    initialize_system()

    # Check if role is already selected
    if st.session_state.get('selected_role') == 'admin':
        show_admin_interface()
        return
    elif st.session_state.get('selected_role') == 'student':
        show_student_interface()
        return

    # Main interface with original styling
    st.markdown("""
    <style>
    .main-header {
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 3rem;
        border-radius: 15px;
        text-align: center;
        margin-bottom: 2rem;
    }

    .role-card {
        background: white;
        padding: 2rem;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        text-align: center;
        margin: 1rem;
        border: 2px solid transparent;
        transition: all 0.3s ease;
    }

    .role-card:hover {
        border-color: #667eea;
        transform: translateY(-2px);
    }

    .feature-card {
        background: #f8f9fa;
        padding: 1.5rem;
        border-radius: 10px;
        border-left: 4px solid #667eea;
        margin: 1rem 0;
    }
    </style>
    """, unsafe_allow_html=True)

    st.markdown("""
    <div class="main-header">
        <h1>🎯 Ajil Proctoring System</h1>
        <p style="font-size: 1.2rem;">Advanced Online Test Proctoring with Audio/Video Recording</p>
    </div>
    """, unsafe_allow_html=True)

    # Role selection with original styling
    col1, col2, col3 = st.columns([1, 2, 1])

    with col2:
        st.markdown("### 🚀 Choose Your Role")

        col_admin, col_student = st.columns(2)

        with col_admin:
            st.markdown("""
            <div class="role-card">
                <h3>🔧 Admin Dashboard</h3>
                <p>Create tests, manage sessions, grade submissions</p>
            </div>
            """, unsafe_allow_html=True)

            if st.button("Access Admin Dashboard", type="primary", use_container_width=True):
                st.session_state.selected_role = 'admin'
                st.rerun()

        with col_student:
            st.markdown("""
            <div class="role-card">
                <h3>📝 Student Portal</h3>
                <p>Join test sessions, take proctored tests</p>
            </div>
            """, unsafe_allow_html=True)

            if st.button("Access Student Portal", type="secondary", use_container_width=True):
                st.session_state.selected_role = 'student'
                st.rerun()

    # System features with original styling
    st.markdown("---")
    st.markdown("### 🎯 System Features")

    col1, col2, col3 = st.columns(3)

    with col1:
        st.markdown("""
        <div class="feature-card">
            <h4>🔧 Admin Features</h4>
            <ul>
                <li>Create tests from PDF</li>
                <li>Manage test sessions</li>
                <li>View audio/video recordings</li>
                <li>Grade submissions manually</li>
                <li>Session time limits (5min-24hrs)</li>
            </ul>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        st.markdown("""
        <div class="feature-card">
            <h4>📝 Student Features</h4>
            <ul>
                <li>Join active test sessions</li>
                <li>Take proctored tests</li>
                <li>Audio/video recording</li>
                <li>Real-time progress tracking</li>
                <li>View graded results</li>
            </ul>
        </div>
        """, unsafe_allow_html=True)

    with col3:
        st.markdown("""
        <div class="feature-card">
            <h4>🎯 System Features</h4>
            <ul>
                <li>Real-time proctoring</li>
                <li>Automatic grading</li>
                <li>Session management</li>
                <li>Result tracking</li>
                <li>PDF question generation</li>
            </ul>
        </div>
        """, unsafe_allow_html=True)

def initialize_system():
    """Initialize the system and database"""
    if 'system_initialized' not in st.session_state:
        # Create tables
        Base.metadata.create_all(engine)
        
        # Create default users if they don't exist
        db = get_db_session()
        try:
            # Check if admin exists
            admin = db.query(User).filter(User.email == "<EMAIL>").first()
            if not admin:
                admin_password = hashlib.sha256("admin123".encode()).hexdigest()
                admin = User(
                    name="Admin User",
                    email="<EMAIL>",
                    password_hash=admin_password,
                    role="admin",
                    is_active=True
                )
                db.add(admin)
            
            # Check if student exists
            student = db.query(User).filter(User.email == "<EMAIL>").first()
            if not student:
                student_password = hashlib.sha256("student123".encode()).hexdigest()
                student = User(
                    name="Test Student",
                    email="<EMAIL>",
                    password_hash=student_password,
                    role="student",
                    is_active=True
                )
                db.add(student)
            
            db.commit()
        except:
            db.rollback()
        finally:
            db.close()
        
        st.session_state.system_initialized = True

def show_admin_interface():
    """Show admin interface"""
    from simple_admin import show_simple_admin
    show_simple_admin()

def show_student_interface():
    """Show student interface"""
    from simple_student import show_simple_student
    show_simple_student()

if __name__ == "__main__":
    main()
