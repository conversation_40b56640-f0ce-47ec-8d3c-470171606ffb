"""
Create a sample test with 3 questions for proctoring demonstration
"""
import json
from database_models import create_test, get_db_session, User

def create_sample_test():
    """Create a sample test with 3 questions for proctoring"""
    
    # Sample test questions
    questions = [
        {
            "id": 1,
            "type": "multiple_choice",
            "question": "What is the primary purpose of AI proctoring in online examinations?",
            "options": [
                "To replace human proctors entirely",
                "To monitor student behavior and detect potential cheating",
                "To grade tests automatically",
                "To provide technical support during exams"
            ],
            "correct_answer": 1,
            "points": 10,
            "explanation": "AI proctoring is designed to monitor student behavior and detect potential cheating activities during online examinations."
        },
        {
            "id": 2,
            "type": "true_false",
            "question": "Students are allowed to switch tabs or applications during a proctored exam.",
            "correct_answer": False,
            "points": 10,
            "explanation": "Switching tabs or applications during a proctored exam is typically considered a violation as it may indicate cheating behavior."
        },
        {
            "id": 3,
            "type": "short_answer",
            "question": "Explain in 2-3 sentences why maintaining eye contact with the camera is important during an AI-proctored exam.",
            "correct_answer": "Maintaining eye contact with the camera helps the AI system verify the student's identity and ensures they are focused on the exam. It also helps detect if the student is looking away frequently, which could indicate they are seeking unauthorized help or using prohibited materials.",
            "points": 15,
            "explanation": "Eye contact with the camera is crucial for identity verification and focus monitoring in AI proctoring systems."
        }
    ]
    
    # Test settings
    settings = {
        "time_limit": 10,  # 10 minutes for demo
        "allow_backtrack": True,
        "shuffle_questions": False,
        "show_results_immediately": True,
        "proctoring_enabled": True,
        "camera_required": True,
        "microphone_required": True,
        "fullscreen_required": True,
        "tab_switching_detection": True,
        "face_detection": True,
        "multiple_person_detection": True,
        "phone_detection": True,
        "violation_threshold": 3
    }
    
    # Get admin user ID
    db = get_db_session()
    try:
        admin_user = db.query(User).filter(User.email == "<EMAIL>").first()
        if not admin_user:
            print("Admin user not found!")
            return False
        
        admin_id = admin_user.id
    finally:
        db.close()
    
    # Create the test
    success, test_id = create_test(
        title="AI Proctoring Demo Test",
        description="A sample test with 3 questions to demonstrate AI proctoring capabilities. This test includes multiple choice, true/false, and short answer questions.",
        duration_minutes=10,
        questions=questions,
        settings=settings,
        created_by=admin_id
    )
    
    if success:
        print(f"✅ Sample test created successfully with ID: {test_id}")
        print("\n📋 Test Details:")
        print(f"   Title: AI Proctoring Demo Test")
        print(f"   Duration: 10 minutes")
        print(f"   Questions: 3")
        print(f"   Total Points: 35")
        print(f"   Proctoring: Enabled")
        print("\n🎯 Questions included:")
        for i, q in enumerate(questions, 1):
            print(f"   {i}. {q['type'].replace('_', ' ').title()}: {q['question'][:50]}...")
        
        print(f"\n🔗 Test ID: {test_id}")
        print("You can now assign this test to students and start proctoring sessions!")
        return test_id
    else:
        print(f"❌ Failed to create test: {test_id}")
        return False

if __name__ == "__main__":
    create_sample_test()
