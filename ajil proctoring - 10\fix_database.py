"""
Fix Database Schema Issues
Adds missing columns and updates database structure
"""
import sqlite3
from database_models import engine, Base

def fix_database_schema():
    """Fix database schema issues"""
    print("🔧 Fixing database schema...")
    
    # Connect to SQLite database
    conn = sqlite3.connect('proctoring_system.db')
    cursor = conn.cursor()
    
    try:
        # Check if event_data column exists in monitoring_events table
        cursor.execute("PRAGMA table_info(monitoring_events)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'event_data' not in columns:
            print("📝 Adding missing event_data column to monitoring_events table...")
            cursor.execute("ALTER TABLE monitoring_events ADD COLUMN event_data TEXT")
            print("✅ Added event_data column")
        else:
            print("✅ event_data column already exists")
        
        # Check if severity column exists
        if 'severity' not in columns:
            print("📝 Adding missing severity column to monitoring_events table...")
            cursor.execute("ALTER TABLE monitoring_events ADD COLUMN severity VARCHAR(20) DEFAULT 'medium'")
            print("✅ Added severity column")
        else:
            print("✅ severity column already exists")
        
        # Commit changes
        conn.commit()
        print("✅ Database schema updated successfully!")
        
        # Recreate all tables to ensure consistency
        print("🔄 Ensuring all new tables exist...")
        Base.metadata.create_all(engine)
        print("✅ All tables verified/created")
        
    except Exception as e:
        print(f"❌ Error fixing database: {e}")
        conn.rollback()
    
    finally:
        conn.close()

if __name__ == "__main__":
    fix_database_schema()
