"""
Test script to verify question visibility in the student portal
"""
import streamlit as st

def test_question_styling():
    """Test the question styling to ensure visibility"""
    
    st.set_page_config(
        page_title="Question Visibility Test",
        page_icon="🎯",
        layout="wide"
    )
    
    # Apply the same CSS as the student interface
    st.markdown("""
    <style>
    .question-card {
        background: #f8f9fa;
        color: #2c3e50;
        padding: 2rem;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
        border-left: 5px solid #667eea;
    }
    
    .question-text {
        color: #2c3e50;
        font-size: 1.1rem;
        font-weight: 500;
        margin-bottom: 1rem;
        line-height: 1.6;
    }
    
    .question-number {
        color: #667eea;
        font-weight: 600;
        font-size: 1.2rem;
    }
    
    /* Streamlit component styling */
    .stRadio > div {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 10px;
        border: 1px solid #dee2e6;
    }
    
    .stRadio label {
        color: #2c3e50 !important;
        font-weight: 500;
    }
    
    .stTextArea > div > div > textarea {
        background: #f8f9fa !important;
        color: #2c3e50 !important;
        border: 2px solid #dee2e6 !important;
        border-radius: 10px !important;
    }
    
    .stMarkdown h3 {
        color: #2c3e50 !important;
    }
    
    .stMarkdown p {
        color: #2c3e50 !important;
    }
    </style>
    """, unsafe_allow_html=True)
    
    st.title("🎯 Question Visibility Test")
    st.markdown("This page tests the visibility of questions with the new styling.")
    
    # Test Question 1 - Multiple Choice
    st.markdown("""
    <div class="question-card">
        <div class="question-number">Question 1 of 3</div>
        <div class="question-text">What is the primary purpose of AI proctoring in online examinations?</div>
    </div>
    """, unsafe_allow_html=True)
    
    answer1 = st.radio(
        "Select your answer for Question 1:",
        [
            "To replace human proctors entirely",
            "To monitor student behavior and detect potential cheating",
            "To grade tests automatically",
            "To provide technical support during exams"
        ],
        key="q1"
    )
    
    st.markdown("---")
    
    # Test Question 2 - True/False
    st.markdown("""
    <div class="question-card">
        <div class="question-number">Question 2 of 3</div>
        <div class="question-text">Students are allowed to switch tabs or applications during a proctored exam.</div>
    </div>
    """, unsafe_allow_html=True)
    
    answer2 = st.radio(
        "Select your answer for Question 2:",
        ["True", "False"],
        key="q2"
    )
    
    st.markdown("---")
    
    # Test Question 3 - Short Answer
    st.markdown("""
    <div class="question-card">
        <div class="question-number">Question 3 of 3</div>
        <div class="question-text">Explain in 2-3 sentences why maintaining eye contact with the camera is important during an AI-proctored exam.</div>
    </div>
    """, unsafe_allow_html=True)
    
    answer3 = st.text_area(
        "Your answer for Question 3:",
        placeholder="Type your answer here...",
        key="q3"
    )
    
    st.markdown("---")
    
    # Results
    if st.button("🎯 Test Visibility"):
        st.success("✅ All questions should now be clearly visible!")
        st.info("📋 Styling Applied:")
        st.write("- Question cards: Light gray background (#f8f9fa)")
        st.write("- Text color: Dark blue-gray (#2c3e50)")
        st.write("- Question numbers: Blue accent (#667eea)")
        st.write("- Radio buttons: Styled with proper contrast")
        st.write("- Text areas: Light background with dark text")

if __name__ == "__main__":
    test_question_styling()
