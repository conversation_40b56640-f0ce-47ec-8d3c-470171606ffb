2025-09-20 18:19:13,241 - api_server - INFO - Starting API server on port 5000
2025-09-20 18:19:13,254 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-09-20 18:19:13,254 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-09-20 18:19:14,242 - api_server - INFO - API server started on port 5000
2025-09-20 18:19:14,242 - api_server - INFO - Initial test_data: {'tab_switches': 0, 'fullscreen_exits': 0, 'duration': 0, 'test_completed': False}
2025-09-20 18:19:22,535 - api_server - INFO - Starting API server on port 5000
2025-09-20 18:19:22,543 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-09-20 18:19:22,552 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-09-20 18:19:23,536 - api_server - INFO - API server started on port 5000
2025-09-20 18:19:23,536 - api_server - INFO - Initial test_data: {'tab_switches': 0, 'fullscreen_exits': 0, 'duration': 0, 'test_completed': False}
2025-09-20 18:21:17,944 - api_server - INFO - Starting API server on port 5000
2025-09-20 18:21:17,955 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-09-20 18:21:17,955 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-09-20 18:21:18,946 - api_server - INFO - API server started on port 5000
2025-09-20 18:21:18,946 - api_server - INFO - Initial test_data: {'tab_switches': 0, 'fullscreen_exits': 0, 'duration': 0, 'test_completed': False}
2025-09-20 18:22:26,314 - api_server - INFO - Starting API server on port 5000
2025-09-20 18:22:26,332 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-09-20 18:22:26,333 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-09-20 18:22:27,315 - api_server - INFO - API server started on port 5000
2025-09-20 18:22:27,315 - api_server - INFO - Initial test_data: {'tab_switches': 0, 'fullscreen_exits': 0, 'duration': 0, 'test_completed': False}
2025-09-20 18:24:00,113 - api_server - INFO - Starting API server on port 5000
2025-09-20 18:24:00,147 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-09-20 18:24:00,149 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-09-20 18:24:01,115 - api_server - INFO - API server started on port 5000
2025-09-20 18:24:01,117 - api_server - INFO - Initial test_data: {'tab_switches': 0, 'fullscreen_exits': 0, 'duration': 0, 'test_completed': False}
