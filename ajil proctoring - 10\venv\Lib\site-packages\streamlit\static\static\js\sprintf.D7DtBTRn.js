var b={},x;function m(){return x||(x=1,function(k){(function(){var i={not_type:/[^T]/,not_primitive:/[^v]/,number:/[diefg]/,numeric_arg:/[bcdiefguxX]/,json:/[j]/,text:/^[^\x25]+/,modulo:/^\x25{2}/,placeholder:/^\x25(?:([1-9]\d*)\$|\(([^)]+)\))?(\+)?(0|'[^$])?(-)?(\d+)?(?:\.(\d+))?([b-gijostTuvxX])/,key:/^([a-z_][a-z_\d]*)/i,key_access:/^\.([a-z_][a-z_\d]*)/i,index_access:/^\[(\d+)\]/,sign:/^[+-]/};function f(t){return v(S(t),arguments)}function w(t,o){return f.apply(null,[t].concat(o||[]))}function v(t,o){var n=1,c=t.length,e,p="",a,s,r,l,d,h,y,u;for(a=0;a<c;a++)if(typeof t[a]=="string")p+=t[a];else if(typeof t[a]=="object"){if(r=t[a],r.keys)for(e=o[n],s=0;s<r.keys.length;s++){if(e==null)throw new Error(f('[sprintf] Cannot access property "%s" of undefined value "%s"',r.keys[s],r.keys[s-1]));e=e[r.keys[s]]}else r.param_no?e=o[r.param_no]:e=o[n++];if(i.not_type.test(r.type)&&i.not_primitive.test(r.type)&&e instanceof Function&&(e=e()),i.numeric_arg.test(r.type)&&typeof e!="number"&&isNaN(e))throw new TypeError(f("[sprintf] expecting number but found %T",e));switch(i.number.test(r.type)&&(y=e>=0),r.type){case"b":e=parseInt(e,10).toString(2);break;case"c":e=String.fromCharCode(parseInt(e,10));break;case"d":case"i":e=parseInt(e,10);break;case"j":e=JSON.stringify(e,null,r.width?parseInt(r.width):0);break;case"e":e=r.precision?parseFloat(e).toExponential(r.precision):parseFloat(e).toExponential();break;case"f":e=r.precision?parseFloat(e).toFixed(r.precision):parseFloat(e);break;case"g":e=r.precision?String(Number(e.toPrecision(r.precision))):parseFloat(e);break;case"o":e=(parseInt(e,10)>>>0).toString(8);break;case"s":e=String(e),e=r.precision?e.substring(0,r.precision):e;break;case"t":e=String(!!e),e=r.precision?e.substring(0,r.precision):e;break;case"T":e=Object.prototype.toString.call(e).slice(8,-1).toLowerCase(),e=r.precision?e.substring(0,r.precision):e;break;case"u":e=parseInt(e,10)>>>0;break;case"v":e=e.valueOf(),e=r.precision?e.substring(0,r.precision):e;break;case"x":e=(parseInt(e,10)>>>0).toString(16);break;case"X":e=(parseInt(e,10)>>>0).toString(16).toUpperCase();break}i.json.test(r.type)?p+=e:(i.number.test(r.type)&&(!y||r.sign)?(u=y?"+":"-",e=e.toString().replace(i.sign,"")):u="",d=r.pad_char?r.pad_char==="0"?"0":r.pad_char.charAt(1):" ",h=r.width-(u+e).length,l=r.width&&h>0?d.repeat(h):"",p+=r.align?u+e+l:d==="0"?u+l+e:l+u+e)}return p}var g=Object.create(null);function S(t){if(g[t])return g[t];for(var o=t,n,c=[],e=0;o;){if((n=i.text.exec(o))!==null)c.push(n[0]);else if((n=i.modulo.exec(o))!==null)c.push("%");else if((n=i.placeholder.exec(o))!==null){if(n[2]){e|=1;var p=[],a=n[2],s=[];if((s=i.key.exec(a))!==null)for(p.push(s[1]);(a=a.substring(s[0].length))!=="";)if((s=i.key_access.exec(a))!==null)p.push(s[1]);else if((s=i.index_access.exec(a))!==null)p.push(s[1]);else throw new SyntaxError("[sprintf] failed to parse named argument key");else throw new SyntaxError("[sprintf] failed to parse named argument key");n[2]=p}else e|=2;if(e===3)throw new Error("[sprintf] mixing positional and named placeholders is not (yet) supported");c.push({placeholder:n[0],param_no:n[1],keys:n[2],sign:n[3],pad_char:n[4],align:n[5],width:n[6],precision:n[7],type:n[8]})}else throw new SyntaxError("[sprintf] unexpected placeholder");o=o.substring(n[0].length)}return g[t]=c}k.sprintf=f,k.vsprintf=w,typeof window<"u"&&(window.sprintf=f,window.vsprintf=w)})()}(b)),b}var _=m();export{_ as s};
