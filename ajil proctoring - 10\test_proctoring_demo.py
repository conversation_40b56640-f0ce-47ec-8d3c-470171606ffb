"""
Proctoring System Demonstration
Shows how the complete proctoring workflow works
"""
import json
from simple_models import (
    get_db_session, create_test, create_test_session, 
    join_test_session, submit_test_answers
)

def demonstrate_proctoring_workflow():
    """Demonstrate the complete proctoring workflow"""
    print("🎯 Proctoring System Demonstration")
    print("=" * 50)
    
    # Step 1: Create a test
    print("\n📝 Step 1: Admin Creates Test")
    questions = [
        {
            'question': 'What is 2 + 2?',
            'type': 'multiple_choice',
            'options': ['3', '4', '5', '6'],
            'correct_answer': 1,
            'points': 1
        },
        {
            'question': 'The sky is blue.',
            'type': 'true_false',
            'correct_answer': True,
            'points': 1
        }
    ]
    
    questions_data = json.dumps({'questions': questions})
    success, test_id = create_test(
        title="Proctoring Demo Test",
        duration_minutes=10,
        questions_data=questions_data,
        created_by="<EMAIL>"
    )
    
    if success:
        print(f"✅ Test created with ID: {test_id}")
    else:
        print(f"❌ Error creating test: {test_id}")
        return
    
    # Step 2: Create a session
    print("\n🎮 Step 2: Admin Creates Session")
    success, session_id = create_test_session(
        test_id=test_id,
        session_name="Demo Proctoring Session",
        max_participants=5,
        session_duration_hours=1,
        created_by="<EMAIL>"
    )
    
    if success:
        print(f"✅ Session created with ID: {session_id}")
    else:
        print(f"❌ Error creating session: {session_id}")
        return
    
    # Step 3: Student joins session
    print("\n👨‍🎓 Step 3: Student Joins Session")
    
    # Get student ID
    db = get_db_session()
    try:
        from simple_models import User
        student = db.query(User).filter(User.email == "<EMAIL>").first()
        if not student:
            print("❌ Student not found")
            return
        
        student_id = student.id
        print(f"Student: {student.name} (ID: {student_id})")
    finally:
        db.close()
    
    # Join session
    success, submission_id = join_test_session(session_id, student_id, test_id)
    
    if success:
        print(f"✅ Student joined session, submission ID: {submission_id}")
    else:
        print(f"❌ Error joining session: {submission_id}")
        return
    
    # Step 4: Simulate proctoring during test
    print("\n🎥 Step 4: Proctoring During Test")
    print("📹 Video recording started")
    print("🎵 Audio recording started")
    print("👁️ Face detection active")
    print("🖥️ Tab switch monitoring active")
    print("📱 Fullscreen monitoring active")
    
    # Simulate some proctoring events
    proctoring_events = [
        "✅ Face detected and centered",
        "✅ Student looking at screen",
        "⚠️ Student looked away for 2 seconds",
        "✅ Face back in frame",
        "✅ No tab switches detected",
        "✅ Fullscreen maintained"
    ]
    
    for event in proctoring_events:
        print(f"   {event}")
    
    # Step 5: Student submits test
    print("\n📤 Step 5: Student Submits Test")
    
    # Simulate student answers
    student_answers = {
        'q_0': {'answer': 1, 'text': '4'},  # Correct
        'q_1': {'answer': True, 'text': 'True'}  # Correct
    }
    
    success, result = submit_test_answers(submission_id, student_answers)
    
    if success:
        print(f"✅ Test submitted successfully!")
        print(f"📊 Score: {result['score']}/{result['max_score']} ({result['percentage']:.1f}%)")
    else:
        print(f"❌ Error submitting test: {result}")
        return
    
    # Step 6: Proctoring analysis
    print("\n🔍 Step 6: Proctoring Analysis")
    print("📹 Video recording stopped and saved")
    print("🎵 Audio recording stopped and saved")
    
    # Simulate proctoring analysis
    proctoring_analysis = {
        'total_duration': '8 minutes 45 seconds',
        'face_detection_score': 95,
        'attention_score': 88,
        'tab_switches': 0,
        'fullscreen_exits': 0,
        'suspicious_activities': 1,  # Looking away
        'overall_score': 92
    }
    
    print("📊 Proctoring Analysis Results:")
    for key, value in proctoring_analysis.items():
        print(f"   {key.replace('_', ' ').title()}: {value}")
    
    # Step 7: Admin review
    print("\n👨‍🏫 Step 7: Admin Review Available")
    print("📁 Files available for admin review:")
    print(f"   📹 Video: recordings/video_{submission_id}.webm")
    print(f"   🎵 Audio: recordings/audio_{submission_id}.wav")
    print(f"   📊 Proctoring Report: reports/proctoring_{submission_id}.pdf")
    print(f"   📋 Event Log: logs/events_{submission_id}.json")
    
    # Final summary
    print("\n🎉 Proctoring Workflow Complete!")
    print("=" * 50)
    print("✅ Test created and session activated")
    print("✅ Student joined with full proctoring")
    print("✅ Audio/video recorded throughout test")
    print("✅ All activities monitored and logged")
    print("✅ Test submitted with automatic grading")
    print("✅ Proctoring data available for admin review")
    print("✅ Complete audit trail maintained")
    
    print(f"\n🔗 Access the system at: http://localhost:8506")
    print("👨‍🏫 Admin: <EMAIL> / admin123")
    print("👨‍🎓 Student: <EMAIL> / student123")

if __name__ == "__main__":
    demonstrate_proctoring_workflow()
