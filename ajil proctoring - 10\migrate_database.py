"""
Database Migration Script
Fixes database schema issues and adds missing columns
"""
import sqlite3
import os
from datetime import datetime

def migrate_database():
    """Migrate database to fix schema issues"""
    db_path = "users.db"
    
    if not os.path.exists(db_path):
        print("Database file not found. Creating new database...")
        return
    
    print("Starting database migration...")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if created_at column exists in proctor_sessions
        cursor.execute("PRAGMA table_info(proctor_sessions)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'created_at' not in columns:
            print("Adding created_at column to proctor_sessions...")
            cursor.execute("""
                ALTER TABLE proctor_sessions 
                ADD COLUMN created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            """)
            
            # Update existing records with current timestamp
            cursor.execute("""
                UPDATE proctor_sessions 
                SET created_at = CURRENT_TIMESTAMP 
                WHERE created_at IS NULL
            """)
            
            print("✅ Added created_at column to proctor_sessions")
        else:
            print("✅ created_at column already exists in proctor_sessions")
        
        # Check if other required columns exist
        required_columns = {
            'face_detection_score': 'REAL DEFAULT 0.0',
            'attention_score': 'REAL DEFAULT 0.0',
            'total_violations': 'INTEGER DEFAULT 0'
        }
        
        for column_name, column_def in required_columns.items():
            if column_name not in columns:
                print(f"Adding {column_name} column to proctor_sessions...")
                cursor.execute(f"""
                    ALTER TABLE proctor_sessions 
                    ADD COLUMN {column_name} {column_def}
                """)
                print(f"✅ Added {column_name} column")
        
        # Check tests table
        cursor.execute("PRAGMA table_info(tests)")
        test_columns = [column[1] for column in cursor.fetchall()]
        
        test_required_columns = {
            'questions_data': 'TEXT',
            'settings': 'TEXT',
            'is_active': 'BOOLEAN DEFAULT 1'
        }
        
        for column_name, column_def in test_required_columns.items():
            if column_name not in test_columns:
                print(f"Adding {column_name} column to tests...")
                cursor.execute(f"""
                    ALTER TABLE tests 
                    ADD COLUMN {column_name} {column_def}
                """)
                print(f"✅ Added {column_name} column to tests")
        
        # Create monitoring_events table if it doesn't exist
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS monitoring_events (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id INTEGER NOT NULL,
                event_type VARCHAR(100) NOT NULL,
                severity VARCHAR(20) DEFAULT 'medium',
                description TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                metadata TEXT,
                FOREIGN KEY (session_id) REFERENCES proctor_sessions (id)
            )
        """)
        print("✅ Ensured monitoring_events table exists")
        
        # Create session_metrics table if it doesn't exist
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS session_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id INTEGER NOT NULL,
                metric_name VARCHAR(100) NOT NULL,
                metric_value REAL NOT NULL,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (session_id) REFERENCES proctor_sessions (id)
            )
        """)
        print("✅ Ensured session_metrics table exists")
        
        # Create indexes for better performance
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_proctor_sessions_test_id ON proctor_sessions(test_id)",
            "CREATE INDEX IF NOT EXISTS idx_proctor_sessions_student_id ON proctor_sessions(student_id)",
            "CREATE INDEX IF NOT EXISTS idx_proctor_sessions_status ON proctor_sessions(status)",
            "CREATE INDEX IF NOT EXISTS idx_proctor_sessions_created_at ON proctor_sessions(created_at)",
            "CREATE INDEX IF NOT EXISTS idx_monitoring_events_session_id ON monitoring_events(session_id)",
            "CREATE INDEX IF NOT EXISTS idx_monitoring_events_timestamp ON monitoring_events(timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_session_metrics_session_id ON session_metrics(session_id)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        print("✅ Created database indexes")
        
        conn.commit()
        print("🎉 Database migration completed successfully!")
        
        # Show table info
        print("\n📊 Current database schema:")
        
        tables = ['users', 'tests', 'proctor_sessions', 'monitoring_events', 'session_metrics']
        for table in tables:
            try:
                cursor.execute(f"PRAGMA table_info({table})")
                columns = cursor.fetchall()
                print(f"\n{table.upper()} table:")
                for column in columns:
                    print(f"  - {column[1]} ({column[2]})")
            except sqlite3.OperationalError:
                print(f"\n{table.upper()} table: Not found")
        
    except Exception as e:
        print(f"❌ Error during migration: {e}")
        conn.rollback()
    
    finally:
        conn.close()

if __name__ == "__main__":
    migrate_database()
