
"""
Admin Violation Dashboard
Real-time monitoring of proctoring violations
"""
import streamlit as st
import pandas as pd
from datetime import datetime, timedelta
from database_models import get_db_session, MonitoringEvent, ProctorSession, User, Test

def show_violation_dashboard():
    """Show real-time violation dashboard"""
    
    st.markdown("### 🚨 Live Violation Monitoring")
    
    # Get recent violations (last 24 hours)
    db = get_db_session()
    try:
        cutoff_time = datetime.now() - timedelta(hours=24)
        
        violations = db.query(MonitoringEvent).filter(
            MonitoringEvent.timestamp >= cutoff_time
        ).order_by(MonitoringEvent.timestamp.desc()).limit(50).all()
        
        if violations:
            # Create violation summary
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                total_violations = len(violations)
                st.metric("Total Violations (24h)", total_violations)
            
            with col2:
                high_severity = len([v for v in violations if v.severity == 'high'])
                st.metric("High Severity", high_severity)
            
            with col3:
                critical_violations = len([v for v in violations if v.severity == 'critical'])
                st.metric("Critical", critical_violations, delta=critical_violations)
            
            with col4:
                active_sessions = db.query(ProctorSession).filter(
                    ProctorSession.status == 'active'
                ).count()
                st.metric("Active Sessions", active_sessions)
            
            # Violation details table
            st.markdown("#### Recent Violations")
            
            violation_data = []
            for violation in violations:
                # Get session info
                session = db.query(ProctorSession).filter(
                    ProctorSession.id == violation.session_id
                ).first()
                
                if session:
                    # Get student info
                    student = db.query(User).filter(
                        User.id == session.student_id
                    ).first()
                    
                    # Get test info
                    test = db.query(Test).filter(
                        Test.id == session.test_id
                    ).first()
                    
                    violation_data.append({
                        'Time': violation.timestamp.strftime('%H:%M:%S'),
                        'Student': student.name if student else 'Unknown',
                        'Test': test.title if test else 'Unknown',
                        'Violation': violation.event_type.replace('_', ' ').title(),
                        'Severity': violation.severity.upper(),
                        'Session ID': session.id
                    })
            
            if violation_data:
                df = pd.DataFrame(violation_data)
                
                # Color code by severity
                def highlight_severity(row):
                    if row['Severity'] == 'CRITICAL':
                        return ['background-color: #ffebee'] * len(row)
                    elif row['Severity'] == 'HIGH':
                        return ['background-color: #fff3e0'] * len(row)
                    else:
                        return [''] * len(row)
                
                styled_df = df.style.apply(highlight_severity, axis=1)
                st.dataframe(styled_df, use_container_width=True, hide_index=True)
                
                # Flagged sessions
                st.markdown("#### 🚩 Flagged Sessions")
                
                flagged_sessions = []
                session_violations = {}
                
                for violation in violations:
                    session_id = violation.session_id
                    if session_id not in session_violations:
                        session_violations[session_id] = []
                    session_violations[session_id].append(violation)
                
                # Check flagging criteria
                for session_id, session_viols in session_violations.items():
                    high_count = len([v for v in session_viols if v.severity == 'high'])
                    critical_count = len([v for v in session_viols if v.severity == 'critical'])
                    
                    if critical_count >= 1 or high_count >= 3:
                        session = db.query(ProctorSession).filter(
                            ProctorSession.id == session_id
                        ).first()
                        
                        if session:
                            student = db.query(User).filter(
                                User.id == session.student_id
                            ).first()
                            test = db.query(Test).filter(
                                Test.id == session.test_id
                            ).first()
                            
                            flagged_sessions.append({
                                'Session ID': session_id,
                                'Student': student.name if student else 'Unknown',
                                'Test': test.title if test else 'Unknown',
                                'High Violations': high_count,
                                'Critical Violations': critical_count,
                                'Status': session.status,
                                'Action': 'Review Required'
                            })
                
                if flagged_sessions:
                    flagged_df = pd.DataFrame(flagged_sessions)
                    st.dataframe(flagged_df, use_container_width=True, hide_index=True)
                    
                    # Alert for flagged sessions
                    st.error(f"⚠️ {len(flagged_sessions)} sessions require immediate review!")
                else:
                    st.success("✅ No sessions currently flagged")
            
        else:
            st.info("No violations recorded in the last 24 hours")
            
    finally:
        db.close()

if __name__ == "__main__":
    show_violation_dashboard()
