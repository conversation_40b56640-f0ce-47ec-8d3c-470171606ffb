"""
Implement Simple Proctoring Integration
This script adds proctoring to the student test interface
"""
import os
import shutil
from datetime import datetime

def create_proctoring_javascript():
    """Create JavaScript for browser monitoring"""
    
    js_code = '''
// Proctoring JavaScript for browser monitoring
class ProctoringMonitor {
    constructor() {
        this.violations = [];
        this.isFullscreen = false;
        this.tabSwitchCount = 0;
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        // Tab visibility change detection
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.logViolation('tab_switch', {
                    timestamp: new Date().toISOString(),
                    type: 'tab_hidden'
                });
            }
        });
        
        // Fullscreen monitoring
        document.addEventListener('fullscreenchange', () => {
            this.isFullscreen = !!document.fullscreenElement;
            if (!this.isFullscreen) {
                this.logViolation('fullscreen_exit', {
                    timestamp: new Date().toISOString()
                });
            }
        });
        
        // Window focus/blur detection
        window.addEventListener('blur', () => {
            this.logViolation('window_blur', {
                timestamp: new Date().toISOString()
            });
        });
        
        // Keyboard shortcuts prevention
        document.addEventListener('keydown', (e) => {
            // Prevent common cheating shortcuts
            if (e.ctrlKey && (e.key === 't' || e.key === 'n' || e.key === 'w')) {
                e.preventDefault();
                this.logViolation('blocked_shortcut', {
                    key: e.key,
                    timestamp: new Date().toISOString()
                });
            }
        });
        
        // Right-click prevention
        document.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            this.logViolation('right_click_attempt', {
                timestamp: new Date().toISOString()
            });
        });
    }
    
    logViolation(type, data) {
        const violation = {
            type: type,
            data: data,
            timestamp: new Date().toISOString()
        };
        
        this.violations.push(violation);
        
        // Send to backend
        fetch('/api/log_violation', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(violation)
        }).catch(console.error);
        
        // Show warning to student
        this.showViolationWarning(type);
    }
    
    showViolationWarning(type) {
        const warnings = {
            'tab_switch': 'Warning: Tab switching detected!',
            'fullscreen_exit': 'Warning: Please return to fullscreen mode!',
            'window_blur': 'Warning: Please keep the test window focused!',
            'blocked_shortcut': 'Warning: Keyboard shortcuts are disabled!',
            'right_click_attempt': 'Warning: Right-click is disabled during test!'
        };
        
        const message = warnings[type] || 'Warning: Suspicious activity detected!';
        
        // Create warning popup
        const warning = document.createElement('div');
        warning.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #ff6b6b;
            color: white;
            padding: 1rem;
            border-radius: 10px;
            z-index: 10000;
            font-weight: bold;
            box-shadow: 0 4px 6px rgba(0,0,0,0.3);
        `;
        warning.textContent = message;
        
        document.body.appendChild(warning);
        
        // Remove after 5 seconds
        setTimeout(() => {
            if (warning.parentNode) {
                warning.parentNode.removeChild(warning);
            }
        }, 5000);
    }
    
    requestFullscreen() {
        if (document.documentElement.requestFullscreen) {
            document.documentElement.requestFullscreen();
        }
    }
    
    getViolationSummary() {
        return {
            total: this.violations.length,
            by_type: this.violations.reduce((acc, v) => {
                acc[v.type] = (acc[v.type] || 0) + 1;
                return acc;
            }, {}),
            violations: this.violations
        };
    }
}

// Initialize proctoring monitor
window.proctoringMonitor = new ProctoringMonitor();

// Auto-request fullscreen when test starts
window.addEventListener('load', () => {
    setTimeout(() => {
        if (window.location.pathname.includes('test') || 
            document.querySelector('[data-testid="test-interface"]')) {
            window.proctoringMonitor.requestFullscreen();
        }
    }, 1000);
});
'''
    
    # Create static directory if it doesn't exist
    os.makedirs('static', exist_ok=True)

    # Save JavaScript file
    with open('static/proctoring.js', 'w') as f:
        f.write(js_code)

    print("✅ Created proctoring JavaScript file")

def create_proctoring_css():
    """Create CSS for proctoring interface"""
    
    css_code = '''
/* Proctoring Interface Styles */
.proctoring-header {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    padding: 1rem;
    border-radius: 10px;
    text-align: center;
    margin-bottom: 1rem;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.proctoring-status {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 10px;
    border-left: 4px solid #28a745;
    margin: 1rem 0;
}

.camera-preview {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 200px;
    height: 150px;
    border: 2px solid #667eea;
    border-radius: 10px;
    background: #000;
    z-index: 1000;
}

.violation-counter {
    position: fixed;
    top: 20px;
    left: 20px;
    background: #ff6b6b;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: bold;
    z-index: 1000;
}

.proctoring-checklist {
    background: #e3f2fd;
    border: 1px solid #2196f3;
    border-radius: 10px;
    padding: 1.5rem;
    margin: 1rem 0;
}

.checklist-item {
    display: flex;
    align-items: center;
    margin: 0.5rem 0;
    padding: 0.5rem;
    border-radius: 5px;
    transition: background 0.3s ease;
}

.checklist-item.passed {
    background: #d4edda;
    color: #155724;
}

.checklist-item.failed {
    background: #f8d7da;
    color: #721c24;
}

.violation-warning {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #ff6b6b;
    color: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    z-index: 10000;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translate(-50%, -50%) rotate(0deg); }
    25% { transform: translate(-50%, -50%) rotate(-1deg); }
    75% { transform: translate(-50%, -50%) rotate(1deg); }
}

.test-locked {
    pointer-events: none;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

/* Hide browser UI elements during test */
.proctoring-active {
    overflow: hidden;
}

.proctoring-active * {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
'''
    
    # Create static directory if it doesn't exist
    os.makedirs('static', exist_ok=True)
    
    # Save CSS file
    with open('static/proctoring.css', 'w') as f:
        f.write(css_code)
    
    print("✅ Created proctoring CSS file")

def create_admin_violation_dashboard():
    """Create admin dashboard for viewing violations"""
    
    dashboard_code = '''
"""
Admin Violation Dashboard
Real-time monitoring of proctoring violations
"""
import streamlit as st
import pandas as pd
from datetime import datetime, timedelta
from database_models import get_db_session, MonitoringEvent, ProctorSession, User, Test

def show_violation_dashboard():
    """Show real-time violation dashboard"""
    
    st.markdown("### 🚨 Live Violation Monitoring")
    
    # Get recent violations (last 24 hours)
    db = get_db_session()
    try:
        cutoff_time = datetime.now() - timedelta(hours=24)
        
        violations = db.query(MonitoringEvent).filter(
            MonitoringEvent.timestamp >= cutoff_time
        ).order_by(MonitoringEvent.timestamp.desc()).limit(50).all()
        
        if violations:
            # Create violation summary
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                total_violations = len(violations)
                st.metric("Total Violations (24h)", total_violations)
            
            with col2:
                high_severity = len([v for v in violations if v.severity == 'high'])
                st.metric("High Severity", high_severity)
            
            with col3:
                critical_violations = len([v for v in violations if v.severity == 'critical'])
                st.metric("Critical", critical_violations, delta=critical_violations)
            
            with col4:
                active_sessions = db.query(ProctorSession).filter(
                    ProctorSession.status == 'active'
                ).count()
                st.metric("Active Sessions", active_sessions)
            
            # Violation details table
            st.markdown("#### Recent Violations")
            
            violation_data = []
            for violation in violations:
                # Get session info
                session = db.query(ProctorSession).filter(
                    ProctorSession.id == violation.session_id
                ).first()
                
                if session:
                    # Get student info
                    student = db.query(User).filter(
                        User.id == session.student_id
                    ).first()
                    
                    # Get test info
                    test = db.query(Test).filter(
                        Test.id == session.test_id
                    ).first()
                    
                    violation_data.append({
                        'Time': violation.timestamp.strftime('%H:%M:%S'),
                        'Student': student.name if student else 'Unknown',
                        'Test': test.title if test else 'Unknown',
                        'Violation': violation.event_type.replace('_', ' ').title(),
                        'Severity': violation.severity.upper(),
                        'Session ID': session.id
                    })
            
            if violation_data:
                df = pd.DataFrame(violation_data)
                
                # Color code by severity
                def highlight_severity(row):
                    if row['Severity'] == 'CRITICAL':
                        return ['background-color: #ffebee'] * len(row)
                    elif row['Severity'] == 'HIGH':
                        return ['background-color: #fff3e0'] * len(row)
                    else:
                        return [''] * len(row)
                
                styled_df = df.style.apply(highlight_severity, axis=1)
                st.dataframe(styled_df, use_container_width=True, hide_index=True)
                
                # Flagged sessions
                st.markdown("#### 🚩 Flagged Sessions")
                
                flagged_sessions = []
                session_violations = {}
                
                for violation in violations:
                    session_id = violation.session_id
                    if session_id not in session_violations:
                        session_violations[session_id] = []
                    session_violations[session_id].append(violation)
                
                # Check flagging criteria
                for session_id, session_viols in session_violations.items():
                    high_count = len([v for v in session_viols if v.severity == 'high'])
                    critical_count = len([v for v in session_viols if v.severity == 'critical'])
                    
                    if critical_count >= 1 or high_count >= 3:
                        session = db.query(ProctorSession).filter(
                            ProctorSession.id == session_id
                        ).first()
                        
                        if session:
                            student = db.query(User).filter(
                                User.id == session.student_id
                            ).first()
                            test = db.query(Test).filter(
                                Test.id == session.test_id
                            ).first()
                            
                            flagged_sessions.append({
                                'Session ID': session_id,
                                'Student': student.name if student else 'Unknown',
                                'Test': test.title if test else 'Unknown',
                                'High Violations': high_count,
                                'Critical Violations': critical_count,
                                'Status': session.status,
                                'Action': 'Review Required'
                            })
                
                if flagged_sessions:
                    flagged_df = pd.DataFrame(flagged_sessions)
                    st.dataframe(flagged_df, use_container_width=True, hide_index=True)
                    
                    # Alert for flagged sessions
                    st.error(f"⚠️ {len(flagged_sessions)} sessions require immediate review!")
                else:
                    st.success("✅ No sessions currently flagged")
            
        else:
            st.info("No violations recorded in the last 24 hours")
            
    finally:
        db.close()

if __name__ == "__main__":
    show_violation_dashboard()
'''
    
    with open('admin_violation_dashboard.py', 'w', encoding='utf-8') as f:
        f.write(dashboard_code)
    
    print("✅ Created admin violation dashboard")

def main():
    """Main implementation function"""
    print("🚀 Implementing Simple Proctoring System...")
    print("=" * 50)
    
    # Create necessary files
    create_proctoring_javascript()
    create_proctoring_css()
    create_admin_violation_dashboard()
    
    print("\n📋 Implementation Summary:")
    print("✅ Proctoring JavaScript created (static/proctoring.js)")
    print("✅ Proctoring CSS created (static/proctoring.css)")
    print("✅ Admin violation dashboard created")
    
    print("\n🔧 Next Steps:")
    print("1. Integrate JavaScript into student_test_interface.py")
    print("2. Add proctoring session creation to test start")
    print("3. Include violation dashboard in admin interface")
    print("4. Test the complete workflow")
    
    print("\n📊 Files Created:")
    files = [
        "static/proctoring.js - Browser monitoring",
        "static/proctoring.css - Proctoring interface styles", 
        "admin_violation_dashboard.py - Real-time violation monitoring"
    ]
    
    for file in files:
        print(f"   • {file}")
    
    print("\n🎯 Ready for integration!")

if __name__ == "__main__":
    main()
