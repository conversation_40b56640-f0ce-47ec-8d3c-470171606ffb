"""
Update Database with Proctoring Events Table
"""
from simple_models import Base, engine, get_db_session, ProctoringEvent
import json
from datetime import datetime, timezone

def update_database():
    """Update database with new proctoring events table"""
    print("🔄 Updating database with proctoring events table...")
    
    # Create all tables (including new ProctoringEvent table)
    Base.metadata.create_all(engine)
    print("✅ Database tables updated")
    
    # Add some sample proctoring events for demonstration
    add_sample_proctoring_events()
    
    print("🎉 Database update complete!")

def add_sample_proctoring_events():
    """Add sample proctoring events for existing submissions"""
    db = get_db_session()
    try:
        from simple_models import StudentSubmission, log_proctoring_event
        
        # Get existing submissions
        submissions = db.query(StudentSubmission).all()
        
        if not submissions:
            print("No submissions found to add proctoring events")
            return
        
        print(f"Adding sample proctoring events for {len(submissions)} submissions...")
        
        # Sample events for each submission
        sample_events = [
            ("session_started", {"start_time": "2024-01-01T10:00:00Z"}, "low"),
            ("face_detected", {"confidence": 0.95, "position": "center"}, "low"),
            ("tab_switch", {"count": 1, "duration": 3}, "high"),
            ("looking_away", {"duration": 4, "direction": "left"}, "medium"),
            ("face_detected", {"confidence": 0.88, "position": "center"}, "low"),
            ("fullscreen_exit", {"count": 1, "duration": 2}, "high"),
            ("multiple_faces", {"count": 2, "confidence": 0.8}, "critical"),
            ("face_lost", {"duration": 6, "reason": "moved_away"}, "high"),
            ("phone_detected", {"confidence": 0.7, "position": "right"}, "critical"),
            ("suspicious_movement", {"intensity": "high", "duration": 5}, "medium"),
            ("face_detected", {"confidence": 0.92, "position": "center"}, "low"),
            ("session_completed", {"end_time": "2024-01-01T10:30:00Z"}, "low")
        ]
        
        for submission in submissions:
            print(f"  Adding events for submission {submission.id}")
            
            # Add 5-8 random events per submission
            import random
            num_events = random.randint(5, 8)
            selected_events = random.sample(sample_events, num_events)
            
            for event_type, details, severity in selected_events:
                log_proctoring_event(
                    submission_id=submission.id,
                    event_type=event_type,
                    details=details,
                    severity=severity
                )
        
        print(f"✅ Added sample proctoring events for all submissions")
        
    except Exception as e:
        print(f"Error adding sample events: {e}")
    finally:
        db.close()

def show_database_contents():
    """Show all data in database tables"""
    print("\n📊 Database Contents:")
    print("=" * 50)
    
    db = get_db_session()
    try:
        from simple_models import User, Test, TestSession, StudentSubmission, ProctoringEvent
        
        # Users
        users = db.query(User).all()
        print(f"\n👥 Users ({len(users)}):")
        for user in users:
            print(f"  - {user.name} ({user.email}) - {user.role}")
        
        # Tests
        tests = db.query(Test).filter(Test.is_active == True).all()
        print(f"\n📝 Tests ({len(tests)}):")
        for test in tests:
            print(f"  - {test.title} ({test.duration_minutes} min) - Created: {test.created_at.strftime('%Y-%m-%d %H:%M')}")
        
        # Sessions
        sessions = db.query(TestSession).all()
        print(f"\n🎮 Sessions ({len(sessions)}):")
        for session in sessions:
            status = "Active" if session.is_active else "Inactive"
            print(f"  - {session.session_name} ({status}) - Participants: {session.participants_joined}/{session.max_participants}")
        
        # Submissions
        submissions = db.query(StudentSubmission).all()
        print(f"\n📤 Submissions ({len(submissions)}):")
        for submission in submissions:
            print(f"  - ID: {submission.id} | Score: {submission.auto_score}/{submission.max_score} | Status: {submission.status}")
        
        # Proctoring Events
        events = db.query(ProctoringEvent).all()
        print(f"\n🔍 Proctoring Events ({len(events)}):")
        
        # Group by submission
        events_by_submission = {}
        for event in events:
            if event.submission_id not in events_by_submission:
                events_by_submission[event.submission_id] = []
            events_by_submission[event.submission_id].append(event)
        
        for submission_id, submission_events in events_by_submission.items():
            print(f"  Submission {submission_id} ({len(submission_events)} events):")
            
            # Count by type and severity
            event_counts = {}
            severity_counts = {'low': 0, 'medium': 0, 'high': 0, 'critical': 0}
            
            for event in submission_events:
                event_counts[event.event_type] = event_counts.get(event.event_type, 0) + 1
                severity_counts[event.severity] = severity_counts.get(event.severity, 0) + 1
            
            print(f"    Events: {dict(event_counts)}")
            print(f"    Severity: {dict(severity_counts)}")
            
            # Calculate integrity score
            from simple_models import calculate_proctoring_score, get_cheating_analysis
            score = calculate_proctoring_score(submission_id)
            analysis = get_cheating_analysis(submission_id)
            print(f"    Integrity Score: {score}% (Risk: {analysis['risk_level']})")
            print()
        
    finally:
        db.close()

if __name__ == "__main__":
    update_database()
    show_database_contents()
