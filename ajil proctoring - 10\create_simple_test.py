"""
Create Simple Test for Login-Based System
"""
import json
from database_models import create_test

def create_simple_test():
    """Create a simple test for the login-based system"""
    print("🎯 Creating simple test for login-based system...")
    
    # Sample questions for a general knowledge test
    questions = [
        {
            'question': 'What is the capital of France?',
            'type': 'multiple_choice',
            'options': ['London', 'Berlin', 'Paris', 'Madrid'],
            'correct_answer': 2,
            'points': 1,
            'difficulty': 'Easy'
        },
        {
            'question': 'The Earth revolves around the Sun.',
            'type': 'true_false',
            'correct_answer': True,
            'points': 1,
            'difficulty': 'Easy'
        },
        {
            'question': 'What is 2 + 2?',
            'type': 'multiple_choice',
            'options': ['3', '4', '5', '6'],
            'correct_answer': 1,
            'points': 1,
            'difficulty': 'Easy'
        },
        {
            'question': 'Python is a programming language.',
            'type': 'true_false',
            'correct_answer': True,
            'points': 1,
            'difficulty': 'Easy'
        },
        {
            'question': 'Explain the importance of education in modern society.',
            'type': 'short_answer',
            'correct_answer': 'Education is crucial for personal development, economic growth, and social progress.',
            'points': 3,
            'difficulty': 'Medium'
        }
    ]
    
    # Create test
    success, message = create_test(
        title="General Knowledge Test",
        description="A simple test covering basic general knowledge questions",
        duration_minutes=15,
        questions=json.dumps({
            'questions': questions,
            'source': 'manual_creation'
        }),
        settings=json.dumps({
            'time_limit': 15,
            'difficulty': 'Easy',
            'auto_submit': True,
            'randomize': False
        }),
        created_by="<EMAIL>"
    )
    
    if success:
        print(f"✅ Test created successfully! Test ID: {message}")
        print("\n🎉 Test is now available for students!")
        print("\n📋 Student Login Instructions:")
        print("1. Go to: http://localhost:8503")
        print("2. Login with: <EMAIL> / student123")
        print("3. Select 'General Knowledge Test' from available tests")
        print("4. Complete the test and see results")
        
        return message
    else:
        print(f"❌ Error creating test: {message}")
        return None

if __name__ == "__main__":
    create_simple_test()
