"""
Recreate Database with All Tables
Creates a fresh database with all required tables and sample data
"""
import os
import hashlib
from database_models import (
    Base, engine, User, Test, ProctorSession, MonitoringEvent, SessionMetrics,
    TestAssignment, TestResult, get_db_session
)

def recreate_database():
    """Recreate the database with all tables"""
    print("🗄️ Recreating database...")
    
    # Remove existing database file
    db_file = 'proctoring_system.db'
    if os.path.exists(db_file):
        os.remove(db_file)
        print("🗑️ Removed old database file")
    
    # Create all tables
    Base.metadata.create_all(engine)
    print("✅ Created all database tables")
    
    # Create sample users
    db = get_db_session()
    try:
        # Create admin user
        admin_password = hashlib.sha256("admin123".encode()).hexdigest()
        admin = User(
            name="Admin User",
            email="<EMAIL>",
            password_hash=admin_password,
            role="admin",
            is_active=True
        )
        db.add(admin)
        
        # Create student users
        student_password = hashlib.sha256("student123".encode()).hexdigest()
        
        students = [
            User(
                name="Test Student",
                email="<EMAIL>",
                password_hash=student_password,
                role="student",
                is_active=True
            ),
            User(
                name="John Doe",
                email="<EMAIL>",
                password_hash=student_password,
                role="student",
                is_active=True
            ),
            User(
                name="Jane Smith",
                email="<EMAIL>",
                password_hash=student_password,
                role="student",
                is_active=True
            )
        ]
        
        for student in students:
            db.add(student)
        
        db.commit()
        print("✅ Created sample users")
        print("📋 User accounts created:")
        print("   Admin: <EMAIL> / admin123")
        print("   Student: <EMAIL> / student123")
        print("   Student: <EMAIL> / student123")
        print("   Student: <EMAIL> / student123")
        
    except Exception as e:
        print(f"❌ Error creating users: {e}")
        db.rollback()
    finally:
        db.close()
    
    print("🎉 Database recreated successfully!")
    print("📊 Tables created:")
    print("   - users (admin and student accounts)")
    print("   - tests (test content and questions)")
    print("   - test_assignments (assignments with deadlines)")
    print("   - test_results (student answers and scores)")
    print("   - proctor_sessions (proctoring sessions)")
    print("   - monitoring_events (proctoring events)")
    print("   - session_metrics (session metrics)")

if __name__ == "__main__":
    recreate_database()
