"""
Demo Test Assignment
Creates a test and assigns it to students to demonstrate the new system
"""
import json
from database_models import (
    create_test, assign_test_to_student, get_db_session, User, Test
)

def create_demo_assignment():
    """Create a demo test and assign it to students"""
    print("🎯 Creating demo test assignment...")
    
    # Sample questions for demo
    questions = [
        {
            'question': 'What is the capital of Japan?',
            'type': 'multiple_choice',
            'options': ['Tokyo', 'Osaka', 'Kyoto', 'Hiroshima'],
            'correct_answer': 0,
            'points': 2,
            'difficulty': 'Easy'
        },
        {
            'question': 'The Great Wall of China is visible from space.',
            'type': 'true_false',
            'correct_answer': False,
            'points': 1,
            'difficulty': 'Medium'
        },
        {
            'question': 'What is 15 × 8?',
            'type': 'multiple_choice',
            'options': ['110', '120', '130', '140'],
            'correct_answer': 1,
            'points': 2,
            'difficulty': 'Easy'
        },
        {
            'question': 'Water boils at 100 degrees Celsius at sea level.',
            'type': 'true_false',
            'correct_answer': True,
            'points': 1,
            'difficulty': 'Easy'
        },
        {
            'question': 'Explain the importance of renewable energy sources in modern society.',
            'type': 'short_answer',
            'correct_answer': 'Renewable energy sources are important for environmental sustainability, reducing carbon emissions, and ensuring long-term energy security.',
            'points': 4,
            'difficulty': 'Medium'
        }
    ]
    
    # Create test
    success, message = create_test(
        title="Demo Knowledge Assessment",
        description="A demonstration test covering general knowledge topics with time limits and automatic grading",
        duration_minutes=20,
        questions=json.dumps({
            'questions': questions,
            'source': 'demo_creation'
        }),
        settings=json.dumps({
            'time_limit': 20,
            'difficulty': 'Mixed',
            'auto_submit': True,
            'randomize': False,
            'passing_score': 70
        }),
        created_by="<EMAIL>"
    )
    
    if success:
        test_id = message
        print(f"✅ Demo test created successfully! Test ID: {test_id}")
        
        # Get all students and assign test
        db = get_db_session()
        try:
            students = db.query(User).filter(User.role == 'student', User.is_active == True).all()
            
            if students:
                print(f"📤 Assigning test to {len(students)} students...")
                
                success_count = 0
                for student in students:
                    success, token = assign_test_to_student(
                        test_id=test_id,
                        student_id=student.id,
                        deadline_hours=48,  # 48 hours to complete
                        attempts_allowed=2
                    )
                    
                    if success:
                        success_count += 1
                        print(f"   ✅ Assigned to {student.name} ({student.email})")
                        print(f"      Access Token: {token}")
                    else:
                        print(f"   ❌ Failed to assign to {student.name}: {token}")
                
                print(f"\n🎉 Demo assignment complete!")
                print(f"📊 Summary:")
                print(f"   - Test: Demo Knowledge Assessment")
                print(f"   - Duration: 20 minutes")
                print(f"   - Questions: 5 (Mixed types)")
                print(f"   - Deadline: 48 hours from now")
                print(f"   - Attempts: 2 per student")
                print(f"   - Students assigned: {success_count}")
                
                print(f"\n📋 Next Steps:")
                print(f"1. Students can login at: http://localhost:8503")
                print(f"2. Use credentials: <EMAIL> / student123")
                print(f"3. Students will see the assigned test with deadline")
                print(f"4. After completion, check 'Results & Grading' tab in admin panel")
                print(f"5. Admin can view answers and provide manual grades/remarks")
                
                return test_id
            else:
                print("❌ No students found. Please create student accounts first.")
                return None
        
        finally:
            db.close()
    else:
        print(f"❌ Error creating test: {message}")
        return None

if __name__ == "__main__":
    create_demo_assignment()
