"""
Check student access and provide clear instructions
"""
import requests
import time
from database_models import get_db_session, User, Test, TestAssignment, get_student_assignments

def check_student_portal_access():
    """Check if the student portal is accessible"""
    print("=== STUDENT PORTAL ACCESS CHECK ===")
    
    try:
        response = requests.get("http://localhost:8503", timeout=5)
        if response.status_code == 200:
            print("✅ Student portal is accessible at http://localhost:8503")
            return True
        else:
            print(f"❌ Student portal returned status code: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Student portal is not accessible - connection refused")
        return False
    except Exception as e:
        print(f"❌ Error checking student portal: {e}")
        return False

def verify_test_assignments():
    """Verify that test assignments exist for the student"""
    print("\n=== TEST ASSIGNMENTS VERIFICATION ===")
    
    db = get_db_session()
    try:
        # Check the test student specifically
        student = db.query(User).filter(User.email == '<EMAIL>').first()
        if not student:
            print("❌ Test student (<EMAIL>) not found!")
            return False
        
        print(f"✅ Test student found: {student.name} (ID: {student.id})")
        
        # Get assignments using the same function as the portal
        assignments = get_student_assignments(student.id)
        
        if not assignments:
            print("❌ No assignments found for test student!")
            return False
        
        available_assignments = [a for a in assignments if a.status in ['assigned', 'started']]
        
        print(f"📊 Total assignments: {len(assignments)}")
        print(f"📋 Available assignments: {len(available_assignments)}")
        
        if available_assignments:
            print("📝 Available tests:")
            for assignment in available_assignments:
                test = db.query(Test).filter(Test.id == assignment.test_id).first()
                if test:
                    print(f"   ✅ {test.title}")
                    print(f"      - Duration: {test.duration_minutes} minutes")
                    print(f"      - Attempts: {assignment.attempts_used}/{assignment.attempts_allowed}")
                    print(f"      - Status: {assignment.status}")
            return True
        else:
            print("❌ No available assignments found!")
            return False
            
    finally:
        db.close()

def provide_access_instructions():
    """Provide clear instructions for accessing the student portal"""
    print("\n=== ACCESS INSTRUCTIONS ===")
    print("🌐 CORRECT Student Portal URL: http://localhost:8503")
    print("👤 Login Credentials:")
    print("   📧 Email: <EMAIL>")
    print("   🔑 Password: student123")
    print("\n📋 Steps to access tests:")
    print("1. Open your web browser")
    print("2. Navigate to: http://localhost:8503")
    print("3. Enter the login credentials above")
    print("4. Click 'Login'")
    print("5. You should see available tests listed")
    
    print("\n⚠️  IMPORTANT NOTES:")
    print("- Make sure you're using http://localhost:8503 (not 8501 or 8502)")
    print("- If you see 'No tests currently assigned', you might be on the wrong interface")
    print("- The correct interface shows 'My Assigned Tests' after login")
    print("- If issues persist, try refreshing the page or clearing browser cache")

def main():
    """Main check function"""
    print("🔍 STUDENT PORTAL DIAGNOSTIC CHECK")
    print("=" * 50)
    
    # Check 1: Portal accessibility
    portal_accessible = check_student_portal_access()
    
    # Check 2: Test assignments
    assignments_exist = verify_test_assignments()
    
    # Provide instructions
    provide_access_instructions()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 DIAGNOSTIC SUMMARY:")
    print(f"   🌐 Portal Accessible: {'✅ YES' if portal_accessible else '❌ NO'}")
    print(f"   📋 Tests Available: {'✅ YES' if assignments_exist else '❌ NO'}")
    
    if portal_accessible and assignments_exist:
        print("\n🎉 EVERYTHING IS WORKING!")
        print("   The student portal should show available tests when you login.")
    else:
        print("\n⚠️  ISSUES DETECTED!")
        if not portal_accessible:
            print("   - Student portal is not running or accessible")
        if not assignments_exist:
            print("   - No test assignments found for the student")

if __name__ == "__main__":
    main()
