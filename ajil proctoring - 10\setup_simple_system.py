"""
Setup Simple Proctoring System
Creates database, users, and sample data
"""
import hashlib
import json
from simple_models import (
    Base, engine, get_db_session, User, Test, TestSession,
    create_test, create_test_session
)

def setup_system():
    """Setup the complete system"""
    print("🚀 Setting up Simple Proctoring System...")
    
    # Create all tables
    Base.metadata.create_all(engine)
    print("✅ Database tables created")
    
    # Create users
    create_users()
    
    # Create sample test
    create_sample_test()
    
    print("🎉 System setup complete!")
    print("\n📋 Access Information:")
    print("🔧 Admin Dashboard:")
    print("   - Run: streamlit run simple_launcher.py")
    print("   - Login: <EMAIL> / admin123")
    print("\n📝 Student Portal:")
    print("   - Run: streamlit run simple_launcher.py")
    print("   - Login: <EMAIL> / student123")
    print("\n🎯 Features:")
    print("   - PDF-based test creation")
    print("   - Session management with time limits")
    print("   - Audio/Video recording simulation")
    print("   - Real-time grading and feedback")

def create_users():
    """Create admin and student users"""
    db = get_db_session()
    try:
        # Admin user
        admin_password = hashlib.sha256("admin123".encode()).hexdigest()
        admin = User(
            name="Admin User",
            email="<EMAIL>",
            password_hash=admin_password,
            role="admin",
            is_active=True
        )
        
        # Student users
        student_password = hashlib.sha256("student123".encode()).hexdigest()
        
        students = [
            User(
                name="Test Student",
                email="<EMAIL>",
                password_hash=student_password,
                role="student",
                is_active=True
            ),
            User(
                name="John Doe",
                email="<EMAIL>",
                password_hash=student_password,
                role="student",
                is_active=True
            ),
            User(
                name="Jane Smith",
                email="<EMAIL>",
                password_hash=student_password,
                role="student",
                is_active=True
            ),
            User(
                name="Mike Johnson",
                email="<EMAIL>",
                password_hash=student_password,
                role="student",
                is_active=True
            )
        ]
        
        # Add all users
        db.add(admin)
        for student in students:
            db.add(student)
        
        db.commit()
        print("✅ Created users:")
        print("   - 1 Admin: <EMAIL>")
        print("   - 4 Students: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>")
        
    except Exception as e:
        print(f"⚠️ Users may already exist: {e}")
        db.rollback()
    finally:
        db.close()

def create_sample_test():
    """Create a sample test"""
    # Sample questions
    questions = [
        {
            'question': 'What is the capital of France?',
            'type': 'multiple_choice',
            'options': ['London', 'Berlin', 'Paris', 'Madrid'],
            'correct_answer': 2,
            'points': 2
        },
        {
            'question': 'The Earth is flat.',
            'type': 'true_false',
            'correct_answer': False,
            'points': 1
        },
        {
            'question': 'What is 15 + 25?',
            'type': 'multiple_choice',
            'options': ['35', '40', '45', '50'],
            'correct_answer': 1,
            'points': 2
        },
        {
            'question': 'Python is a programming language.',
            'type': 'true_false',
            'correct_answer': True,
            'points': 1
        },
        {
            'question': 'Explain the importance of renewable energy.',
            'type': 'short_answer',
            'correct_answer': 'Renewable energy is important for environmental sustainability and reducing carbon emissions.',
            'points': 4
        }
    ]
    
    # Create test
    questions_data = json.dumps({'questions': questions})
    success, test_id = create_test(
        title="Sample Knowledge Test",
        duration_minutes=20,
        questions_data=questions_data,
        created_by="<EMAIL>"
    )
    
    if success:
        print(f"✅ Created sample test (ID: {test_id})")
        
        # Create a sample session
        success, session_id = create_test_session(
            test_id=test_id,
            session_name="Demo Session - Morning Batch",
            max_participants=10,
            session_duration_hours=2,
            created_by="<EMAIL>"
        )
        
        if success:
            print(f"✅ Created sample session (ID: {session_id})")
            print("   - Session will be active for 2 hours")
            print("   - Max 10 participants")
            print("   - Students can join and take the test")
        else:
            print(f"❌ Error creating session: {session_id}")
    else:
        print(f"❌ Error creating test: {test_id}")

if __name__ == "__main__":
    setup_system()
