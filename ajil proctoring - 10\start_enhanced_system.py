"""
Enhanced Proctoring System Launcher
Starts the main application with proper configuration
"""
import streamlit as st
import sys
import os

# Set page config first (before any other Streamlit commands)
st.set_page_config(
    page_title="Enhanced AI Proctoring System",
    page_icon="🎓",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Initialize session state for AI features
if 'test_questions' not in st.session_state:
    st.session_state.test_questions = []
if 'ai_generated_questions' not in st.session_state:
    st.session_state.ai_generated_questions = []
if 'show_ai_review' not in st.session_state:
    st.session_state.show_ai_review = False

# Import main application
try:
    from main_app import main
    main()
except Exception as e:
    st.error(f"Error starting application: {e}")
    st.info("Please ensure all dependencies are installed and the database is properly initialized.")
