import{r,b as z}from"./index.DKN5MVff.js";import{s as J,i as D,a as Q,b as U,T as Z,C as ee,m as te}from"./index.LU8juINp.js";import"./withFullScreenWrapper.C-gXt0Rl.js";import"./Toolbar.Dt4jIKlY.js";import"./FormClearHelper.DF4gFAOO.js";import"./mergeWith.GRNk8iwv.js";import"./sprintf.D7DtBTRn.js";import"./checkbox.Cgxgc0et.js";import"./createDownloadLinkElement.ZaXNnPK4.js";import"./toConsumableArray.BJvaP8gb.js";import"./possibleConstructorReturn.Bd4ImlQ9.js";import"./createSuper.siQeagI2.js";import"./FileDownload.esm.Bz9nxNC5.js";const re=()=>t=>t.targetX,ie=()=>t=>t.targetY,ne=()=>t=>t.targetWidth,se=()=>t=>t.targetHeight,ae=()=>t=>t.targetY+10,oe=()=>t=>Math.max(0,(t.targetHeight-28)/2),le=J("div")({name:"DataGridOverlayEditorStyle",class:"gdg-d19meir1",propsAsIs:!1,vars:{"d19meir1-0":[ie(),"px"],"d19meir1-1":[re(),"px"],"d19meir1-2":[ne(),"px"],"d19meir1-3":[se(),"px"],"d19meir1-4":[ae(),"px"],"d19meir1-5":[oe(),"px"]}});function de(){const[t,s]=r.useState();return[t??void 0,s]}function ue(){const[t,s]=de(),[n,h]=r.useState(0),[f,v]=r.useState(!0);r.useLayoutEffect(()=>{if(t===void 0||!("IntersectionObserver"in window))return;const a=new IntersectionObserver(o=>{o.length!==0&&v(o[0].isIntersecting)},{threshold:1});return a.observe(t),()=>a.disconnect()},[t]),r.useEffect(()=>{if(f||t===void 0)return;let a;const o=()=>{const{right:x}=t.getBoundingClientRect();h(m=>Math.min(m+window.innerWidth-x-10,0)),a=requestAnimationFrame(o)};return a=requestAnimationFrame(o),()=>{a!==void 0&&cancelAnimationFrame(a)}},[t,f]);const C=r.useMemo(()=>({transform:`translateX(${n}px)`}),[n]);return{ref:s,style:C}}const Se=t=>{const{target:s,content:n,onFinishEditing:h,forceEditMode:f,initialValue:v,imageEditorOverride:C,markdownDivCreateNode:a,highlight:o,className:x,theme:m,id:H,cell:p,bloom:I,portalElementRef:w,validateCell:g,getCellRenderer:F,provideEditor:P,isOutsideClick:W,customEventTarget:X}=t,[l,A]=r.useState(f?n:void 0),O=r.useRef(l??n);O.current=l??n;const[y,S]=r.useState(()=>g===void 0?!0:!(D(n)&&g?.(p,n,O.current)===!1)),u=r.useCallback((e,i)=>{h(y?e:void 0,i)},[y,h]),K=r.useCallback(e=>{if(g!==void 0&&e!==void 0&&D(e)){const i=g(p,e,O.current);i===!1?S(!1):(typeof i=="object"&&(e=i),S(!0))}A(e)},[p,g]),E=r.useRef(!1),c=r.useRef(void 0),Y=r.useCallback(()=>{u(l,[0,0]),E.current=!0},[l,u]),j=r.useCallback((e,i)=>{u(e,i??c.current??[0,0]),E.current=!0},[u]),L=r.useCallback(async e=>{let i=!1;e.key==="Escape"?(e.stopPropagation(),e.preventDefault(),c.current=[0,0]):e.key==="Enter"&&!e.shiftKey?(e.stopPropagation(),e.preventDefault(),c.current=[0,1],i=!0):e.key==="Tab"&&(e.stopPropagation(),e.preventDefault(),c.current=[e.shiftKey?-1:1,0],i=!0),window.setTimeout(()=>{!E.current&&c.current!==void 0&&(u(i?l:void 0,c.current),E.current=!0)},0)},[u,l]),k=l??n,[d,q]=r.useMemo(()=>{if(Q(n))return[];const e={...n,location:p},i=P?.(e);return i!==void 0?[i,!1]:[F(n)?.provideEditor?.(e),!1]},[p,n,F,P]),{ref:B,style:$}=ue();let T=!0,M,_=!0,b;if(d!==void 0){T=d.disablePadding!==!0,_=d.disableStyling!==!0;const e=U(d);e&&(b=d.styleOverride);const i=e?d.editor:d;M=r.createElement(i,{portalElementRef:w,isHighlighted:o,onChange:K,value:k,initialValue:v,onFinishedEditing:j,validatedSelection:D(k)?k.selectionRange:void 0,forceEditMode:f,target:s,imageEditorOverride:C,markdownDivCreateNode:a,isValid:y,theme:m})}b={...b,...$};const N=w?.current??document.getElementById("portal");if(N===null)return console.error('Cannot open Data Grid overlay editor, because portal not found. Please, either provide a portalElementRef or add `<div id="portal" />` as the last child of your `<body>`.'),null;let R=_?"gdg-style":"gdg-unstyle";y||(R+=" gdg-invalid"),T&&(R+=" gdg-pad");const V=I?.[0]??1,G=I?.[1]??1;return z.createPortal(r.createElement(Z.Provider,{value:m},r.createElement(ee,{style:te(m),className:x,onClickOutside:Y,isOutsideClick:W,customEventTarget:X},r.createElement(le,{ref:B,id:H,className:R,style:b,as:q===!0?"label":void 0,targetX:s.x-V,targetY:s.y-G,targetWidth:s.width+V*2,targetHeight:s.height+G*2},r.createElement("div",{className:"gdg-clip-region",onKeyDown:L},M)))),N)};export{Se as default};
