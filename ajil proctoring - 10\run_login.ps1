Write-Host "Starting Proctoring System Login..." -ForegroundColor Green
Write-Host ""
Write-Host "Login will be available at: http://localhost:8501" -ForegroundColor Yellow
Write-Host ""

try {
    python -m streamlit run main_app.py --server.port 8501
}
catch {
    Write-Host "Error starting the application: $_" -ForegroundColor Red
    Write-Host "Make sure Streamlit is installed: pip install streamlit" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
