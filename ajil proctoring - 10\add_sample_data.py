"""
Add sample data to the database for demonstration
"""
import json
from simple_models import get_db_session, Test, create_test

def add_sample_tests():
    """Add sample tests to the database"""
    
    # Sample test 1: Mathematics
    math_questions = [
        {
            'question': 'What is 2 + 2?',
            'type': 'multiple_choice',
            'options': ['3', '4', '5', '6'],
            'correct_answer': 1,
            'points': 2
        },
        {
            'question': 'Is 10 greater than 5?',
            'type': 'true_false',
            'correct_answer': True,
            'points': 1
        },
        {
            'question': 'What is the square root of 16?',
            'type': 'multiple_choice',
            'options': ['2', '3', '4', '5'],
            'correct_answer': 2,
            'points': 3
        },
        {
            'question': 'Explain the Pythagorean theorem.',
            'type': 'short_answer',
            'correct_answer': 'The Pythagorean theorem states that in a right triangle, the square of the hypotenuse equals the sum of squares of the other two sides.',
            'points': 5
        }
    ]
    
    # Sample test 2: Science
    science_questions = [
        {
            'question': 'What is the chemical symbol for water?',
            'type': 'multiple_choice',
            'options': ['H2O', 'CO2', 'NaCl', 'O2'],
            'correct_answer': 0,
            'points': 2
        },
        {
            'question': 'The Earth revolves around the Sun.',
            'type': 'true_false',
            'correct_answer': True,
            'points': 1
        },
        {
            'question': 'How many planets are in our solar system?',
            'type': 'multiple_choice',
            'options': ['7', '8', '9', '10'],
            'correct_answer': 1,
            'points': 2
        },
        {
            'question': 'Describe photosynthesis.',
            'type': 'short_answer',
            'correct_answer': 'Photosynthesis is the process by which plants use sunlight, water, and carbon dioxide to produce glucose and oxygen.',
            'points': 5
        }
    ]
    
    # Sample test 3: History
    history_questions = [
        {
            'question': 'In which year did World War II end?',
            'type': 'multiple_choice',
            'options': ['1944', '1945', '1946', '1947'],
            'correct_answer': 1,
            'points': 2
        },
        {
            'question': 'The Great Wall of China was built in the 20th century.',
            'type': 'true_false',
            'correct_answer': False,
            'points': 1
        },
        {
            'question': 'Who was the first President of the United States?',
            'type': 'multiple_choice',
            'options': ['Thomas Jefferson', 'George Washington', 'John Adams', 'Benjamin Franklin'],
            'correct_answer': 1,
            'points': 2
        }
    ]
    
    # Create the tests
    tests_to_create = [
        {
            'title': 'Basic Mathematics Test',
            'duration': 30,
            'questions': math_questions,
            'created_by': '<EMAIL>'
        },
        {
            'title': 'General Science Quiz',
            'duration': 25,
            'questions': science_questions,
            'created_by': '<EMAIL>'
        },
        {
            'title': 'History Knowledge Test',
            'duration': 20,
            'questions': history_questions,
            'created_by': '<EMAIL>'
        }
    ]
    
    print("Adding sample tests to database...")
    
    for test_data in tests_to_create:
        questions_json = json.dumps({'questions': test_data['questions']})
        
        success, result = create_test(
            title=test_data['title'],
            duration_minutes=test_data['duration'],
            questions_data=questions_json,
            created_by=test_data['created_by']
        )
        
        if success:
            print(f"✅ Created test: {test_data['title']} (ID: {result})")
        else:
            print(f"❌ Failed to create test: {test_data['title']} - {result}")
    
    print("Sample data creation complete!")

def check_existing_tests():
    """Check if tests already exist"""
    db = get_db_session()
    try:
        tests = db.query(Test).filter(Test.is_active == True).all()
        print(f"Current tests in database: {len(tests)}")
        for test in tests:
            print(f"  - {test.title} ({test.duration_minutes} min)")
        return len(tests)
    finally:
        db.close()

if __name__ == "__main__":
    print("🎯 AI Examiner - Sample Data Setup")
    print("=" * 40)
    
    existing_count = check_existing_tests()
    
    if existing_count == 0:
        print("\nNo tests found. Adding sample tests...")
        add_sample_tests()
    else:
        print(f"\n{existing_count} tests already exist.")
        response = input("Do you want to add more sample tests? (y/n): ")
        if response.lower() == 'y':
            add_sample_tests()
        else:
            print("Skipping sample data creation.")
    
    print("\n" + "=" * 40)
    print("Setup complete! You can now use the AI Examiner system.")
