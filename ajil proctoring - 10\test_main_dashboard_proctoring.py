"""
Test Main Dashboard Proctoring Implementation
============================================

Verify that students can take tests with proctoring directly from localhost:8501
"""

import time
from database_models import (
    get_db_session, assign_test_to_student, User, Test, TestAssignment
)

def verify_main_dashboard_implementation():
    """Verify the main dashboard student interface implementation"""
    print("🔍 VERIFYING MAIN DASHBOARD PROCTORING IMPLEMENTATION")
    print("=" * 60)
    
    # Check if the main_app.py has student interface
    try:
        with open('main_app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        required_components = [
            ('elif st.session_state.user_role == \'student\':', 'Student role handling'),
            ('show_student_test_interface_with_proctoring', 'Student test interface function'),
            ('show_basic_student_interface', 'Basic student interface fallback'),
        ]
        
        print("📋 Main App Component Verification:")
        all_present = True
        
        for component, description in required_components:
            if component in content:
                print(f"   ✅ {description}")
            else:
                print(f"   ❌ {description} - MISSING")
                all_present = False
        
        return all_present
        
    except Exception as e:
        print(f"❌ Error verifying main app: {e}")
        return False

def verify_student_interface_implementation():
    """Verify the enhanced student interface implementation"""
    print("\n📚 STUDENT INTERFACE VERIFICATION")
    print("-" * 40)
    
    try:
        with open('student_test_interface.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        required_components = [
            ('show_student_test_interface_with_proctoring', 'Enhanced student interface'),
            ('show_available_tests_with_proctoring', 'Proctoring-enabled test display'),
            ('SimpleProctoringMonitor', 'JavaScript proctoring monitor'),
            ('initializeCamera', 'Camera initialization'),
            ('enableFullscreen', 'Fullscreen enforcement'),
            ('logViolation', 'Violation logging'),
            ('Start Proctored Test', 'Proctored test start button'),
        ]
        
        print("📍 Interface Checks:")
        all_implemented = True
        
        for component, description in required_components:
            if component in content:
                print(f"   ✅ {description}")
            else:
                print(f"   ❌ {description} - MISSING")
                all_implemented = False
        
        return all_implemented
        
    except Exception as e:
        print(f"❌ Error verifying student interface: {e}")
        return False

def setup_test_assignment_for_main_dashboard():
    """Setup test assignment for main dashboard testing"""
    print("\n📤 SETTING UP TEST ASSIGNMENT FOR MAIN DASHBOARD")
    print("-" * 50)
    
    db = get_db_session()
    try:
        # Get test student
        student = db.query(User).filter(
            User.email == '<EMAIL>',
            User.role == 'student'
        ).first()
        
        if not student:
            print("❌ Test student not found")
            return False
        
        # Get first available test
        test = db.query(Test).filter(Test.is_active == True).first()
        
        if not test:
            print("❌ No tests available")
            return False
        
        print(f"✅ Found student: {student.name}")
        print(f"✅ Found test: {test.title}")
        
        # Clear existing assignments
        existing_assignments = db.query(TestAssignment).filter(
            TestAssignment.student_id == student.id,
            TestAssignment.test_id == test.id
        ).all()
        
        for assignment in existing_assignments:
            db.delete(assignment)
        
        db.commit()
        
        # Create new assignment
        success, message = assign_test_to_student(
            test_id=test.id,
            student_id=student.id,
            deadline_hours=24,
            attempts_allowed=3
        )
        
        if success:
            print(f"✅ Test assigned successfully for main dashboard!")
            return True
        else:
            print(f"❌ Failed to assign test: {message}")
            return False
            
    except Exception as e:
        print(f"❌ Error setting up test: {e}")
        return False
    finally:
        db.close()

def show_testing_instructions():
    """Show testing instructions for main dashboard"""
    print("\n" + "=" * 60)
    print("🧪 MAIN DASHBOARD PROCTORING TESTING INSTRUCTIONS")
    print("=" * 60)
    
    print("1. 🌐 Open main dashboard: http://localhost:8501")
    print("2. 🔑 Login as STUDENT with: <EMAIL> / student123")
    print("3. 📚 You should see 'Student Test Dashboard' interface")
    print("4. 🚀 Click 'Start Proctored Test' on the assigned test")
    print()
    print("🔍 What to Test:")
    print("   📹 Camera Preview:")
    print("      • Should appear on TOP LEFT corner")
    print("      • Live video feed should start automatically")
    print("      • Size: 200x150 pixels with green border")
    print()
    print("   🖥️ Automatic Fullscreen:")
    print("      • Should activate immediately when test starts")
    print("      • If you exit fullscreen, should re-enable automatically")
    print("      • Check every 5 seconds for enforcement")
    print()
    print("   🔄 Tab Switch Detection:")
    print("      • Switch to another tab/window")
    print("      • Should log violation and increment counter")
    print("      • Counter should update in real-time")
    print()
    print("   ⚠️ Violation Counter:")
    print("      • Located below camera on top left")
    print("      • Should start at 'Violations: 0'")
    print("      • Color changes: Green → Yellow → Red")
    print()
    print("📊 Monitor in Browser Console:")
    print("   • Press F12 to open developer tools")
    print("   • Check Console tab for proctoring logs")
    print("   • Look for: '✅ Proctoring initialized from main dashboard'")
    print("   • Violation logs: '🚨 Violation logged'")
    print()
    print("🔄 Test Both Interfaces:")
    print("   • Main Dashboard (8501): Integrated student interface")
    print("   • Student Portal (8503): Dedicated student portal")
    print("   • Both should have identical proctoring features")

def main():
    """Main testing function"""
    print("🚀 MAIN DASHBOARD PROCTORING VERIFICATION")
    print("=" * 60)
    
    # Verify implementation
    main_app_ok = verify_main_dashboard_implementation()
    student_interface_ok = verify_student_interface_implementation()
    
    print("\n" + "=" * 60)
    print("📊 VERIFICATION RESULTS")
    print("=" * 60)
    
    if main_app_ok:
        print("✅ Main dashboard student interface: VERIFIED")
    else:
        print("❌ Main dashboard student interface: ISSUES FOUND")
    
    if student_interface_ok:
        print("✅ Enhanced student interface with proctoring: VERIFIED")
    else:
        print("❌ Enhanced student interface: ISSUES FOUND")
    
    if main_app_ok and student_interface_ok:
        print("\n🎉 ALL VERIFICATIONS PASSED!")
        
        # Setup test assignment
        assignment_ok = setup_test_assignment_for_main_dashboard()
        
        if assignment_ok:
            show_testing_instructions()
            
            print("\n" + "=" * 60)
            print("🎯 MAIN DASHBOARD PROCTORING FEATURES:")
            print("=" * 60)
            print("✅ Student login directly on localhost:8501")
            print("✅ Integrated proctoring interface")
            print("✅ Camera on top left corner (200x150px)")
            print("✅ Automatic camera initialization")
            print("✅ Automatic fullscreen enforcement")
            print("✅ Tab switch detection with counter")
            print("✅ Real-time violation logging")
            print("✅ Visual violation counter")
            print("✅ Seamless test start with proctoring")
            print("✅ Cross-browser compatibility")
            print("✅ Unified dashboard experience")
        else:
            print("❌ Test assignment setup failed")
    else:
        print("\n❌ SOME VERIFICATIONS FAILED - CHECK IMPLEMENTATION")

if __name__ == "__main__":
    main()
