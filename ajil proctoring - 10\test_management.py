"""
Test Management System for Admin Users
"""
import streamlit as st
import json
from datetime import datetime, timezone
from database_models import (
    create_test, get_tests_by_admin, Test, ProctorSession,
    create_proctor_session, get_active_sessions, get_db_session, User,
    TestAssignment, TestResult, assign_test_to_student, get_student_assignments, submit_test_result
)


def show_test_management():
    """Display test management interface for admins"""
    st.header("📝 Test Management")
    
    # Check if user is admin
    if st.session_state.get('user_role') != 'admin':
        st.error("Access denied. Admin privileges required.")
        return
    
    # Tabs for different test management functions
    tab1, tab2, tab3, tab4 = st.tabs(["Create Test", "Manage Tests", "Assign Tests", "Results & Grading"])

    with tab1:
        show_create_test()

    with tab2:
        show_manage_tests()

    with tab3:
        show_test_assignments()

    with tab4:
        show_test_results_grading()


def show_create_test():
    """Interface for creating new tests with modern UI"""

    # Custom CSS for modern UI
    st.markdown("""
    <style>
    .test-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 2rem;
        border-radius: 20px;
        color: white;
        margin-bottom: 2rem;
        text-align: center;
    }

    .upload-area {
        border: 2px dashed #dee2e6;
        border-radius: 15px;
        padding: 3rem 2rem;
        text-align: center;
        background: #f8f9fa;
        margin: 1rem 0;
        transition: all 0.3s ease;
    }

    .upload-area:hover {
        border-color: #6f42c1;
        background: rgba(111, 66, 193, 0.05);
    }

    .option-card {
        background: #f8f9fa;
        padding: 1.5rem;
        border-radius: 15px;
        margin: 1rem 0;
        border: 1px solid #dee2e6;
    }

    .metric-card {
        background: white;
        padding: 1.5rem;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        text-align: center;
        margin: 0.5rem 0;
    }

    .stButton > button {
        background: linear-gradient(135deg, #6f42c1, #5a359a);
        color: white;
        border: none;
        border-radius: 10px;
        padding: 0.8rem 2rem;
        font-weight: 600;
        transition: all 0.3s ease;
        width: 100%;
    }

    .stButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(111, 66, 193, 0.3);
    }
    </style>
    """, unsafe_allow_html=True)

    # Header with modern design
    st.markdown("""
    <div class="test-card">
        <h2>🎯 Create New Test</h2>
        <p>Upload your material and generate AI-powered questions</p>
    </div>
    """, unsafe_allow_html=True)

    # Simplified test creation - only file upload
    show_simple_file_upload_test()


def show_manual_test_creation():
    """Manual test creation interface"""
    st.subheader("Manual Test Creation")

    # Initialize questions in session state
    if 'test_questions' not in st.session_state:
        st.session_state.test_questions = []

    # Question management outside form
    st.subheader("📝 Manage Questions")

    col1, col2, col3 = st.columns([2, 1, 1])
    with col1:
        if st.button("➕ Add MCQ Question", type="secondary"):
            st.session_state.test_questions.append({
                'question': '',
                'type': 'multiple_choice',
                'options': ['', '', '', ''],
                'correct_answer': 0,
                'points': 1
            })
            st.rerun()

    with col2:
        if st.button("➕ Add True/False", type="secondary"):
            st.session_state.test_questions.append({
                'question': '',
                'type': 'true_false',
                'correct_answer': True,
                'points': 1
            })
            st.rerun()

    with col3:
        if st.button("➕ Add Essay", type="secondary"):
            st.session_state.test_questions.append({
                'question': '',
                'type': 'short_answer',
                'correct_answer': '',
                'points': 5
            })
            st.rerun()

    # Display existing questions
    if st.session_state.test_questions:
        st.write(f"**Questions Added: {len(st.session_state.test_questions)}**")

        for i, question in enumerate(st.session_state.test_questions):
            with st.expander(f"Question {i+1}: {question['type'].replace('_', ' ').title()}", expanded=True):
                col1, col2 = st.columns([4, 1])

                with col1:
                    question['question'] = st.text_area(
                        f"Question {i+1}",
                        value=question['question'],
                        key=f"q_{i}",
                        height=100
                    )

                    if question['type'] == 'multiple_choice':
                        st.write("**Options:**")
                        for j in range(4):
                            question['options'][j] = st.text_input(
                                f"Option {j+1}",
                                value=question['options'][j],
                                key=f"opt_{i}_{j}"
                            )
                        question['correct_answer'] = st.selectbox(
                            "Correct Answer",
                            [0, 1, 2, 3],
                            index=question['correct_answer'],
                            format_func=lambda x: f"Option {x+1}",
                            key=f"correct_{i}"
                        )
                    elif question['type'] == 'true_false':
                        question['correct_answer'] = st.selectbox(
                            "Correct Answer",
                            [True, False],
                            index=0 if question['correct_answer'] else 1,
                            key=f"tf_{i}"
                        )
                    elif question['type'] == 'short_answer':
                        question['correct_answer'] = st.text_area(
                            "Sample Answer/Keywords",
                            value=question['correct_answer'],
                            key=f"answer_{i}",
                            help="Provide sample answer or keywords for grading"
                        )

                    question['points'] = st.number_input(
                        "Points",
                        min_value=1,
                        value=question['points'],
                        key=f"points_{i}"
                    )

                with col2:
                    st.write("")  # Spacing
                    st.write("")  # Spacing
                    if st.button("🗑️ Remove", key=f"remove_{i}", type="secondary"):
                        st.session_state.test_questions.pop(i)
                        st.rerun()
    else:
        st.info("No questions added yet. Click the buttons above to add questions.")

    # Test creation form
    st.divider()
    st.subheader("📋 Test Details")

    with st.form("create_test_form"):
        # Basic test information
        col1, col2 = st.columns(2)

        with col1:
            title = st.text_input("Test Title*", placeholder="Enter test title")
            duration = st.number_input("Duration (minutes)*", min_value=1, max_value=300, value=60)

        with col2:
            description = st.text_area("Description", placeholder="Enter test description")

        # Test settings
        st.subheader("Test Settings")
        col3, col4 = st.columns(2)

        with col3:
            allow_tab_switch = st.checkbox("Allow tab switching", value=False)
            allow_fullscreen_exit = st.checkbox("Allow fullscreen exit", value=False)
            require_webcam = st.checkbox("Require webcam", value=True)

        with col4:
            max_violations = st.number_input("Max violations before termination", min_value=1, value=5)
            auto_submit = st.checkbox("Auto-submit on time expiry", value=True)
            record_session = st.checkbox("Record proctoring session", value=True)

        # Submit button
        submitted = st.form_submit_button("Create Test", type="primary")

        if submitted:
            if not title:
                st.error("Test title is required")
            elif not st.session_state.test_questions:
                st.error("At least one question is required")
            else:
                try:
                    # Prepare test settings
                    settings = {
                        'allow_tab_switch': allow_tab_switch,
                        'allow_fullscreen_exit': allow_fullscreen_exit,
                        'require_webcam': require_webcam,
                        'max_violations': max_violations,
                        'auto_submit': auto_submit,
                        'record_session': record_session
                    }

                    # Get current user ID
                    db = get_db_session()
                    try:
                        user = db.query(User).filter(User.email == st.session_state.user_email).first()
                        if user:
                            # Create the test
                            test = create_test(
                                title=title,
                                description=description,
                                duration_minutes=duration,
                                questions=st.session_state.test_questions,
                                settings=settings,
                                created_by=user.id
                            )

                            st.success(f"Test '{title}' created successfully!")
                            st.session_state.test_questions = []  # Clear questions
                            st.rerun()
                        else:
                            st.error("User not found")
                    finally:
                        db.close()

                except Exception as e:
                    st.error(f"Error creating test: {str(e)}")


def show_ai_generated_test():
    """AI-generated test creation interface"""
    st.subheader("🤖 AI-Generated Test Creation")

    with st.form("ai_test_form"):
        col1, col2 = st.columns(2)

        with col1:
            subject = st.text_input("Subject/Topic*", placeholder="e.g., Mathematics, Python Programming, History")
            difficulty = st.selectbox("Difficulty Level", ["Beginner", "Intermediate", "Advanced"])
            num_mcq = st.number_input("Number of MCQ Questions", min_value=0, max_value=50, value=10)
            num_essay = st.number_input("Number of Essay Questions", min_value=0, max_value=20, value=2)

        with col2:
            test_type = st.selectbox("Test Type", ["General Knowledge", "Problem Solving", "Conceptual", "Mixed"])
            duration = st.number_input("Duration (minutes)", min_value=10, max_value=300, value=60)
            keywords = st.text_area("Keywords/Topics", placeholder="Enter specific topics or keywords (optional)")

        # Additional options
        st.subheader("AI Generation Options")
        col3, col4 = st.columns(2)

        with col3:
            include_explanations = st.checkbox("Include answer explanations", value=True)
            web_search = st.checkbox("Search web for latest questions", value=True)

        with col4:
            review_before_create = st.checkbox("Review questions before creating test", value=True)
            save_as_template = st.checkbox("Save as template for future use", value=False)

        submitted = st.form_submit_button("🤖 Generate Test with AI", type="primary")

        if submitted:
            if not subject:
                st.error("Subject/Topic is required")
            else:
                with st.spinner("🤖 AI is generating your test... This may take a moment."):
                    generated_questions = generate_ai_questions(
                        subject=subject,
                        difficulty=difficulty,
                        num_mcq=num_mcq,
                        num_essay=num_essay,
                        test_type=test_type,
                        keywords=keywords,
                        web_search=web_search,
                        include_explanations=include_explanations
                    )

                    if generated_questions:
                        st.session_state.ai_generated_questions = generated_questions
                        st.success(f"✅ Generated {len(generated_questions)} questions!")

                        if review_before_create:
                            st.session_state.show_ai_review = True
                        else:
                            # Auto-create test
                            st.session_state.test_questions = generated_questions
                            st.success("Questions added to test! Go to 'Manual Creation' tab to finalize.")

                        st.rerun()
                    else:
                        st.error("Failed to generate questions. Please try again.")

    # Show AI-generated questions for review
    if st.session_state.get('show_ai_review', False) and st.session_state.get('ai_generated_questions'):
        st.divider()
        st.subheader("📋 Review AI-Generated Questions")

        questions = st.session_state.ai_generated_questions

        col1, col2, col3 = st.columns(3)
        with col1:
            if st.button("✅ Accept All Questions", type="primary"):
                st.session_state.test_questions = questions
                st.session_state.show_ai_review = False
                st.success("All questions added to test!")
                st.rerun()

        with col2:
            if st.button("🔄 Regenerate Questions"):
                st.session_state.show_ai_review = False
                st.session_state.ai_generated_questions = []
                st.rerun()

        with col3:
            if st.button("❌ Cancel"):
                st.session_state.show_ai_review = False
                st.session_state.ai_generated_questions = []
                st.rerun()

        # Display questions for review
        for i, question in enumerate(questions):
            with st.expander(f"Question {i+1}: {question['type'].replace('_', ' ').title()}", expanded=False):
                st.write(f"**Question:** {question['question']}")

                if question['type'] == 'multiple_choice':
                    st.write("**Options:**")
                    for j, option in enumerate(question['options']):
                        marker = "✅" if j == question['correct_answer'] else "○"
                        st.write(f"  {marker} {option}")
                elif question['type'] == 'true_false':
                    st.write(f"**Answer:** {question['correct_answer']}")
                elif question['type'] == 'short_answer':
                    st.write(f"**Sample Answer:** {question['correct_answer']}")

                st.write(f"**Points:** {question['points']}")

                if question.get('explanation'):
                    st.write(f"**Explanation:** {question['explanation']}")


def show_file_upload_test():
    """File upload test creation interface"""
    st.subheader("📁 Create Test from Uploaded Files")

    st.info("Upload documents, PDFs, or text files to automatically generate questions based on the content.")

    uploaded_files = st.file_uploader(
        "Upload Files",
        type=['pdf', 'docx', 'txt', 'md'],
        accept_multiple_files=True,
        help="Supported formats: PDF, Word documents, Text files, Markdown"
    )

    if uploaded_files:
        st.write(f"**Uploaded {len(uploaded_files)} file(s):**")
        for file in uploaded_files:
            st.write(f"- {file.name} ({file.size} bytes)")

        with st.form("file_test_form"):
            col1, col2 = st.columns(2)

            with col1:
                extraction_method = st.selectbox(
                    "Content Extraction Method",
                    ["Auto-detect", "Full Text", "Key Concepts", "Summary Based"]
                )
                question_focus = st.selectbox(
                    "Question Focus",
                    ["Comprehension", "Analysis", "Application", "Mixed"]
                )

            with col2:
                num_questions = st.number_input("Number of Questions", min_value=1, max_value=50, value=10)
                difficulty = st.selectbox("Difficulty Level", ["Easy", "Medium", "Hard"])

            # Question type distribution
            st.subheader("Question Type Distribution")
            col3, col4, col5 = st.columns(3)

            with col3:
                mcq_percentage = st.slider("MCQ Questions (%)", 0, 100, 60)
            with col4:
                tf_percentage = st.slider("True/False (%)", 0, 100, 20)
            with col5:
                essay_percentage = st.slider("Essay Questions (%)", 0, 100, 20)

            # Validation
            total_percentage = mcq_percentage + tf_percentage + essay_percentage
            if total_percentage != 100:
                st.warning(f"Question type percentages must add up to 100%. Current total: {total_percentage}%")

            submitted = st.form_submit_button("📄 Generate Test from Files", type="primary")

            if submitted:
                if total_percentage != 100:
                    st.error("Question type percentages must add up to 100%")
                else:
                    with st.spinner("📄 Processing uploaded files and generating questions..."):
                        file_questions = process_uploaded_files(
                            uploaded_files=uploaded_files,
                            extraction_method=extraction_method,
                            question_focus=question_focus,
                            num_questions=num_questions,
                            difficulty=difficulty,
                            mcq_percentage=mcq_percentage,
                            tf_percentage=tf_percentage,
                            essay_percentage=essay_percentage
                        )

                        if file_questions:
                            st.session_state.test_questions = file_questions
                            st.success(f"✅ Generated {len(file_questions)} questions from uploaded files!")
                            st.info("Questions added to test! Go to 'Manual Creation' tab to review and finalize.")
                        else:
                            st.error("Failed to generate questions from files. Please check file content and try again.")


def generate_ai_questions(subject, difficulty, num_mcq, num_essay, test_type, keywords, web_search, include_explanations):
    """Generate questions using AI (mock implementation)"""
    import random
    import time

    # Simulate AI processing time
    time.sleep(2)

    questions = []

    # Generate MCQ questions
    for i in range(num_mcq):
        if subject.lower() in ['math', 'mathematics']:
            question_text = f"What is the result of solving this {difficulty.lower()} mathematical problem?"
            options = ["Option A", "Option B", "Option C", "Option D"]
        elif subject.lower() in ['python', 'programming']:
            question_text = f"Which Python concept is demonstrated in this {difficulty.lower()} code example?"
            options = ["Lists", "Dictionaries", "Functions", "Classes"]
        else:
            question_text = f"What is a key concept in {subject} at {difficulty.lower()} level?"
            options = ["Concept A", "Concept B", "Concept C", "Concept D"]

        question = {
            'question': question_text,
            'type': 'multiple_choice',
            'options': options,
            'correct_answer': random.randint(0, 3),
            'points': 1
        }

        if include_explanations:
            question['explanation'] = f"This question tests understanding of {subject} concepts at {difficulty.lower()} level."

        questions.append(question)

    # Generate essay questions
    for i in range(num_essay):
        if test_type == "Problem Solving":
            question_text = f"Solve this {difficulty.lower()} problem in {subject} and explain your approach."
        else:
            question_text = f"Explain a key concept in {subject} and provide examples."

        question = {
            'question': question_text,
            'type': 'short_answer',
            'correct_answer': f"Sample answer for {subject} question",
            'points': 5
        }

        if include_explanations:
            question['explanation'] = f"This essay question evaluates analytical thinking in {subject}."

        questions.append(question)

    return questions


def process_uploaded_files(uploaded_files, extraction_method, question_focus, num_questions, difficulty, mcq_percentage, tf_percentage, essay_percentage):
    """Process uploaded files and generate questions (mock implementation)"""
    import time

    # Simulate file processing time
    time.sleep(3)

    questions = []

    # Calculate number of each question type
    num_mcq = int(num_questions * mcq_percentage / 100)
    num_tf = int(num_questions * tf_percentage / 100)
    num_essay = num_questions - num_mcq - num_tf

    # Generate MCQ questions based on file content
    for i in range(num_mcq):
        questions.append({
            'question': f"Based on the uploaded content, which statement is correct about the main topic?",
            'type': 'multiple_choice',
            'options': ["Statement A from content", "Statement B from content", "Statement C from content", "Statement D from content"],
            'correct_answer': 0,
            'points': 1
        })

    # Generate True/False questions
    for i in range(num_tf):
        questions.append({
            'question': f"True or False: This statement from the uploaded content is accurate.",
            'type': 'true_false',
            'correct_answer': True,
            'points': 1
        })

    # Generate essay questions
    for i in range(num_essay):
        questions.append({
            'question': f"Analyze the main concepts presented in the uploaded content and provide your interpretation.",
            'type': 'short_answer',
            'correct_answer': "Sample analysis based on uploaded content",
            'points': 5
        })

    return questions


def show_manage_tests():
    """Interface for managing existing tests"""
    st.subheader("Manage Existing Tests")
    
    # Get current user email
    admin_email = st.session_state.get('user_email', '<EMAIL>')

    # Get tests created by this admin
    db = get_db_session()
    try:
        tests = get_tests_by_admin(admin_email)
        
        if not tests:
            st.info("No tests created yet. Create your first test in the 'Create Test' tab.")
            return
        
        # Display tests in a table format
        for test in tests:
            with st.expander(f"📝 {test.title} ({test.duration_minutes} min)", expanded=False):
                col1, col2, col3 = st.columns([2, 1, 1])
                
                with col1:
                    st.write(f"**Description:** {test.description or 'No description'}")
                    st.write(f"**Created:** {test.created_at.strftime('%Y-%m-%d %H:%M')}")
                    st.write(f"**Questions:** {len(test.get_questions())}")
                    st.write(f"**Status:** {'Active' if test.is_active else 'Inactive'}")
                
                with col2:
                    if st.button(f"Start Session", key=f"start_{test.id}"):
                        show_start_session_dialog(test)
                    
                    if st.button(f"View Details", key=f"details_{test.id}"):
                        show_test_details(test)
                
                with col3:
                    if test.is_active:
                        if st.button(f"Deactivate", key=f"deactivate_{test.id}"):
                            deactivate_test(test.id)
                    else:
                        if st.button(f"Activate", key=f"activate_{test.id}"):
                            activate_test(test.id)
    
    finally:
        db.close()


def show_active_sessions():
    """Display currently active proctoring sessions"""
    st.subheader("Active Proctoring Sessions")
    
    active_sessions = get_active_sessions()
    
    if not active_sessions:
        st.info("No active proctoring sessions.")
        return
    
    # Display active sessions
    for session in active_sessions:
        with st.container():
            col1, col2, col3, col4 = st.columns([2, 1, 1, 1])
            
            with col1:
                st.write(f"**Session ID:** {session.id}")
                st.write(f"**Test ID:** {session.test_id}")
                st.write(f"**Student ID:** {session.student_id}")
                if session.started_at:
                    duration = datetime.now(timezone.utc) - session.started_at
                    st.write(f"**Duration:** {str(duration).split('.')[0]}")
            
            with col2:
                st.metric("Violations", session.total_violations)
            
            with col3:
                st.metric("Face Score", f"{session.face_detection_score:.1%}")
            
            with col4:
                if st.button(f"Terminate", key=f"terminate_{session.id}"):
                    terminate_session(session.id)
            
            st.divider()


def show_combined_sessions():
    """Show active sessions at top and older sessions below"""
    st.subheader("📊 Session Management")

    db = get_db_session()
    try:
        # Get all sessions
        all_sessions = db.query(ProctorSession).order_by(ProctorSession.created_at.desc()).all()

        if not all_sessions:
            st.info("📝 No session data found. Create a test and start sessions to see data here.")
            return

        # Separate active and completed sessions
        active_sessions = [s for s in all_sessions if s.status == 'active']
        older_sessions = [s for s in all_sessions if s.status != 'active']

        # Show active sessions first
        if active_sessions:
            st.markdown("### 🟢 Active Sessions")
            for session in active_sessions:
                with st.container():
                    col1, col2, col3, col4 = st.columns([2, 2, 2, 1])

                    with col1:
                        st.write(f"**Session ID:** {session.id}")
                        st.write(f"**Status:** 🟢 {session.status.title()}")

                    with col2:
                        test = db.query(Test).filter(Test.id == session.test_id).first()
                        st.write(f"**Test:** {test.title if test else 'Unknown'}")
                        st.write(f"**Started:** {session.started_at.strftime('%Y-%m-%d %H:%M') if session.started_at else 'Not started'}")

                    with col3:
                        student = db.query(User).filter(User.id == session.student_id).first()
                        st.write(f"**Student:** {student.name if student else 'Unknown'}")
                        st.write(f"**Duration:** {session.total_duration or 0} min")

                    with col4:
                        if st.button("🛑 Stop", key=f"stop_{session.id}", type="secondary"):
                            terminate_session(session.id)

                    st.divider()
        else:
            st.markdown("### 🟢 Active Sessions")
            st.info("No active sessions currently running.")

        # Show older sessions
        if older_sessions:
            st.markdown("### 📋 Session History")

            # Create a table for older sessions
            session_data = []
            for session in older_sessions[:20]:  # Show last 20 sessions
                test = db.query(Test).filter(Test.id == session.test_id).first()
                student = db.query(User).filter(User.id == session.student_id).first()

                session_data.append({
                    'ID': session.id,
                    'Test': test.title if test else 'Unknown',
                    'Student': student.name if student else 'Unknown',
                    'Status': session.status.title(),
                    'Started': session.started_at.strftime('%Y-%m-%d %H:%M') if session.started_at else 'Not started',
                    'Completed': session.completed_at.strftime('%Y-%m-%d %H:%M') if session.completed_at else '-',
                    'Duration': f"{session.total_duration or 0} min",
                    'Violations': session.total_violations or 0
                })

            if session_data:
                import pandas as pd
                df = pd.DataFrame(session_data)
                st.dataframe(df, use_container_width=True, hide_index=True)
            else:
                st.info("No session history available.")
        else:
            st.markdown("### 📋 Session History")
            st.info("No completed sessions found.")

    except Exception as e:
        st.error(f"Error loading sessions: {str(e)}")
    finally:
        db.close()


def show_start_session_dialog(test):
    """Show dialog to start a proctoring session"""
    st.subheader(f"Start Session for: {test.title}")
    
    # Get list of students
    db = get_db_session()
    try:
        students = db.query(User).filter(User.role == 'student', User.is_active == True).all()
        
        if not students:
            st.error("No active students found.")
            return
        
        student_options = {f"{student.name} ({student.email})": student.id for student in students}
        selected_student = st.selectbox("Select Student", options=list(student_options.keys()))
        
        if st.button("Start Proctoring Session"):
            try:
                student_id = student_options[selected_student]
                session = create_proctor_session(test.id, student_id)
                st.success(f"Proctoring session created! Session Token: {session.session_token}")
            except Exception as e:
                st.error(f"Error creating session: {str(e)}")
    
    finally:
        db.close()


def show_test_details(test):
    """Show detailed test information"""
    st.subheader(f"Test Details: {test.title}")
    
    # Basic information
    st.write(f"**Description:** {test.description}")
    st.write(f"**Duration:** {test.duration_minutes} minutes")
    st.write(f"**Created:** {test.created_at.strftime('%Y-%m-%d %H:%M')}")
    
    # Settings
    try:
        settings = test.get_settings()
        if settings:
            st.subheader("Settings")
            for key, value in settings.items():
                st.write(f"**{key.replace('_', ' ').title()}:** {value}")
        else:
            st.subheader("Settings")
            st.write("No settings configured")
    except Exception as e:
        st.subheader("Settings")
        st.write("Error loading settings")
    
    # Questions
    questions = test.get_questions()
    st.subheader(f"Questions ({len(questions)})")
    for i, question in enumerate(questions):
        with st.expander(f"Question {i+1}"):
            st.write(f"**Question:** {question['question']}")
            st.write(f"**Type:** {question['type']}")
            st.write(f"**Points:** {question['points']}")
            
            if question['type'] == 'multiple_choice':
                st.write("**Options:**")
                for j, option in enumerate(question['options']):
                    marker = "✓" if j == question['correct_answer'] else "○"
                    st.write(f"  {marker} {option}")


def deactivate_test(test_id):
    """Deactivate a test"""
    db = get_db_session()
    try:
        test = db.query(Test).filter(Test.id == test_id).first()
        if test:
            test.is_active = False
            db.commit()
            st.success("Test deactivated successfully!")
            st.rerun()
    except Exception as e:
        st.error(f"Error deactivating test: {str(e)}")
    finally:
        db.close()


def activate_test(test_id):
    """Activate a test"""
    db = get_db_session()
    try:
        test = db.query(Test).filter(Test.id == test_id).first()
        if test:
            test.is_active = True
            db.commit()
            st.success("Test activated successfully!")
            st.rerun()
    except Exception as e:
        st.error(f"Error activating test: {str(e)}")
    finally:
        db.close()


def terminate_session(session_id):
    """Terminate an active session"""
    db = get_db_session()
    try:
        session = db.query(ProctorSession).filter(ProctorSession.id == session_id).first()
        if session:
            session.status = 'terminated'
            session.completed_at = datetime.now(timezone.utc)
            if session.started_at:
                session.total_duration = int((session.completed_at - session.started_at).total_seconds())
            db.commit()
            st.success("Session terminated successfully!")
            st.rerun()
    except Exception as e:
        st.error(f"Error terminating session: {str(e)}")
    finally:
        db.close()


def show_modern_file_upload_test():
    """Modern file upload interface similar to aiexaminer.app"""

    # Modern upload area
    st.markdown("""
    <div style="
        border: 2px dashed #dee2e6;
        border-radius: 15px;
        padding: 3rem 2rem;
        text-align: center;
        background: #f8f9fa;
        margin: 1rem 0;
        transition: all 0.3s ease;
    ">
        <div style="font-size: 3rem; color: #6f42c1; margin-bottom: 1rem;">
            📁
        </div>
        <h4>Upload your material here</h4>
        <p style="color: #6c757d;">
            Drag and drop files here, or click to browse<br>
            Supported formats: PDF, DOC, DOCX, TXT
        </p>
    </div>
    """, unsafe_allow_html=True)

    uploaded_file = st.file_uploader(
        "Choose a file",
        type=['pdf', 'doc', 'docx', 'txt'],
        help="Upload documents to generate AI-powered questions",
        label_visibility="collapsed"
    )

    if uploaded_file:
        st.success(f"✅ File uploaded: {uploaded_file.name}")

        # Test configuration form
        with st.form("modern_test_form"):
            st.markdown("### 📋 Test Configuration")

            col1, col2 = st.columns(2)

            with col1:
                title = st.text_input("📝 Document Title", placeholder="Enter a title for your test")
                test_format = st.selectbox(
                    "📋 Test Format",
                    ["Multiple Choice", "True/False", "Short Answer", "Essay", "Mixed"]
                )
                total_questions = st.number_input("🔢 Total Questions", min_value=1, max_value=100, value=5)

            with col2:
                difficulty = st.selectbox("📊 Difficulty Level", ["Easy", "Medium", "Hard"])
                time_limit = st.number_input("⏱️ Time Limit (minutes)", min_value=5, max_value=300, value=30)
                passing_score = st.number_input("🎯 Passing Score (%)", min_value=0, max_value=100, value=70)

            # Proctoring options
            st.markdown("### 🛡️ Proctoring Options")

            col3, col4 = st.columns(2)

            with col3:
                enable_camera = st.checkbox("📹 Enable Camera Monitoring", value=True)
                enable_screen = st.checkbox("🖥️ Enable Screen Recording", value=False)
                enable_face = st.checkbox("👤 Enable Face Detection", value=True)

            with col4:
                negative_marking = st.checkbox("➖ Enable Negative Marking", value=False)
                randomize_questions = st.checkbox("🔀 Randomize Questions", value=True)
                strict_mode = st.checkbox("🔒 Strict Mode", value=False)

            # Timer options
            st.markdown("### ⏰ Timer Settings")
            col5, col6 = st.columns(2)

            with col5:
                auto_submit = st.checkbox("⏰ Auto-submit on time", value=True)
                show_timer = st.checkbox("👁️ Show timer to students", value=True)

            with col6:
                warning_time = st.number_input("⚠️ Warning time (minutes before end)", min_value=1, max_value=30, value=5)
                grace_period = st.number_input("🕐 Grace period (seconds)", min_value=0, max_value=300, value=30)

            # Submit button
            submitted = st.form_submit_button("🎯 Generate Test", type="primary")

            if submitted:
                if title:
                    # Create progress bar
                    progress_bar = st.progress(0)
                    status_text = st.empty()

                    # Simulate processing
                    import time

                    status_text.text("📄 Processing document...")
                    progress_bar.progress(25)
                    time.sleep(1)

                    status_text.text("🤖 Generating AI questions...")
                    progress_bar.progress(50)
                    time.sleep(1)

                    status_text.text("⚙️ Configuring test settings...")
                    progress_bar.progress(75)
                    time.sleep(1)

                    status_text.text("✅ Finalizing test...")
                    progress_bar.progress(100)
                    time.sleep(0.5)

                    # Create test in database
                    admin_email = st.session_state.get('user_email', '<EMAIL>')

                    test_config = {
                        'title': title,
                        'format': test_format,
                        'total_questions': total_questions,
                        'difficulty': difficulty,
                        'time_limit': time_limit,
                        'passing_score': passing_score,
                        'file_name': uploaded_file.name,
                        'proctoring': {
                            'camera': enable_camera,
                            'screen_recording': enable_screen,
                            'face_detection': enable_face,
                            'strict_mode': strict_mode
                        },
                        'settings': {
                            'negative_marking': negative_marking,
                            'randomize': randomize_questions,
                            'auto_submit': auto_submit,
                            'show_timer': show_timer,
                            'warning_time': warning_time,
                            'grace_period': grace_period
                        }
                    }

                    # Generate sample questions (in real implementation, this would use AI)
                    sample_questions = generate_sample_questions(test_format, total_questions, difficulty)

                    success, message = create_test(
                        title=title,
                        description=f"{test_format} test generated from {uploaded_file.name}",
                        duration_minutes=time_limit,
                        created_by=admin_email,
                        questions_data=json.dumps({
                            'config': test_config,
                            'questions': sample_questions
                        })
                    )

                    if success:
                        progress_bar.empty()
                        status_text.empty()

                        st.success(f"🎉 Test '{title}' created successfully!")
                        st.balloons()

                        # Show test summary
                        st.markdown("### 📊 Test Summary")

                        col_a, col_b, col_c = st.columns(3)

                        with col_a:
                            st.metric("Questions", total_questions)
                            st.metric("Duration", f"{time_limit} min")

                        with col_b:
                            st.metric("Format", test_format)
                            st.metric("Difficulty", difficulty)

                        with col_c:
                            st.metric("Passing Score", f"{passing_score}%")
                            st.metric("Proctoring", "✅ Enabled" if enable_camera else "❌ Disabled")

                        # Next steps
                        st.info("""
                        **🎯 Next Steps:**
                        1. Review and edit questions in the 'Manage Tests' tab
                        2. Set up proctoring sessions in 'Session Control'
                        3. Share test links with students
                        4. Monitor test sessions in real-time
                        """)

                        # Option to preview test
                        if st.button("👀 Preview Test", type="secondary"):
                            st.session_state.preview_test_id = message  # Assuming message contains test ID
                            st.rerun()

                    else:
                        progress_bar.empty()
                        status_text.empty()
                        st.error(f"❌ Error creating test: {message}")
                else:
                    st.error("Please provide a test title.")

    else:
        # Show example of what can be uploaded
        st.markdown("### 💡 What you can upload:")

        col1, col2 = st.columns(2)

        with col1:
            st.markdown("""
            **📄 Document Types:**
            - PDF files
            - Word documents (.doc, .docx)
            - Text files (.txt)
            - PowerPoint presentations (.ppt, .pptx)
            """)

        with col2:
            st.markdown("""
            **🎯 AI will generate:**
            - Multiple choice questions
            - True/False questions
            - Short answer questions
            - Essay prompts
            """)


def generate_sample_questions(test_format, num_questions, difficulty):
    """Generate sample questions for demonstration"""
    questions = []

    for i in range(num_questions):
        if test_format == "Multiple Choice":
            questions.append({
                'question': f'Sample multiple choice question {i+1} ({difficulty} level)',
                'type': 'multiple_choice',
                'options': [
                    f'Option A for question {i+1}',
                    f'Option B for question {i+1}',
                    f'Option C for question {i+1}',
                    f'Option D for question {i+1}'
                ],
                'correct_answer': 0,
                'points': 1,
                'difficulty': difficulty
            })
        elif test_format == "True/False":
            questions.append({
                'question': f'Sample true/false question {i+1} ({difficulty} level)',
                'type': 'true_false',
                'correct_answer': True,
                'points': 1,
                'difficulty': difficulty
            })
        elif test_format == "Short Answer":
            questions.append({
                'question': f'Sample short answer question {i+1} ({difficulty} level)',
                'type': 'short_answer',
                'correct_answer': f'Sample answer for question {i+1}',
                'points': 2,
                'difficulty': difficulty
            })

    return questions


def show_simple_file_upload_test():
    """Simplified file upload interface for test creation"""

    st.markdown("### 📁 Upload Document to Generate Test")
    st.info("Upload a PDF document and the system will automatically scan and generate questions from the content.")

    # File upload
    uploaded_file = st.file_uploader(
        "Choose a PDF file",
        type=['pdf'],
        help="Upload a PDF document to generate questions automatically"
    )

    if uploaded_file:
        st.success(f"✅ File uploaded: {uploaded_file.name}")

        # Simple configuration
        with st.form("simple_test_form"):
            col1, col2 = st.columns(2)

            with col1:
                title = st.text_input("Test Title", value=uploaded_file.name.replace('.pdf', ''))
                num_questions = st.number_input("Number of Questions", min_value=5, max_value=50, value=10)

            with col2:
                time_limit = st.number_input("Time Limit (minutes)", min_value=10, max_value=180, value=30)
                difficulty = st.selectbox("Difficulty Level", ["Easy", "Medium", "Hard"])

            submitted = st.form_submit_button("🎯 Generate Test from PDF", type="primary")

            if submitted and title:
                # Create progress bar
                progress_bar = st.progress(0)
                status_text = st.empty()

                # Simulate PDF processing
                import time

                status_text.text("📄 Scanning PDF document...")
                progress_bar.progress(20)
                time.sleep(1)

                status_text.text("🔍 Extracting text content...")
                progress_bar.progress(40)
                time.sleep(1)

                status_text.text("🤖 Generating questions from content...")
                progress_bar.progress(60)
                time.sleep(1)

                status_text.text("⚙️ Creating test structure...")
                progress_bar.progress(80)
                time.sleep(1)

                status_text.text("✅ Finalizing test...")
                progress_bar.progress(100)
                time.sleep(0.5)

                # Generate questions from PDF content
                questions = generate_questions_from_pdf(uploaded_file, num_questions, difficulty)

                # Create test in database
                admin_email = st.session_state.get('user_email', '<EMAIL>')

                success, message = create_test(
                    title=title,
                    description=f"Test generated from {uploaded_file.name}",
                    duration_minutes=time_limit,
                    questions=json.dumps({
                        'questions': questions,
                        'source_file': uploaded_file.name
                    }),
                    settings=json.dumps({
                        'time_limit': time_limit,
                        'difficulty': difficulty,
                        'auto_submit': True,
                        'randomize': True
                    }),
                    created_by=admin_email
                )

                if success:
                    progress_bar.empty()
                    status_text.empty()

                    st.success(f"🎉 Test '{title}' created and activated successfully!")
                    st.balloons()

                    # Show test info
                    st.info(f"""
                    **✅ Test is now active and available to students!**

                    **Test Details:**
                    - Title: {title}
                    - Questions: {len(questions)}
                    - Duration: {time_limit} minutes
                    - Status: Active

                    Students can now login and take this test immediately.
                    """)

                else:
                    progress_bar.empty()
                    status_text.empty()
                    st.error(f"❌ Error creating test: {message}")

    else:
        st.info("📁 Please upload a PDF file to create a test.")


def generate_questions_from_pdf(uploaded_file, num_questions, difficulty):
    """Generate questions from PDF content"""
    # In a real implementation, this would:
    # 1. Extract text from PDF using PyPDF2 or pdfplumber
    # 2. Use NLP to identify key concepts
    # 3. Generate questions based on content
    # 4. Create multiple choice options

    # For now, generate sample questions based on file name and settings
    questions = []

    # Extract some context from filename
    filename = uploaded_file.name.lower()

    # Generate contextual questions based on filename
    if 'math' in filename or 'calculus' in filename or 'algebra' in filename:
        base_topics = ['equations', 'functions', 'derivatives', 'integrals', 'graphs']
    elif 'science' in filename or 'physics' in filename or 'chemistry' in filename:
        base_topics = ['atoms', 'molecules', 'energy', 'forces', 'reactions']
    elif 'history' in filename:
        base_topics = ['events', 'dates', 'people', 'causes', 'effects']
    elif 'english' in filename or 'literature' in filename:
        base_topics = ['themes', 'characters', 'plot', 'style', 'meaning']
    else:
        base_topics = ['concepts', 'principles', 'methods', 'applications', 'examples']

    for i in range(num_questions):
        if i % 3 == 0:  # Multiple choice
            topic = base_topics[i % len(base_topics)]
            questions.append({
                'question': f'According to the document, what is the most important aspect of {topic}?',
                'type': 'multiple_choice',
                'options': [
                    f'The fundamental principles of {topic}',
                    f'The practical applications of {topic}',
                    f'The historical development of {topic}',
                    f'The theoretical framework of {topic}'
                ],
                'correct_answer': 0,
                'points': 1,
                'difficulty': difficulty
            })
        elif i % 3 == 1:  # True/False
            topic = base_topics[i % len(base_topics)]
            questions.append({
                'question': f'The document emphasizes the importance of understanding {topic} in practical contexts.',
                'type': 'true_false',
                'correct_answer': True,
                'points': 1,
                'difficulty': difficulty
            })
        else:  # Short answer
            topic = base_topics[i % len(base_topics)]
            questions.append({
                'question': f'Explain the key concepts related to {topic} as discussed in the document.',
                'type': 'short_answer',
                'correct_answer': f'The document discusses {topic} in the context of its fundamental principles and practical applications.',
                'points': 2,
                'difficulty': difficulty
            })

    return questions


def terminate_session(session_id):
    """Terminate a session"""
    db = get_db_session()
    try:
        session = db.query(ProctorSession).filter(ProctorSession.id == session_id).first()
        if session:
            session.status = 'terminated'
            session.completed_at = datetime.now(timezone.utc)
            if session.started_at:
                session.total_duration = int((session.completed_at - session.started_at).total_seconds())
            db.commit()
            st.success(f"Session {session_id} terminated successfully.")
        else:
            st.error("Session not found.")
    except Exception as e:
        st.error(f"Error terminating session: {str(e)}")
    finally:
        db.close()


def show_test_assignments():
    """Interface for assigning tests to students with deadlines"""
    st.subheader("📋 Assign Tests to Students")

    # Get current admin's tests
    admin_email = st.session_state.get('user_email', '<EMAIL>')
    tests = get_tests_by_admin(admin_email)

    if not tests:
        st.info("No tests available. Create a test first in the 'Create Test' tab.")
        return

    # Assignment form
    with st.form("assign_test_form"):
        col1, col2 = st.columns(2)

        with col1:
            # Select test
            test_options = {f"{test.title} ({test.duration_minutes} min)": test.id for test in tests}
            selected_test_name = st.selectbox("Select Test", options=list(test_options.keys()))
            selected_test_id = test_options[selected_test_name] if selected_test_name else None

            # Get students
            db = get_db_session()
            try:
                students = db.query(User).filter(User.role == 'student', User.is_active == True).all()
                if not students:
                    st.error("No active students found.")
                    return

                student_options = {f"{student.name} ({student.email})": student.id for student in students}
                selected_students = st.multiselect("Select Students", options=list(student_options.keys()))
            finally:
                db.close()

        with col2:
            # Assignment settings
            deadline_hours = st.number_input("Deadline (hours from now)", min_value=1, max_value=168, value=24)
            attempts_allowed = st.number_input("Attempts Allowed", min_value=1, max_value=5, value=1)

            # Auto-assign to all students option
            assign_to_all = st.checkbox("Assign to all active students")

        submitted = st.form_submit_button("📤 Assign Test", type="primary")

        if submitted and selected_test_id:
            if assign_to_all:
                # Assign to all students
                success_count = 0
                error_count = 0

                for student in students:
                    success, message = assign_test_to_student(
                        selected_test_id, student.id, deadline_hours, attempts_allowed
                    )
                    if success:
                        success_count += 1
                    else:
                        error_count += 1

                if success_count > 0:
                    st.success(f"✅ Test assigned to {success_count} students successfully!")
                if error_count > 0:
                    st.warning(f"⚠️ {error_count} assignments failed (may already exist)")

            elif selected_students:
                # Assign to selected students
                success_count = 0
                error_count = 0

                for student_name in selected_students:
                    student_id = student_options[student_name]
                    success, message = assign_test_to_student(
                        selected_test_id, student_id, deadline_hours, attempts_allowed
                    )
                    if success:
                        success_count += 1
                    else:
                        error_count += 1
                        st.error(f"Error assigning to {student_name}: {message}")

                if success_count > 0:
                    st.success(f"✅ Test assigned to {success_count} students successfully!")
            else:
                st.error("Please select students or choose 'Assign to all students'")

    # Show current assignments
    st.divider()
    st.subheader("📊 Current Test Assignments")

    db = get_db_session()
    try:
        # Get all assignments for admin's tests
        test_ids = [test.id for test in tests]
        assignments = db.query(TestAssignment).filter(
            TestAssignment.test_id.in_(test_ids)
        ).order_by(TestAssignment.assigned_at.desc()).all()

        if assignments:
            assignment_data = []
            for assignment in assignments:
                test = db.query(Test).filter(Test.id == assignment.test_id).first()
                student = db.query(User).filter(User.id == assignment.student_id).first()

                # Check if expired
                current_time = datetime.now(timezone.utc)
                if assignment.deadline < current_time and assignment.status == 'assigned':
                    assignment.status = 'expired'
                    db.commit()

                assignment_data.append({
                    'Test': test.title if test else 'Unknown',
                    'Student': student.name if student else 'Unknown',
                    'Email': student.email if student else 'Unknown',
                    'Status': assignment.status.title(),
                    'Assigned': assignment.assigned_at.strftime('%Y-%m-%d %H:%M'),
                    'Deadline': assignment.deadline.strftime('%Y-%m-%d %H:%M'),
                    'Attempts': f"{assignment.attempts_used}/{assignment.attempts_allowed}",
                    'Access Token': assignment.access_token[:8] + "..."
                })

            if assignment_data:
                import pandas as pd
                df = pd.DataFrame(assignment_data)
                st.dataframe(df, use_container_width=True, hide_index=True)

                # Download assignments
                csv = df.to_csv(index=False)
                st.download_button(
                    label="📥 Download Assignments CSV",
                    data=csv,
                    file_name=f"test_assignments_{datetime.now().strftime('%Y%m%d')}.csv",
                    mime="text/csv"
                )
        else:
            st.info("No test assignments found.")

    except Exception as e:
        st.error(f"Error loading assignments: {str(e)}")
    finally:
        db.close()


def show_test_results_grading():
    """Interface for viewing and grading test results"""
    st.subheader("📊 Test Results & Grading")

    # Get current admin's tests
    admin_email = st.session_state.get('user_email', '<EMAIL>')
    tests = get_tests_by_admin(admin_email)

    if not tests:
        st.info("No tests available.")
        return

    # Filter options
    col1, col2, col3 = st.columns(3)

    with col1:
        test_filter = st.selectbox(
            "Filter by Test",
            options=["All Tests"] + [test.title for test in tests]
        )

    with col2:
        status_filter = st.selectbox(
            "Filter by Status",
            options=["All", "Completed", "In Progress", "Auto Submitted"]
        )

    with col3:
        grading_filter = st.selectbox(
            "Grading Status",
            options=["All", "Graded", "Ungraded"]
        )

    # Get test results
    db = get_db_session()
    try:
        test_ids = [test.id for test in tests]
        query = db.query(TestResult).filter(TestResult.test_id.in_(test_ids))

        # Apply filters
        if test_filter != "All Tests":
            selected_test = next((t for t in tests if t.title == test_filter), None)
            if selected_test:
                query = query.filter(TestResult.test_id == selected_test.id)

        if status_filter != "All":
            status_map = {
                "Completed": "completed",
                "In Progress": "in_progress",
                "Auto Submitted": "auto_submitted"
            }
            query = query.filter(TestResult.status == status_map[status_filter])

        if grading_filter == "Graded":
            query = query.filter(TestResult.graded_at.isnot(None))
        elif grading_filter == "Ungraded":
            query = query.filter(TestResult.graded_at.is_(None))

        results = query.order_by(TestResult.started_at.desc()).all()

        if results:
            # Display results
            for result in results:
                test = db.query(Test).filter(Test.id == result.test_id).first()
                student = db.query(User).filter(User.id == result.student_id).first()

                with st.expander(
                    f"📝 {test.title if test else 'Unknown'} - {student.name if student else 'Unknown'} "
                    f"({result.percentage:.1f}%)",
                    expanded=False
                ):
                    col1, col2, col3 = st.columns([2, 1, 1])

                    with col1:
                        st.write(f"**Student:** {student.name if student else 'Unknown'} ({student.email if student else 'Unknown'})")
                        st.write(f"**Test:** {test.title if test else 'Unknown'}")
                        st.write(f"**Started:** {result.started_at.strftime('%Y-%m-%d %H:%M')}")
                        if result.completed_at:
                            st.write(f"**Completed:** {result.completed_at.strftime('%Y-%m-%d %H:%M')}")
                        st.write(f"**Time Taken:** {result.time_taken // 60 if result.time_taken else 0} minutes")

                    with col2:
                        st.metric("Auto Score", f"{result.score}/{result.max_score}")
                        st.metric("Percentage", f"{result.percentage:.1f}%")
                        st.metric("Status", result.status.title())

                    with col3:
                        # Manual grading
                        if result.status in ['completed', 'auto_submitted']:
                            with st.form(f"grade_form_{result.id}"):
                                admin_score = st.number_input(
                                    "Manual Score",
                                    min_value=0.0,
                                    max_value=float(result.max_score),
                                    value=float(result.admin_score or result.score),
                                    step=0.5,
                                    key=f"score_{result.id}"
                                )

                                admin_remarks = st.text_area(
                                    "Remarks",
                                    value=result.admin_remarks or "",
                                    height=100,
                                    key=f"remarks_{result.id}"
                                )

                                if st.form_submit_button("💾 Save Grade"):
                                    result.admin_score = admin_score
                                    result.admin_remarks = admin_remarks
                                    result.graded_at = datetime.now(timezone.utc)
                                    result.graded_by = admin_email
                                    db.commit()
                                    st.success("Grade saved!")
                                    st.rerun()

                    # Show student answers
                    if st.button(f"👁️ View Answers", key=f"view_{result.id}"):
                        show_student_answers(result, test)
        else:
            st.info("No test results found with the selected filters.")

    except Exception as e:
        st.error(f"Error loading results: {str(e)}")
    finally:
        db.close()


def show_student_answers(result, test):
    """Display student answers for review"""
    st.subheader("📋 Student Answers Review")

    try:
        answers = result.get_answers()
        questions = test.get_questions()

        if not questions:
            st.error("No questions found for this test.")
            return

        for i, question in enumerate(questions):
            question_key = f"q_{i}"

            st.markdown(f"**Question {i+1}:** {question.get('question', 'No question text')}")

            # Show correct answer
            if question.get('type') == 'multiple_choice':
                options = question.get('options', [])
                correct_idx = question.get('correct_answer', 0)
                st.write(f"**Correct Answer:** {options[correct_idx] if correct_idx < len(options) else 'Unknown'}")

                # Show student answer
                if question_key in answers:
                    student_answer_idx = answers[question_key]['answer']
                    student_answer = options[student_answer_idx] if student_answer_idx < len(options) else 'Invalid'
                    is_correct = student_answer_idx == correct_idx
                    st.write(f"**Student Answer:** {student_answer} {'✅' if is_correct else '❌'}")
                else:
                    st.write("**Student Answer:** Not answered ❌")

            elif question.get('type') == 'true_false':
                correct_answer = question.get('correct_answer', True)
                st.write(f"**Correct Answer:** {correct_answer}")

                if question_key in answers:
                    student_answer = answers[question_key]['answer']
                    is_correct = student_answer == correct_answer
                    st.write(f"**Student Answer:** {student_answer} {'✅' if is_correct else '❌'}")
                else:
                    st.write("**Student Answer:** Not answered ❌")

            elif question.get('type') == 'short_answer':
                st.write(f"**Expected Answer:** {question.get('correct_answer', 'No expected answer')}")

                if question_key in answers:
                    student_answer = answers[question_key]['answer']
                    st.write(f"**Student Answer:** {student_answer}")
                    st.info("💡 Short answer questions require manual grading")
                else:
                    st.write("**Student Answer:** Not answered")

            st.divider()

    except Exception as e:
        st.error(f"Error displaying answers: {str(e)}")
