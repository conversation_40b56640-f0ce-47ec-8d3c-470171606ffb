"""
Verify that the demo tests are properly created and accessible in admin dashboard
"""
from database_models import get_db_session, get_tests_by_admin, Test, User
import json

def verify_tests():
    """Verify that tests are properly created and accessible"""
    print("🔍 Verifying Demo Tests...")
    
    db = get_db_session()
    try:
        # Get admin user
        admin = db.query(User).filter(User.role == 'admin').first()
        if not admin:
            print("❌ No admin user found")
            return False
        
        print(f"✅ Found admin user: {admin.name} ({admin.email})")
        
        # Get all tests
        all_tests = db.query(Test).filter(Test.is_active == True).all()
        print(f"✅ Total active tests in database: {len(all_tests)}")
        
        # Get tests by admin (this is what the admin dashboard uses)
        admin_tests = get_tests_by_admin(admin.email)
        print(f"✅ Tests accessible by admin: {len(admin_tests)}")
        
        if admin_tests:
            print("\n📋 Available Tests for Assignment:")
            print("-" * 50)
            
            for i, test in enumerate(admin_tests, 1):
                print(f"{i}. {test.title}")
                print(f"   Duration: {test.duration_minutes} minutes")
                print(f"   Description: {test.description}")
                
                # Parse questions to show count
                try:
                    if test.questions_data:
                        questions_data = json.loads(test.questions_data)
                        if isinstance(questions_data, list):
                            question_count = len(questions_data)
                        elif isinstance(questions_data, dict) and 'questions' in questions_data:
                            question_count = len(questions_data['questions'])
                        else:
                            question_count = "Unknown"
                        print(f"   Questions: {question_count}")
                    else:
                        print(f"   Questions: No questions data")
                except:
                    print(f"   Questions: Error parsing questions")
                
                print(f"   Created: {test.created_at}")
                print()
            
            return True
        else:
            print("❌ No tests found for admin")
            return False
            
    except Exception as e:
        print(f"❌ Error verifying tests: {e}")
        return False
    finally:
        db.close()

def check_admin_dashboard_access():
    """Check if admin dashboard can access the tests"""
    print("🌐 Checking Admin Dashboard Access...")
    
    try:
        # Simulate what the admin dashboard does
        admin_email = "<EMAIL>"  # Default admin email
        tests = get_tests_by_admin(admin_email)
        
        if tests:
            print(f"✅ Admin dashboard can access {len(tests)} tests")
            print("✅ Tests are ready for assignment in admin interface")
            return True
        else:
            print("❌ Admin dashboard cannot access tests")
            return False
            
    except Exception as e:
        print(f"❌ Error checking admin dashboard access: {e}")
        return False

def show_assignment_instructions():
    """Show instructions for assigning tests"""
    print("\n" + "=" * 60)
    print("📋 HOW TO ASSIGN TESTS IN ADMIN DASHBOARD")
    print("=" * 60)
    print("1. Open admin dashboard: http://localhost:8501")
    print("2. Login with admin credentials")
    print("3. Go to 'Test Management' tab")
    print("4. Click on 'Assign Tests' sub-tab")
    print("5. You should see 3 demo tests available:")
    print("   • Python Programming Fundamentals")
    print("   • Mathematics & Logic Assessment") 
    print("   • General Knowledge & Critical Thinking")
    print("6. Select a test from the dropdown")
    print("7. Choose students to assign to")
    print("8. Set deadline and attempts allowed")
    print("9. Click 'Assign Test' button")
    print("\n🎯 Students can then access assigned tests at: http://localhost:8503")

def main():
    """Main verification function"""
    print("🚀 Demo Tests Verification")
    print("=" * 60)
    
    # Verify tests exist and are accessible
    tests_ok = verify_tests()
    
    # Check admin dashboard access
    dashboard_ok = check_admin_dashboard_access()
    
    print("\n" + "=" * 60)
    print("📊 VERIFICATION RESULTS")
    print("=" * 60)
    
    if tests_ok and dashboard_ok:
        print("✅ All verifications passed!")
        print("✅ Demo tests are ready for assignment")
        show_assignment_instructions()
    else:
        print("❌ Some verifications failed")
        if not tests_ok:
            print("   - Tests not properly created or accessible")
        if not dashboard_ok:
            print("   - Admin dashboard cannot access tests")

if __name__ == "__main__":
    main()
