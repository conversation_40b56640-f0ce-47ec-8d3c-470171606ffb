import{r as l,j as n,bu as c,bU as g}from"./index.DKN5MVff.js";import{a as b}from"./useBasicWidgetState.DB3vMS9V.js";import"./FormClearHelper.DF4gFAOO.js";const m=(t,e)=>t.getStringValue(e),f=t=>t.default??null,p=t=>t.value??null,C=(t,e,r,o)=>{e.setStringValue(t,r.value,{fromUi:r.fromUi},o)},d=({element:t,disabled:e,widgetMgr:r,fragmentId:o})=>{const[i,a]=b({getStateFromWidgetMgr:m,getDefaultStateFromProto:f,getCurrStateFromProto:p,updateWidgetMgrState:C,element:t,widgetMgr:r,fragmentId:o}),s=l.useCallback(u=>{a({value:u,fromUi:!0})},[a]);return n(g,{label:t.label,labelVisibility:c(t.labelVisibility?.value),help:t.help,onChange:s,disabled:e,value:i})},h=l.memo(d);export{h as default};
