"""
Verification script to check student portal functionality
"""
from database_models import get_db_session, User, Test, TestAssignment, get_student_assignments

def verify_student_portal():
    """Verify that the student portal will show tests correctly"""
    print("=== STUDENT PORTAL VERIFICATION ===")
    
    # Check all students
    db = get_db_session()
    try:
        students = db.query(User).filter(User.role == 'student', User.is_active == True).all()
        
        print(f"\n👥 Found {len(students)} active students:")
        
        for student in students:
            print(f"\n📋 Student: {student.name} ({student.email})")
            
            # Get assignments using the same function as the portal
            assignments = get_student_assignments(student.id)
            
            available = [a for a in assignments if a.status in ['assigned', 'started']]
            completed = [a for a in assignments if a.status == 'completed']
            expired = [a for a in assignments if a.status == 'expired']
            
            print(f"   📊 Total assignments: {len(assignments)}")
            print(f"   📋 Available: {len(available)}")
            print(f"   ✅ Completed: {len(completed)}")
            print(f"   ⏰ Expired: {len(expired)}")
            
            if available:
                print("   📝 Available tests:")
                for assignment in available:
                    test = db.query(Test).filter(Test.id == assignment.test_id).first()
                    if test:
                        print(f"      - {test.title} ({assignment.attempts_used}/{assignment.attempts_allowed} attempts)")
            else:
                print("   ⚠️  No available tests!")
                
    finally:
        db.close()
    
    print("\n🔍 Active Tests in Database:")
    db = get_db_session()
    try:
        active_tests = db.query(Test).filter(Test.is_active == True).all()
        for test in active_tests:
            print(f"   ✅ {test.title} (ID: {test.id}) - Created by: {test.created_by}")
    finally:
        db.close()

def check_test_student_specifically():
    """Check the specific test student that was having issues"""
    print("\n=== SPECIFIC TEST STUDENT CHECK ===")
    
    db = get_db_session()
    try:
        student = db.query(User).filter(User.email == '<EMAIL>').first()
        if not student:
            print("❌ Test student not found!")
            return
            
        print(f"✅ Test Student: {student.name} (ID: {student.id})")
        
        # Get assignments
        assignments = get_student_assignments(student.id)
        print(f"📊 Total assignments: {len(assignments)}")
        
        for i, assignment in enumerate(assignments, 1):
            test = db.query(Test).filter(Test.id == assignment.test_id).first()
            print(f"\n   Assignment {i}:")
            print(f"   📝 Test: {test.title if test else 'Unknown'}")
            print(f"   📊 Status: {assignment.status}")
            print(f"   🎯 Attempts: {assignment.attempts_used}/{assignment.attempts_allowed}")
            print(f"   ⏰ Deadline: {assignment.deadline}")
            print(f"   🔑 Access Token: {assignment.access_token[:8]}...")
            
    finally:
        db.close()

if __name__ == "__main__":
    verify_student_portal()
    check_test_student_specifically()
    
    print("\n🎉 VERIFICATION COMPLETE!")
    print("\n📋 Instructions to test:")
    print("1. Open browser to: http://localhost:8503")
    print("2. Login with: <EMAIL> / student123")
    print("3. You should see 3 available tests!")
    print("4. All tests should be startable with proper attempt counts")
