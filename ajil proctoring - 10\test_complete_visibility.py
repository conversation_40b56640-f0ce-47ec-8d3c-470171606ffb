"""
Complete visibility test for questions and options
"""
import streamlit as st

def main():
    st.set_page_config(
        page_title="Complete Visibility Test",
        page_icon="👁️",
        layout="wide"
    )
    
    # Apply the same CSS as the student interface
    st.markdown("""
    <style>
    .question-card {
        background: #f8f9fa;
        color: #2c3e50;
        padding: 2rem;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
        border-left: 5px solid #667eea;
    }
    
    .question-text {
        color: #2c3e50;
        font-size: 1.1rem;
        font-weight: 500;
        margin-bottom: 1rem;
        line-height: 1.6;
    }
    
    .question-number {
        color: #667eea;
        font-weight: 600;
        font-size: 1.2rem;
    }
    
    /* Streamlit component styling */
    .stRadio > div {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 10px;
        border: 1px solid #dee2e6;
    }
    
    .stRadio label {
        color: #2c3e50 !important;
        font-weight: 500;
    }
    
    .stRadio > div > label > div {
        color: #2c3e50 !important;
    }
    
    .stRadio > div > label > div > div {
        color: #2c3e50 !important;
    }
    
    .stRadio > div > label > div > div > p {
        color: #2c3e50 !important;
        font-size: 1rem !important;
    }
    
    .stRadio div[role="radiogroup"] label {
        color: #2c3e50 !important;
        background: #ffffff !important;
        padding: 0.5rem !important;
        margin: 0.25rem 0 !important;
        border-radius: 8px !important;
        border: 1px solid #dee2e6 !important;
    }
    
    .stRadio div[role="radiogroup"] label:hover {
        background: #e9ecef !important;
        border-color: #667eea !important;
    }
    
    .stMarkdown h3 {
        color: #2c3e50 !important;
    }
    
    .stMarkdown p {
        color: #2c3e50 !important;
    }
    </style>
    """, unsafe_allow_html=True)
    
    st.title("👁️ Complete Visibility Test")
    st.markdown("**Testing both questions and options visibility**")
    
    # Test 1: Multiple Choice Question
    st.markdown("""
    <div class="question-card">
        <div class="question-number">Question 1 of 3</div>
        <div class="question-text">What is the primary purpose of AI proctoring in online examinations?</div>
    </div>
    """, unsafe_allow_html=True)
    
    q1 = st.radio(
        "Select your answer for Question 1:",
        [
            "To replace human proctors entirely",
            "To monitor student behavior and detect potential cheating", 
            "To grade tests automatically",
            "To provide technical support during exams"
        ],
        key="test_q1"
    )
    
    st.markdown("---")
    
    # Test 2: True/False Question
    st.markdown("""
    <div class="question-card">
        <div class="question-number">Question 2 of 3</div>
        <div class="question-text">Students are allowed to switch tabs or applications during a proctored exam.</div>
    </div>
    """, unsafe_allow_html=True)
    
    q2 = st.radio(
        "Select your answer for Question 2:",
        ["True", "False"],
        key="test_q2"
    )
    
    st.markdown("---")
    
    # Test 3: Short Answer
    st.markdown("""
    <div class="question-card">
        <div class="question-number">Question 3 of 3</div>
        <div class="question-text">Explain in 2-3 sentences why maintaining eye contact with the camera is important during an AI-proctored exam.</div>
    </div>
    """, unsafe_allow_html=True)
    
    q3 = st.text_area(
        "Your answer for Question 3:",
        placeholder="Type your answer here...",
        key="test_q3"
    )
    
    # Visibility Check
    if st.button("🔍 Check Visibility"):
        st.success("✅ Visibility Test Results:")
        
        visibility_checks = [
            ("Question Cards", "Light gray background with dark text"),
            ("Question Numbers", "Blue color (#667eea)"),
            ("Question Text", "Dark blue-gray (#2c3e50)"),
            ("Radio Button Options", "White background with dark text"),
            ("Radio Button Hover", "Light gray hover effect"),
            ("Text Area", "Light background with dark text")
        ]
        
        for item, description in visibility_checks:
            st.write(f"• **{item}:** {description}")
        
        if q1 and q2 and q3:
            st.balloons()
            st.success("🎉 All components are working and visible!")
        else:
            st.info("💡 Try selecting options and typing text to test interactivity")

if __name__ == "__main__":
    main()
