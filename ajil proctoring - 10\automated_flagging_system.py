"""
Automated Flagging System
Monitors proctoring sessions and automatically flags suspicious activity
"""
import time
import threading
from datetime import datetime, timedelta
from database_models import (
    get_db_session, ProctorSession, MonitoringEvent, User, Test,
    log_monitoring_event
)
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('flagging_system.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AutomatedFlaggingSystem:
    """Automated system for flagging suspicious proctoring sessions"""
    
    def __init__(self):
        self.running = False
        self.check_interval = 30  # Check every 30 seconds
        self.flagging_thread = None
        
        # Flagging thresholds
        self.thresholds = {
            'critical_violations': 1,  # 1+ critical = immediate flag
            'high_violations': 3,      # 3+ high = auto flag
            'medium_violations': 5,    # 5+ medium = review required
            'total_violations': 8      # 8+ total = high risk
        }
        
        # Violation severity mapping
        self.violation_severity = {
            'face_lost': 'high',
            'multiple_faces': 'critical',
            'tab_switch': 'high',
            'fullscreen_exit': 'medium',
            'window_blur': 'medium',
            'blocked_shortcut': 'medium',
            'right_click_attempt': 'low',
            'suspicious_movement': 'medium',
            'phone_detected': 'high'
        }
    
    def start_monitoring(self):
        """Start the automated flagging system"""
        if self.running:
            logger.warning("Flagging system is already running")
            return
        
        self.running = True
        self.flagging_thread = threading.Thread(target=self._monitoring_loop)
        self.flagging_thread.daemon = True
        self.flagging_thread.start()
        
        logger.info("Automated flagging system started")
    
    def stop_monitoring(self):
        """Stop the automated flagging system"""
        self.running = False
        if self.flagging_thread:
            self.flagging_thread.join()
        
        logger.info("Automated flagging system stopped")
    
    def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.running:
            try:
                self._check_active_sessions()
                time.sleep(self.check_interval)
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(self.check_interval)
    
    def _check_active_sessions(self):
        """Check all active sessions for flagging criteria"""
        db = get_db_session()
        try:
            # Get all active sessions
            active_sessions = db.query(ProctorSession).filter(
                ProctorSession.status == 'active'
            ).all()
            
            for session in active_sessions:
                self._evaluate_session(session)
                
        finally:
            db.close()
    
    def _evaluate_session(self, session):
        """Evaluate a session for flagging"""
        db = get_db_session()
        try:
            # Get all violations for this session
            violations = db.query(MonitoringEvent).filter(
                MonitoringEvent.session_id == session.id
            ).all()
            
            if not violations:
                return
            
            # Count violations by severity
            violation_counts = {
                'critical': 0,
                'high': 0,
                'medium': 0,
                'low': 0,
                'total': len(violations)
            }
            
            for violation in violations:
                severity = violation.severity.lower()
                if severity in violation_counts:
                    violation_counts[severity] += 1
            
            # Check flagging criteria
            flag_reason = self._check_flagging_criteria(violation_counts)
            
            if flag_reason:
                self._flag_session(session, flag_reason, violation_counts)
                
        finally:
            db.close()
    
    def _check_flagging_criteria(self, violation_counts):
        """Check if session meets flagging criteria"""
        
        # Critical violations - immediate flag
        if violation_counts['critical'] >= self.thresholds['critical_violations']:
            return f"Critical violations detected ({violation_counts['critical']})"
        
        # High violations - auto flag
        if violation_counts['high'] >= self.thresholds['high_violations']:
            return f"High violation threshold exceeded ({violation_counts['high']})"
        
        # Medium violations - review required
        if violation_counts['medium'] >= self.thresholds['medium_violations']:
            return f"Medium violation threshold exceeded ({violation_counts['medium']})"
        
        # Total violations - high risk
        if violation_counts['total'] >= self.thresholds['total_violations']:
            return f"Total violation threshold exceeded ({violation_counts['total']})"
        
        return None
    
    def _flag_session(self, session, reason, violation_counts):
        """Flag a session and log the action"""
        try:
            # Check if already flagged
            if hasattr(session, 'flagged') and session.flagged:
                return
            
            # Log flagging event
            log_monitoring_event(
                session_id=session.id,
                event_type='session_flagged',
                event_data={
                    'reason': reason,
                    'violation_counts': violation_counts,
                    'flagged_at': datetime.now().isoformat()
                },
                severity='critical'
            )
            
            # Get student and test info for logging
            db = get_db_session()
            try:
                student = db.query(User).filter(User.id == session.student_id).first()
                test = db.query(Test).filter(Test.id == session.test_id).first()
                
                student_name = student.name if student else 'Unknown'
                test_title = test.title if test else 'Unknown'
                
                logger.warning(
                    f"SESSION FLAGGED - Student: {student_name}, "
                    f"Test: {test_title}, Reason: {reason}"
                )
                
                # Send alert (could be email, SMS, etc.)
                self._send_alert(session, student_name, test_title, reason)
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Error flagging session {session.id}: {e}")
    
    def _send_alert(self, session, student_name, test_title, reason):
        """Send alert to administrators"""
        # This could be enhanced to send actual emails/SMS
        alert_message = (
            f"PROCTORING ALERT: Session {session.id} flagged\n"
            f"Student: {student_name}\n"
            f"Test: {test_title}\n"
            f"Reason: {reason}\n"
            f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        )
        
        logger.critical(alert_message)
        
        # TODO: Implement actual alert mechanisms
        # - Email notifications
        # - SMS alerts
        # - Dashboard notifications
        # - Slack/Teams integration
    
    def get_flagged_sessions(self, hours=24):
        """Get all flagged sessions in the last N hours"""
        db = get_db_session()
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            flagged_events = db.query(MonitoringEvent).filter(
                MonitoringEvent.event_type == 'session_flagged',
                MonitoringEvent.timestamp >= cutoff_time
            ).all()
            
            flagged_sessions = []
            for event in flagged_events:
                session = db.query(ProctorSession).filter(
                    ProctorSession.id == event.session_id
                ).first()
                
                if session:
                    student = db.query(User).filter(
                        User.id == session.student_id
                    ).first()
                    
                    test = db.query(Test).filter(
                        Test.id == session.test_id
                    ).first()
                    
                    flagged_sessions.append({
                        'session_id': session.id,
                        'student_name': student.name if student else 'Unknown',
                        'test_title': test.title if test else 'Unknown',
                        'flagged_at': event.timestamp,
                        'reason': event.get_event_data().get('reason', 'Unknown'),
                        'violation_counts': event.get_event_data().get('violation_counts', {})
                    })
            
            return flagged_sessions
            
        finally:
            db.close()

# Global instance
flagging_system = AutomatedFlaggingSystem()

def start_flagging_system():
    """Start the automated flagging system"""
    flagging_system.start_monitoring()

def stop_flagging_system():
    """Stop the automated flagging system"""
    flagging_system.stop_monitoring()

def get_flagged_sessions(hours=24):
    """Get flagged sessions"""
    return flagging_system.get_flagged_sessions(hours)

if __name__ == "__main__":
    # Start the flagging system
    print("Starting Automated Flagging System...")
    start_flagging_system()
    
    try:
        # Keep running
        while True:
            time.sleep(60)
    except KeyboardInterrupt:
        print("Stopping Automated Flagging System...")
        stop_flagging_system()
