"""
Clear existing tests and create 3 new demo tests for admin assignment
"""
import json
from database_models import (
    get_db_session, create_test, Test, User, TestAssignment, TestResult,
    assign_test_to_student
)

def clear_existing_tests():
    """Clear all existing tests and related data"""
    print("🗑️ Clearing existing tests...")
    
    db = get_db_session()
    try:
        # Delete test results first (foreign key constraint)
        results_deleted = db.query(TestResult).delete()
        print(f"   ✅ Deleted {results_deleted} test results")
        
        # Delete test assignments
        assignments_deleted = db.query(TestAssignment).delete()
        print(f"   ✅ Deleted {assignments_deleted} test assignments")
        
        # Delete tests
        tests_deleted = db.query(Test).delete()
        print(f"   ✅ Deleted {tests_deleted} tests")
        
        db.commit()
        print("✅ All existing tests cleared successfully!")
        return True
        
    except Exception as e:
        db.rollback()
        print(f"❌ Error clearing tests: {e}")
        return False
    finally:
        db.close()

def create_demo_test_1():
    """Create Demo Test 1: Python Programming Fundamentals"""
    print("📝 Creating Demo Test 1: Python Programming Fundamentals...")
    
    questions = [
        {
            'question': 'What is the correct way to define a function in Python?',
            'type': 'multiple_choice',
            'options': [
                'function myFunc():',
                'def myFunc():',
                'create myFunc():',
                'func myFunc():'
            ],
            'correct_answer': 'def myFunc():',
            'points': 2,
            'difficulty': 'Easy'
        },
        {
            'question': 'Which of the following is a mutable data type in Python?',
            'type': 'multiple_choice',
            'options': [
                'tuple',
                'string',
                'list',
                'integer'
            ],
            'correct_answer': 'list',
            'points': 2,
            'difficulty': 'Medium'
        },
        {
            'question': 'What will be the output of: print(type([1, 2, 3]))?',
            'type': 'multiple_choice',
            'options': [
                "<class 'tuple'>",
                "<class 'list'>",
                "<class 'dict'>",
                "<class 'set'>"
            ],
            'correct_answer': "<class 'list'>",
            'points': 2,
            'difficulty': 'Easy'
        },
        {
            'question': 'Explain the difference between a list and a tuple in Python.',
            'type': 'short_answer',
            'correct_answer': 'Lists are mutable (can be changed) while tuples are immutable (cannot be changed). Lists use square brackets [] while tuples use parentheses ().',
            'points': 4,
            'difficulty': 'Medium'
        },
        {
            'question': 'Write a Python function that takes a list of numbers and returns the sum of all even numbers.',
            'type': 'essay',
            'correct_answer': 'def sum_even_numbers(numbers):\n    return sum(num for num in numbers if num % 2 == 0)',
            'points': 5,
            'difficulty': 'Hard'
        }
    ]
    
    settings = {
        'allow_tab_switch': False,
        'allow_fullscreen_exit': False,
        'require_webcam': True,
        'max_violations': 3,
        'auto_submit': True,
        'record_session': True
    }
    
    # Get admin user
    db = get_db_session()
    try:
        admin = db.query(User).filter(User.role == 'admin').first()
        if not admin:
            print("❌ No admin user found")
            return False
        
        success, message = create_test(
            title="Python Programming Fundamentals",
            description="Test your knowledge of Python basics including functions, data types, and programming concepts",
            duration_minutes=45,
            questions=questions,
            settings=settings,
            created_by=admin.id
        )
        
        if success:
            print(f"✅ Demo Test 1 created successfully! Test ID: {message}")
            return message
        else:
            print(f"❌ Failed to create Demo Test 1: {message}")
            return False
            
    finally:
        db.close()

def create_demo_test_2():
    """Create Demo Test 2: Mathematics & Logic"""
    print("📝 Creating Demo Test 2: Mathematics & Logic...")
    
    questions = [
        {
            'question': 'What is the value of 2³ + 3² - 4?',
            'type': 'multiple_choice',
            'options': [
                '13',
                '15',
                '17',
                '19'
            ],
            'correct_answer': '13',
            'points': 2,
            'difficulty': 'Easy'
        },
        {
            'question': 'If a triangle has sides of length 3, 4, and 5, what type of triangle is it?',
            'type': 'multiple_choice',
            'options': [
                'Equilateral',
                'Isosceles',
                'Right triangle',
                'Obtuse triangle'
            ],
            'correct_answer': 'Right triangle',
            'points': 3,
            'difficulty': 'Medium'
        },
        {
            'question': 'What is the next number in the sequence: 2, 6, 12, 20, 30, ?',
            'type': 'multiple_choice',
            'options': [
                '40',
                '42',
                '44',
                '46'
            ],
            'correct_answer': '42',
            'points': 3,
            'difficulty': 'Hard'
        },
        {
            'question': 'Solve for x: 2x + 5 = 17',
            'type': 'short_answer',
            'correct_answer': 'x = 6',
            'points': 3,
            'difficulty': 'Medium'
        },
        {
            'question': 'Explain the Pythagorean theorem and provide an example of its application.',
            'type': 'essay',
            'correct_answer': 'The Pythagorean theorem states that in a right triangle, the square of the hypotenuse equals the sum of squares of the other two sides (a² + b² = c²). Example: A triangle with sides 3 and 4 has hypotenuse √(3² + 4²) = √25 = 5.',
            'points': 4,
            'difficulty': 'Medium'
        }
    ]
    
    settings = {
        'allow_tab_switch': False,
        'allow_fullscreen_exit': False,
        'require_webcam': True,
        'max_violations': 5,
        'auto_submit': True,
        'record_session': True
    }
    
    # Get admin user
    db = get_db_session()
    try:
        admin = db.query(User).filter(User.role == 'admin').first()
        if not admin:
            print("❌ No admin user found")
            return False
        
        success, message = create_test(
            title="Mathematics & Logic Assessment",
            description="Comprehensive test covering algebra, geometry, and logical reasoning skills",
            duration_minutes=35,
            questions=questions,
            settings=settings,
            created_by=admin.id
        )
        
        if success:
            print(f"✅ Demo Test 2 created successfully! Test ID: {message}")
            return message
        else:
            print(f"❌ Failed to create Demo Test 2: {message}")
            return False
            
    finally:
        db.close()

def create_demo_test_3():
    """Create Demo Test 3: General Knowledge & Critical Thinking"""
    print("📝 Creating Demo Test 3: General Knowledge & Critical Thinking...")
    
    questions = [
        {
            'question': 'Which planet is known as the "Red Planet"?',
            'type': 'multiple_choice',
            'options': [
                'Venus',
                'Mars',
                'Jupiter',
                'Saturn'
            ],
            'correct_answer': 'Mars',
            'points': 1,
            'difficulty': 'Easy'
        },
        {
            'question': 'What is the capital city of Australia?',
            'type': 'multiple_choice',
            'options': [
                'Sydney',
                'Melbourne',
                'Canberra',
                'Perth'
            ],
            'correct_answer': 'Canberra',
            'points': 2,
            'difficulty': 'Medium'
        },
        {
            'question': 'Who wrote the novel "1984"?',
            'type': 'multiple_choice',
            'options': [
                'Aldous Huxley',
                'George Orwell',
                'Ray Bradbury',
                'H.G. Wells'
            ],
            'correct_answer': 'George Orwell',
            'points': 2,
            'difficulty': 'Medium'
        },
        {
            'question': 'What is the chemical symbol for gold?',
            'type': 'short_answer',
            'correct_answer': 'Au',
            'points': 2,
            'difficulty': 'Easy'
        },
        {
            'question': 'Discuss the impact of social media on modern communication. Include both positive and negative aspects.',
            'type': 'essay',
            'correct_answer': 'Social media has revolutionized communication by enabling instant global connectivity and information sharing. Positive impacts include enhanced social connections, business opportunities, and access to information. Negative aspects include privacy concerns, misinformation spread, and potential for addiction or social isolation.',
            'points': 6,
            'difficulty': 'Hard'
        }
    ]
    
    settings = {
        'allow_tab_switch': False,
        'allow_fullscreen_exit': False,
        'require_webcam': True,
        'max_violations': 4,
        'auto_submit': True,
        'record_session': True
    }
    
    # Get admin user
    db = get_db_session()
    try:
        admin = db.query(User).filter(User.role == 'admin').first()
        if not admin:
            print("❌ No admin user found")
            return False
        
        success, message = create_test(
            title="General Knowledge & Critical Thinking",
            description="Test covering general knowledge, current affairs, and critical thinking skills",
            duration_minutes=30,
            questions=questions,
            settings=settings,
            created_by=admin.id
        )
        
        if success:
            print(f"✅ Demo Test 3 created successfully! Test ID: {message}")
            return message
        else:
            print(f"❌ Failed to create Demo Test 3: {message}")
            return False
            
    finally:
        db.close()

def main():
    """Main function to clear and create demo tests"""
    print("🚀 Starting Demo Test Setup...")
    print("=" * 60)
    
    # Step 1: Clear existing tests
    if not clear_existing_tests():
        print("❌ Failed to clear existing tests. Exiting.")
        return
    
    print("\n" + "=" * 60)
    
    # Step 2: Create new demo tests
    test_ids = []
    
    test1_id = create_demo_test_1()
    if test1_id:
        test_ids.append(test1_id)
    
    test2_id = create_demo_test_2()
    if test2_id:
        test_ids.append(test2_id)
    
    test3_id = create_demo_test_3()
    if test3_id:
        test_ids.append(test3_id)
    
    print("\n" + "=" * 60)
    print("📊 SUMMARY")
    print("=" * 60)
    print(f"✅ Successfully created {len(test_ids)} demo tests:")
    
    if test1_id:
        print("   1. Python Programming Fundamentals (45 min)")
    if test2_id:
        print("   2. Mathematics & Logic Assessment (35 min)")
    if test3_id:
        print("   3. General Knowledge & Critical Thinking (30 min)")
    
    print("\n🎯 Next Steps:")
    print("1. Open admin dashboard: http://localhost:8501")
    print("2. Go to Test Management → Assign Tests")
    print("3. Select a test and assign to students")
    print("4. Students can access tests at: http://localhost:8503")
    
    print("\n🎉 Demo tests are ready for assignment!")

if __name__ == "__main__":
    main()
