@echo off
echo 🚀 Launching Proctoring System - Admin and Student Portals
echo ============================================================

echo.
echo 🔧 Starting Admin Dashboard on port 8501...
start "Admin Dashboard" cmd /k "venv\Scripts\python.exe -m streamlit run run_admin.py --server.port 8501"

echo.
echo 📝 Starting Student Portal on port 8502...
start "Student Portal" cmd /k "venv\Scripts\python.exe -m streamlit run run_student.py --server.port 8502"

echo.
echo ✅ Both portals are starting...
echo.
echo 🔗 Access URLs:
echo 👨‍🏫 Admin Dashboard: http://localhost:8501
echo 👨‍🎓 Student Portal:   http://localhost:8502
echo.
echo 🔐 Login Credentials:
echo Admin:   <EMAIL> / admin123
echo Student: <EMAIL> / student123
echo.
echo Press any key to open both URLs in browser...
pause >nul

start http://localhost:8501
start http://localhost:8502

echo.
echo 🎯 Both portals are now running!
echo Close this window when done testing.
pause
